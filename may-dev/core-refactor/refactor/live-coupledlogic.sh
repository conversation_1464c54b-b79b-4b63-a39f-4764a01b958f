#!/bin/bash

# Get today's date for log file name
TODAY=$(date +%Y-%m-%d)
LOGFILE="logging/coupledlogic-${TODAY}.log"

echo "💰 Starting CoupledLogic LIVE Trading (New Algorithm)..."
echo "System: Core-Refactor with Smart Strikes"
echo "Account: CoupledLogic"
echo "Port: 4015"
echo "Mode: REAL MONEY TRADING"
echo "Reference Minute: 50 (XX:50:00)"
echo "Risk: 8% Bulls, 7% Bears"
echo "Logging to: $LOGFILE"
echo ""
echo "⚠️  WARNING: This will place real trades with real money!"
echo "⚠️  New smart strike selection"
echo "⚠️  $2.45 spread limit, $3.40/$6.50 butterfly minimums"
echo "⚠️  Risk: 8% for bulls, 7% for bears"
echo ""
echo "Press Ctrl+C within 5 seconds to cancel..."
echo ""

for i in {5..1}; do
    echo "Starting in $i seconds..."
    sleep 1
done

echo "🚀 LAUNCHING COUPLEDLOGIC LIVE TRADING..."
echo ""

python3 live_trading_app.py \
    --data-port 4015 \
    --reference-minute 50 \
    --account ********* \
    --bull-risk-percentage 8.0 \
    --bear-risk-percentage 7.0 \
    --live