#!/usr/bin/env python3
"""
Console UI Module for Refactored SPX Trading System
Provides real-time market data display and strategy monitoring
"""

import os
import sys
import datetime as dt
from typing import Dict, List, Optional, Any
from collections import deque
from tabulate import tabulate

# Import colorama for colored output
try:
    from colorama import Fore, Back, Style, init
    init(autoreset=True)
    HAS_COLORAMA = True
except ImportError:
    # Fallback without colors
    class MockColor:
        def __getattr__(self, name): return ""
    Fore = Back = Style = MockColor()
    HAS_COLORAMA = False

from strategy_state import StrategyState, DecisionType, BarData, ExecutionStatus
from config import TradingConfig
from live_quote_manager import LiveQuoteManager, StrategyQuote


class ConsoleUI:
    """Real-time console interface for SPX trading system"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.market_data = {}
        self.strategy_state = None
        self.current_time = dt.datetime.now()
        self.recent_logs = deque(maxlen=10)
        self.quote_manager = None
        self.bar_tracker = None
        
        # NEW: Minimal UI mode for critical periods
        self.minimal_ui_mode = False
        self.last_full_display = dt.datetime.now()
        
    def clear_screen(self):
        """Clear the console screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
        
    def add_log(self, message: str, level: str = "INFO"):
        """Add a log message to the recent logs display"""
        timestamp = dt.datetime.now().strftime('%H:%M:%S')
        log_entry = f"{timestamp} - {level} - {message}"
        self.recent_logs.append(log_entry)
        
    def update_market_data(self, symbol: str, data: Dict[str, Any]):
        """Update market data for a symbol"""
        self.market_data[symbol] = data
        
    def update_strategy_state(self, strategy_state: StrategyState):
        """Update the strategy state"""
        self.strategy_state = strategy_state
        
    def set_quote_manager(self, quote_manager: LiveQuoteManager):
        """Set the live quote manager"""
        self.quote_manager = quote_manager
        
    def set_bar_tracker(self, bar_tracker):
        """Set the real-time bar tracker"""
        self.bar_tracker = bar_tracker
        
    def format_price_with_color(self, price: float) -> str:
        """Format price with color coding"""
        if price is None:
            return "N/A"
        if price > 0:
            return f"{Fore.GREEN}{price:+.2f}{Style.RESET_ALL}"
        elif price < 0:
            return f"{Fore.RED}{price:.2f}{Style.RESET_ALL}"
        else:
            return f"{price:.2f}"
            
    def format_percent_with_color(self, percent: float) -> str:
        """Format percentage with color coding"""
        if percent is None:
            return "N/A"
        if percent > 0:
            return f"{Fore.GREEN}{percent:+.2f}%{Style.RESET_ALL}"
        elif percent < 0:
            return f"{Fore.RED}{percent:.2f}%{Style.RESET_ALL}"
        else:
            return f"{percent:.2f}%"
            
    def display_header(self):
        """Display the main header"""
        self.current_time = dt.datetime.now()
        print(f"\n{Fore.CYAN}===== SPX TRADING SYSTEM - NEW ALGORITHM ===== {self.current_time.strftime('%Y-%m-%d %H:%M:%S')} ====={Style.RESET_ALL}")
        
    def display_market_data(self):
        """Display current market data"""
        if not self.market_data:
            return
            
        print(f"\n{Back.CYAN}{Fore.BLACK}{Style.BRIGHT} MARKET DATA {Style.RESET_ALL}")
        
        headers = ['Symbol', 'Last', 'Change', '% Change', 'Bid', 'Ask', 'Updated']
        rows = []
        
        for symbol in ['SPX', 'ES', 'VIX']:
            if symbol in self.market_data:
                data = self.market_data[symbol]
                
                # Calculate time since last update
                updated = "N/A"
                if 'timestamp' in data and data['timestamp']:
                    elapsed = (self.current_time - data['timestamp']).total_seconds()
                    if elapsed < 5:
                        updated = f"{Fore.GREEN}{elapsed:.1f}s{Style.RESET_ALL}"
                    elif elapsed < 30:
                        updated = f"{Fore.YELLOW}{elapsed:.1f}s{Style.RESET_ALL}"
                    else:
                        updated = f"{Fore.RED}{elapsed:.1f}s{Style.RESET_ALL}"
                
                row = [
                    f"{Fore.WHITE}{symbol}{Style.RESET_ALL}",
                    f"{data.get('last', 0):.2f}" if data.get('last') else "N/A",
                    self.format_price_with_color(data.get('change')),
                    self.format_percent_with_color(data.get('change_pct')),
                    f"{data.get('bid', 0):.2f}" if data.get('bid') else "N/A",
                    f"{data.get('ask', 0):.2f}" if data.get('ask') else "N/A",
                    updated
                ]
                rows.append(row)
        
        print(tabulate(rows, headers=headers, tablefmt="simple"))
        
    def display_timing_status(self):
        """Display timing and next action information"""
        print(f"\n{Back.GREEN}{Fore.BLACK}{Style.BRIGHT} TIMING STATUS {Style.RESET_ALL}")
        
        now = dt.datetime.now()
        
        # Current time
        print(f"Current Time: {Fore.YELLOW}{now.strftime('%H:%M:%S')}{Style.RESET_ALL}")
        
        # Real-time bar preview
        if hasattr(self, 'bar_tracker') and self.bar_tracker:
            current_bar = self.bar_tracker.get_current_bar_preview()
            if current_bar:
                print(f"\n{Fore.CYAN}Current 30s Bar (Real-Time):{Style.RESET_ALL}")
                print(f"  O: {current_bar['open']:.2f}  H: {current_bar['high']:.2f}  L: {current_bar['low']:.2f}  C: {current_bar['current']:.2f}")
                print(f"  Ticks: {current_bar['tick_count']}  Duration: {(current_bar['current_time'] - current_bar['start_time']).total_seconds():.0f}s")
        
        # Show trading schedule
        print(f"\n{Fore.CYAN}Trading Schedule:{Style.RESET_ALL}")
        
        # Get reference minute from config
        ref_minute = self.config.timing.reference_minute
        
        times = [
            (f"{ref_minute:02d}:30", "First Bar Analysis"),
            (f"{(ref_minute + 1) % 60:02d}:00", "Second Bar Analysis"), 
            (f"{(ref_minute + 1) % 60:02d}:30", "Third Bar Analysis"),
            (f"{(ref_minute + 1) % 60:02d}:31", "Main Execution"),
            ("58:55", "Overshoot Check"),
            ("58:59", "Cancel Narrow Orders")
        ]
        
        for time_str, action in times:
            # Check if this time has passed today
            target_minute = int(time_str.split(':')[0])
            target_second = int(time_str.split(':')[1])
            
            if (now.minute > target_minute or 
                (now.minute == target_minute and now.second >= target_second)):
                status = f"{Fore.GREEN}✓{Style.RESET_ALL}"
            elif now.minute == target_minute and abs(now.second - target_second) <= 5:
                status = f"{Fore.YELLOW}⏰{Style.RESET_ALL}"
            else:
                status = f"{Fore.WHITE}○{Style.RESET_ALL}"
                
            print(f"  {status} XX:{time_str} - {action}")
        
    def display_bar_data(self):
        """Display collected bar data"""
        if not self.strategy_state:
            return
            
        print(f"\n{Back.BLUE}{Fore.WHITE}{Style.BRIGHT} BAR DATA COLLECTION {Style.RESET_ALL}")
        
        bar_headers = ["Bar", "Timestamp", "Open", "High", "Low", "Close", "Range", "Close %"]
        bar_rows = []
        
        # Display each bar if available
        for bar_num in [1, 2, 3]:
            bar_data = getattr(self.strategy_state, f'bar_{bar_num}', None)
            if bar_data:
                bar_range = bar_data.high - bar_data.low
                close_pct = ((bar_data.close - bar_data.low) / (bar_data.high - bar_data.low)) * 100 if bar_range > 0 else 0
                
                # Color code the close percentage
                if close_pct <= 25:
                    close_color = Fore.RED
                elif close_pct >= 75:
                    close_color = Fore.GREEN
                else:
                    close_color = Fore.YELLOW
                    
                row = [
                    f"Bar {bar_num}",
                    bar_data.timestamp.strftime('%H:%M:%S'),
                    f"{bar_data.open:.2f}",
                    f"{bar_data.high:.2f}",
                    f"{bar_data.low:.2f}",
                    f"{bar_data.close:.2f}",
                    f"{bar_range:.2f}",
                    f"{close_color}{close_pct:.1f}%{Style.RESET_ALL}"
                ]
                bar_rows.append(row)
        
        if bar_rows:
            print(tabulate(bar_rows, headers=bar_headers, tablefmt="simple"))
        else:
            print("No bar data collected yet")
            
        # Display SPAN calculations
        if self.strategy_state and hasattr(self.strategy_state, 'span_data'):
            span_data = self.strategy_state.span_data
            if span_data:
                print(f"\n{Back.YELLOW}{Fore.BLACK}{Style.BRIGHT} SPAN CALCULATIONS {Style.RESET_ALL}")
                span_rows = []
                
                if span_data.span1 is not None:
                    span_rows.append(["SPAN1 (First)", f"{span_data.span1:.2f}"])
                if span_data.span2 is not None:
                    span_rows.append(["SPAN2 (Max/Min 1+2)", f"{span_data.span2:.2f}"])
                if span_data.span3 is not None:
                    # Color code SPAN3 based on narrow threshold
                    span3_color = Fore.RED if span_data.span3 < self.config.strategy.narrow_range_threshold else Fore.GREEN
                    span_rows.append(["SPAN3 (Max/Min All)", f"{span3_color}{span_data.span3:.2f}{Style.RESET_ALL}"])
                
                if span_rows:
                    print(tabulate(span_rows, headers=["SPAN", "Value"], tablefmt="simple"))
        
    def display_strategy_decision(self):
        """Display current strategy decision and logic"""
        if not self.strategy_state:
            return
            
        print(f"\n{Back.GREEN}{Fore.BLACK}{Style.BRIGHT} STRATEGY DECISION {Style.RESET_ALL}")
        
        decision_data = []
        
        # Current decision
        decision = self.strategy_state.current_decision
        if decision == DecisionType.BULL:
            decision_display = f"{Fore.GREEN}BULL SPREAD{Style.RESET_ALL}"
        elif decision == DecisionType.BEAR:
            decision_display = f"{Fore.RED}BEAR SPREAD{Style.RESET_ALL}"
        elif decision == DecisionType.BF_BULL:
            decision_display = f"{Fore.GREEN}{Back.BLUE}BF-BULL{Style.RESET_ALL}"
        elif decision == DecisionType.BF_BEAR:
            decision_display = f"{Fore.RED}{Back.BLUE}BF-BEAR{Style.RESET_ALL}"
        elif decision == DecisionType.NARROW_BULL:
            decision_display = f"{Fore.GREEN}{Back.YELLOW}NARROW BULL{Style.RESET_ALL}"
        elif decision == DecisionType.NARROW_BEAR:
            decision_display = f"{Fore.RED}{Back.YELLOW}NARROW BEAR{Style.RESET_ALL}"
        elif decision == DecisionType.IRON_BUTTERFLY:
            decision_display = f"{Fore.YELLOW}IRON BUTTERFLY{Style.RESET_ALL}"
        elif decision == DecisionType.WIDE_IRON_BUTTERFLY:
            decision_display = f"{Fore.CYAN}WIDE IRON BUTTERFLY{Style.RESET_ALL}"
        else:
            decision_display = f"{Fore.MAGENTA}{decision.value.upper()}{Style.RESET_ALL}"
            
        decision_data.append(["Current Decision", decision_display])
        
        # Execution status
        if self.strategy_state.is_trade_executed():
            trade_exec = self.strategy_state.trade_execution
            exec_time = trade_exec.execution_time if trade_exec else None
            time_str = exec_time.strftime('%H:%M:%S') if exec_time else "Unknown"
            decision_data.append(["Main Order", f"{Fore.GREEN}EXECUTED at {time_str}{Style.RESET_ALL}"])
        else:
            decision_data.append(["Main Order", "Pending"])
            
        # Retry attempts
        if self.strategy_state.trade_execution and self.strategy_state.trade_execution.retry_count > 0:
            decision_data.append(["Retry Attempts", f"{self.strategy_state.trade_execution.retry_count}"])
            
        # Dual trade intention
        if hasattr(self.strategy_state, 'dual_trade_intended') and self.strategy_state.dual_trade_intended:
            dual_info = f"{Fore.YELLOW}Iron Butterfly @ {self.strategy_state.dual_trade_strike}{Style.RESET_ALL}"
            decision_data.append(["Dual Trade", dual_info])
            
        print(tabulate(decision_data, headers=["Item", "Status"], tablefmt="simple"))
        
    def display_execution_status(self):
        """Display order execution status"""
        if not self.strategy_state:
            return
            
        print(f"\n{Back.MAGENTA}{Fore.WHITE}{Style.BRIGHT} EXECUTION STATUS {Style.RESET_ALL}")
        
        exec_data = []
        
        # Main order status
        if self.strategy_state.is_trade_executed():
            exec_data.append(["Main Order", f"{Fore.GREEN}✓ EXECUTED{Style.RESET_ALL}"])
        elif self.strategy_state.trade_execution:
            if self.strategy_state.trade_execution.execution_status == ExecutionStatus.PENDING:
                exec_data.append(["Main Order", f"{Fore.YELLOW}⏳ PENDING{Style.RESET_ALL}"])
            else:
                exec_data.append(["Main Order", f"{Fore.RED}{self.strategy_state.trade_execution.execution_status.value.upper()}{Style.RESET_ALL}"])
        else:
            exec_data.append(["Main Order", "Not Started"])
            
        # Wide fallback status
        if self.strategy_state.wide_iron_butterfly_filled:
            exec_data.append(["Wide Fallback", f"{Fore.GREEN}✓ EXECUTED{Style.RESET_ALL}"])
        elif self.strategy_state.wide_iron_butterfly_attempted:
            exec_data.append(["Wide Fallback", f"{Fore.YELLOW}⏳ ATTEMPTED{Style.RESET_ALL}"])
        else:
            exec_data.append(["Wide Fallback", "Not Attempted"])
            
        # Overshoot status
        if self.strategy_state.overshoot_executions:
            exec_data.append(["Overshoot Reversal", f"{Fore.GREEN}✓ EXECUTED ({len(self.strategy_state.overshoot_executions)}){Style.RESET_ALL}"])
        else:
            exec_data.append(["Overshoot Reversal", "Pending"])
            
        print(tabulate(exec_data, headers=["Order Type", "Status"], tablefmt="simple"))
        
    def display_recent_logs(self):
        """Display recent log messages"""
        print(f"\n{Back.BLACK}{Fore.WHITE}{Style.BRIGHT} RECENT LOGS {Style.RESET_ALL}")
        
        if self.recent_logs:
            for log in list(self.recent_logs)[-5:]:  # Show last 5 logs
                # Color based on log level
                if "ERROR" in log:
                    print(f"{Fore.RED}{log}{Style.RESET_ALL}")
                elif "WARNING" in log:
                    print(f"{Fore.YELLOW}{log}{Style.RESET_ALL}")
                elif "INFO" in log:
                    print(f"{Fore.CYAN}{log}{Style.RESET_ALL}")
                elif "DEBUG" in log:
                    print(f"{Fore.GREEN}{log}{Style.RESET_ALL}")
                else:
                    print(log)
        else:
            print("No recent logs")
            
    def display_live_quotes(self):
        """Display live strategy quotes"""
        if not self.quote_manager:
            return
            
        print(f"\n{Back.MAGENTA}{Fore.WHITE}{Style.BRIGHT} LIVE STRATEGY QUOTES {Style.RESET_ALL}")
        
        strategy_quotes = self.quote_manager.get_all_strategy_quotes()
        
        if not strategy_quotes:
            print("No live quotes available")
            return
            
        # Display Bull and Bear spreads side by side
        print(f"\n{Fore.CYAN}Bull & Bear Spreads:{Style.RESET_ALL}")
        
        bull_strategies = ['bull_one_better', 'bull_nearest', 'bull_next_worst']
        bear_strategies = ['bear_one_better', 'bear_nearest', 'bear_next_worst']
        
        combined_headers = ["Bull Strategy", "Strikes", "Net Mid", "Bear Strategy", "Strikes", "Net Mid"]
        combined_rows = []
        
        for i in range(3):
            row = []
            
            # Bull side
            bull_name = bull_strategies[i]
            if bull_name in strategy_quotes:
                bull_quote = strategy_quotes[bull_name]
                bull_strikes = "/".join([f"{s:.0f}" for s in bull_quote.strikes])
                bull_mid = f"{Fore.RED}{bull_quote.net_mid:.2f}{Style.RESET_ALL}" if bull_quote.net_mid else "N/A"
                row.extend([bull_name.replace('bull_', '').replace('_', ' ').title(), bull_strikes, bull_mid])
            else:
                row.extend(["N/A", "N/A", "N/A"])
            
            # Bear side  
            bear_name = bear_strategies[i]
            if bear_name in strategy_quotes:
                bear_quote = strategy_quotes[bear_name]
                bear_strikes = "/".join([f"{s:.0f}" for s in bear_quote.strikes])
                bear_mid = f"{Fore.RED}{bear_quote.net_mid:.2f}{Style.RESET_ALL}" if bear_quote.net_mid else "N/A"
                row.extend([bear_name.replace('bear_', '').replace('_', ' ').title(), bear_strikes, bear_mid])
            else:
                row.extend(["N/A", "N/A", "N/A"])
                
            combined_rows.append(row)
            
        print(tabulate(combined_rows, headers=combined_headers, tablefmt="simple"))
        
        # Iron Butterflies - compact display
        print(f"\n{Fore.CYAN}Iron Butterflies:{Style.RESET_ALL}")
        
        butterfly_strategies = ['wide_butterfly_atm', 'narrow_butterfly_atm', 'wide_butterfly_up', 'narrow_butterfly_up', 'wide_butterfly_down', 'narrow_butterfly_down']
        headers = ["Strategy", "Center", "Wings", "Net Mid", "Status"]
        rows = []
        
        for strategy_name in butterfly_strategies:
            if strategy_name in strategy_quotes:
                quote = strategy_quotes[strategy_name]
                
                # Extract center and wing info
                strikes = quote.strikes
                center = strikes[1]  # Center strike (both call and put)
                wing_width = abs(strikes[0] - center)
                
                # Format name
                if 'wide' in strategy_name:
                    name = "Wide"
                else:
                    name = "Narrow"
                    
                if 'atm' in strategy_name:
                    name += " ATM"
                elif 'up' in strategy_name:
                    name += " Up"
                else:
                    name += " Down"
                
                # Format pricing
                if quote.is_valid and quote.net_mid is not None:
                    mid_color = Fore.GREEN if quote.net_mid > 0 else Fore.RED
                    net_mid_str = f"{mid_color}{quote.net_mid:.2f}{Style.RESET_ALL}"
                    status = f"{Fore.GREEN}✓{Style.RESET_ALL}"
                else:
                    net_mid_str = "N/A"
                    status = f"{Fore.RED}✗{Style.RESET_ALL}"
                    
                rows.append([name, f"{center:.0f}", f"±{wing_width:.0f}", net_mid_str, status])
                
        if rows:
            print(tabulate(rows, headers=headers, tablefmt="simple"))
            
        # Overshoot strategies - simple display
        print(f"\n{Fore.CYAN}Overshoot:{Style.RESET_ALL}")
        overshoot_strategies = ['overshoot_bull', 'overshoot_bear']
        headers = ["Strategy", "Strikes", "Net Mid"]
        rows = []
        
        for strategy_name in overshoot_strategies:
            if strategy_name in strategy_quotes:
                quote = strategy_quotes[strategy_name]
                strikes_str = "/".join([f"{s:.0f}" for s in quote.strikes])
                mid_str = f"{Fore.RED}{quote.net_mid:.2f}{Style.RESET_ALL}" if quote.net_mid else "N/A"
                display_name = strategy_name.replace('overshoot_', '').title()
                rows.append([display_name, strikes_str, mid_str])
                
        if rows:
            print(tabulate(rows, headers=headers, tablefmt="simple"))
                
    def display_individual_options(self):
        """Display individual option quotes around ATM"""
        if not self.quote_manager or not self.quote_manager.current_spx_price:
            return
            
        print(f"\n{Back.YELLOW}{Fore.BLACK}{Style.BRIGHT} INDIVIDUAL OPTION QUOTES {Style.RESET_ALL}")
        
        spx_price = self.quote_manager.current_spx_price
        atm_strike = round(spx_price / 5) * 5
        
        # Show strikes around ATM
        strikes_to_show = [atm_strike - 10, atm_strike - 5, atm_strike, atm_strike + 5, atm_strike + 10]
        
        headers = ["Strike", "Call Bid", "Call Ask", "Call Last", "|", "Put Bid", "Put Ask", "Put Last"]
        rows = []
        
        for strike in strikes_to_show:
            row = [f"{strike:.0f}"]
            
            # Call data
            call_quote = self.quote_manager.get_individual_option_quote(strike, 'C')
            if call_quote:
                row.extend([
                    f"{call_quote['bid']:.2f}" if call_quote['bid'] else "N/A",
                    f"{call_quote['ask']:.2f}" if call_quote['ask'] else "N/A", 
                    f"{call_quote['last']:.2f}" if call_quote['last'] else "N/A"
                ])
            else:
                row.extend(["N/A", "N/A", "N/A"])
                
            row.append("|")  # Separator
            
            # Put data
            put_quote = self.quote_manager.get_individual_option_quote(strike, 'P')
            if put_quote:
                row.extend([
                    f"{put_quote['bid']:.2f}" if put_quote['bid'] else "N/A",
                    f"{put_quote['ask']:.2f}" if put_quote['ask'] else "N/A",
                    f"{put_quote['last']:.2f}" if put_quote['last'] else "N/A"
                ])
            else:
                row.extend(["N/A", "N/A", "N/A"])
                
            # Highlight ATM row
            if strike == atm_strike:
                row = [f"{Back.WHITE}{Fore.BLACK}{cell}{Style.RESET_ALL}" if cell != "|" else cell for cell in row]
                
            rows.append(row)
            
        print(tabulate(rows, headers=headers, tablefmt="simple"))
        print(f"Current SPX: {Fore.YELLOW}{spx_price:.2f}{Style.RESET_ALL}")

    def display_database_status(self, db_connected: bool, db_type: str = None):
        """Display database connection status"""
        print(f"\n{Back.BLUE}{Fore.WHITE}{Style.BRIGHT} DATABASE STATUS {Style.RESET_ALL}")
        
        if db_connected:
            print(f"{Fore.GREEN}✓ Connected to {db_type or 'Database'}{Style.RESET_ALL}")
            print("Trades will be automatically saved")
        else:
            print(f"{Fore.RED}✗ Database not connected{Style.RESET_ALL}")
            print("Trades will NOT be saved")
            
    def is_critical_trading_period(self) -> bool:
        """Check if we're in the critical trading period (15:49:00 - 15:52:00)"""
        now = dt.datetime.now()
        start_minute = (self.config.timing.reference_minute - 1) % 60  # 15:49:00
        end_minute = (self.config.timing.reference_minute + 2) % 60    # 15:52:00
        
        return start_minute <= now.minute <= end_minute

    def should_use_minimal_ui(self) -> bool:
        """Determine if we should use minimal UI"""
        # Always use minimal UI during critical period
        if self.is_critical_trading_period():
            return True
        
        # Or if explicitly enabled
        return getattr(self, 'minimal_ui_mode', False)

    def display_minimal_ui(self, db_connected: bool = False, db_type: str = None):
        """Ultra-fast minimal UI for critical trading periods"""
        self.clear_screen()
        
        now = dt.datetime.now()
        
        # Header - single line
        print(f"{Back.GREEN}{Fore.BLACK} SPX LIVE TRADING - MINIMAL MODE {Style.RESET_ALL} {now.strftime('%H:%M:%S')}")
        
        # Essential info only - single line each
        print(f"\n{Fore.YELLOW}SPX:{Style.RESET_ALL} ${self.market_data.get('SPX', {}).get('last', 'N/A')}")
        print(f"{Fore.GREEN}DB:{Style.RESET_ALL} {'✓' if db_connected else '✗'} | {Fore.CYAN}IB:{Style.RESET_ALL} ✓ Connected")
        
        # Strategy status - one line
        if self.strategy_state:
            decision = self.strategy_state.current_decision.value.upper()
            executed = "✓ EXECUTED" if self.strategy_state.is_trade_executed() else "PENDING"
            print(f"{Fore.MAGENTA}Strategy:{Style.RESET_ALL} {decision} | {Fore.YELLOW}Status:{Style.RESET_ALL} {executed}")
            
            # Bar collection status - one line
            bars = []
            if self.strategy_state.first_bar: bars.append("1")
            if self.strategy_state.second_bar: bars.append("2") 
            if self.strategy_state.third_bar: bars.append("3")
            bars_str = ",".join(bars) if bars else "None"
            print(f"{Fore.CYAN}Bars:{Style.RESET_ALL} [{bars_str}]")
        
        # Timing status - critical windows only
        ref_min = self.config.timing.reference_minute
        critical_times = [
            (f"{ref_min:02d}:30", "Bar1"),
            (f"{(ref_min+1)%60:02d}:00", "Bar2"), 
            (f"{(ref_min+1)%60:02d}:30", "Bar3"),
            (f"{(ref_min+1)%60:02d}:31", "EXEC")
        ]
        
        timing_status = []
        for time_str, label in critical_times:
            target_min = int(time_str.split(':')[0])
            target_sec = int(time_str.split(':')[1])
            
            if (now.minute > target_min or (now.minute == target_min and now.second >= target_sec)):
                timing_status.append(f"{Fore.GREEN}{label}✓{Style.RESET_ALL}")
            elif now.minute == target_min and abs(now.second - target_sec) <= 5:
                timing_status.append(f"{Fore.YELLOW}{label}⏰{Style.RESET_ALL}")
            else:
                timing_status.append(f"{label}○")
        
        print(f"{Fore.YELLOW}Timing:{Style.RESET_ALL} {' '.join(timing_status)}")
        
        # Last log entry only
        if self.recent_logs:
            last_log = list(self.recent_logs)[-1]
            if len(last_log) > 80:
                last_log = last_log[:77] + "..."
            print(f"{Fore.CYAN}Last:{Style.RESET_ALL} {last_log}")
        
        print(f"\n{Fore.RED}MINIMAL MODE{Style.RESET_ALL} - Press Ctrl+C to exit")

    def display_all(self, db_connected: bool = False, db_type: str = None):
        """Display the complete UI or minimal UI based on trading period"""
        
        # Use minimal UI during critical periods or if explicitly enabled
        if self.should_use_minimal_ui():
            self.display_minimal_ui(db_connected, db_type)
            return
        
        # Full UI for non-critical periods (existing code)
        self.clear_screen()
        
        # Main sections
        self.display_header()
        self.display_market_data()
        
        # Show full quotes only every 30 seconds outside critical period
        now = dt.datetime.now()
        if (now - self.last_full_display).total_seconds() > 30:
            self.display_live_quotes()
            self.last_full_display = now
        
        self.display_timing_status()
        self.display_bar_data()
        self.display_strategy_decision()
        self.display_execution_status()
        self.display_database_status(db_connected, db_type)
        self.display_recent_logs()
        
        # Footer
        print(f"\n{Fore.CYAN}Press Ctrl+C to exit{Style.RESET_ALL}")

    def enable_minimal_mode(self):
        """Manually enable minimal UI mode"""
        self.minimal_ui_mode = True

    def disable_minimal_mode(self):
        """Manually disable minimal UI mode"""
        self.minimal_ui_mode = False

    def log_info(self, message: str):
        """Add an info log"""
        self.add_log(message, "INFO")
        
    def log_warning(self, message: str):
        """Add a warning log"""
        self.add_log(message, "WARNING")
        
    def log_error(self, message: str):
        """Add an error log"""
        self.add_log(message, "ERROR")
        
    def log_debug(self, message: str):
        """Add a debug log"""
        self.add_log(message, "DEBUG") 