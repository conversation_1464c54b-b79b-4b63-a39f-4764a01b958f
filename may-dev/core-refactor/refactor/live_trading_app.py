#!/usr/bin/env python3
"""
Live Trading Application - Refactored SPX Trading System with Console UI
Combines the new algorithm with real-time market data and console interface
"""

import logging
import datetime as dt
import time
import sys
import os
import argparse
from typing import Optional, Dict, List
from ib_insync import IB, Stock, Option, Index, Future

# Add the parent directory to path to access existing database modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import existing database components
from database.db_factory import get_database_handler

# Import our refactored modules
from config import TradingConfig
from strategy_state import StrategyState, BarData
from quote_engine import OptionQuoteEngine
from execution_engine import OptionExecutionEngine
from spx_strategy import SPXTradingStrategy
from console_ui import ConsoleUI
from live_quote_manager import LiveQuoteManager

# Set up comprehensive file logging
def setup_logging(data_port=None):
    """Set up file logging with date-based organization and account-specific naming"""
    import os
    from pathlib import Path
    
    # Create logging directory
    log_dir = Path('logging')
    log_dir.mkdir(exist_ok=True)
    
    # Map ports to account names
    port_to_account = {
        4015: 'coupledlogic',
        4013: 'phaedrustrader',
        4017: 'jerryzhang'
    }
    account_name = port_to_account.get(data_port, 'trading')
    
    # Generate log filename with today's date and account name
    today = dt.datetime.now().strftime('%Y-%m-%d')
    log_file = log_dir / f'{account_name}_{today}.log'
    
    # Configure logging with both file and console handlers
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # Keep console output too
        ]
    )
    
    # Create a separate detailed logger for trading actions
    detailed_logger = logging.getLogger('trading_detailed')
    detailed_handler = logging.FileHandler(log_dir / f'{account_name}_detailed_{today}.log')
    detailed_handler.setFormatter(
        logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    )
    detailed_logger.addHandler(detailed_handler)
    detailed_logger.setLevel(logging.DEBUG)
    
    print(f"Logging setup complete - files in {log_dir}/ (account: {account_name})")
    return logging.getLogger(__name__)

# Initial logger (will be reconfigured in main with port info)
logger = logging.getLogger(__name__)


class RealTimeBarTracker:
    """Tracks real OHLC bars from live SPX price data"""
    
    def __init__(self):
        self.price_history: List[Dict] = []  # List of {timestamp, price} dicts
        self.current_bar_start: Optional[dt.datetime] = None
        self.bar_duration_seconds = 30  # 30-second bars
        
    def add_price_tick(self, price: float, timestamp: dt.datetime = None):
        """Add a new price tick to the tracker"""
        if timestamp is None:
            timestamp = dt.datetime.now()
            
        self.price_history.append({
            'timestamp': timestamp,
            'price': price
        })
        
        # Keep only last 5 minutes of data (for efficiency)
        cutoff_time = timestamp - dt.timedelta(minutes=5)
        self.price_history = [
            tick for tick in self.price_history 
            if tick['timestamp'] > cutoff_time
        ]
        
    def get_bar_ending_at(self, end_time: dt.datetime) -> Optional[BarData]:
        """Get the 30-second bar that ends at the specified time"""
        bar_start = end_time - dt.timedelta(seconds=self.bar_duration_seconds)
        
        # Find all ticks within this 30-second window
        bar_ticks = [
            tick for tick in self.price_history
            if bar_start <= tick['timestamp'] <= end_time
        ]
        
        if not bar_ticks:
            logger.warning(f"No price data for bar ending at {end_time}")
            return None
            
        # Calculate OHLC from the ticks
        prices = [tick['price'] for tick in bar_ticks]
        
        return BarData(
            timestamp=end_time,
            open=bar_ticks[0]['price'],  # First tick in window
            high=max(prices),
            low=min(prices),
            close=bar_ticks[-1]['price'],  # Last tick in window
            name=f"real_bar_{end_time.strftime('%H:%M:%S')}"
        )
        
    def get_current_bar_preview(self) -> Optional[Dict]:
        """Get a preview of the current forming bar"""
        if not self.price_history:
            return None
            
        now = dt.datetime.now()
        bar_start = now - dt.timedelta(seconds=self.bar_duration_seconds)
        
        current_ticks = [
            tick for tick in self.price_history
            if tick['timestamp'] > bar_start
        ]
        
        if not current_ticks:
            return None
            
        prices = [tick['price'] for tick in current_ticks]
        
        return {
            'open': current_ticks[0]['price'],
            'high': max(prices),
            'low': min(prices),
            'current': current_ticks[-1]['price'],
            'tick_count': len(current_ticks),
            'start_time': current_ticks[0]['timestamp'],
            'current_time': current_ticks[-1]['timestamp']
        }


class LiveTradingApp:
    """Main application for live SPX trading with console UI"""
    
    def __init__(self, config: TradingConfig, simulation_mode: bool = False, simulation_scenario: str = None):
        self.config = config
        self.console_ui = ConsoleUI(config)
        
        # Simulation mode settings
        self.simulation_mode = simulation_mode
        self.simulation_scenario = simulation_scenario
        self.csv_adapter = None
        
        # IB connections (or mock connections in simulation)
        self.ib_data = None
        self.ib_trading = None
        
        # Trading components
        self.db_handler = None
        self.quote_engine = None
        self.execution_engine = None
        self.strategy = None
        self.live_quote_manager = None
        
        # Real bar tracking
        self.bar_tracker = RealTimeBarTracker()
        
        # Market data storage
        self.market_data = {
            'SPX': {'last': None, 'bid': None, 'ask': None, 'change': None, 'change_pct': None, 'timestamp': None},
            'ES': {'last': None, 'bid': None, 'ask': None, 'change': None, 'change_pct': None, 'timestamp': None},
            'VIX': {'last': None, 'bid': None, 'ask': None, 'change': None, 'change_pct': None, 'timestamp': None}
        }
        
        # Contracts
        self.contracts = {}
        self.tickers = {}
        
    def connect_to_ib(self, data_host: str = '127.0.0.1', data_port: int = 4001, 
                     trading_host: str = '127.0.0.1', trading_port: int = 4001):
        """Connect to Interactive Brokers or setup simulation"""
        try:
            if self.simulation_mode:
                # Import simulation components
                from csv_data_adapter import CSVDataAdapter, MockIBConnection
                
                # Setup CSV adapter
                self.csv_adapter = CSVDataAdapter()
                
                if self.simulation_scenario:
                    if not self.csv_adapter.load_scenario(self.simulation_scenario):
                        self.console_ui.log_error(f"Failed to load simulation scenario: {self.simulation_scenario}")
                        return False
                    self.console_ui.log_info(f"📊 Simulation mode: Loaded scenario {self.simulation_scenario}")
                else:
                    self.console_ui.log_info("📊 Simulation mode: Ready for CSV data")
                
                # Create mock connections
                self.ib_data = MockIBConnection(self.csv_adapter)
                self.ib_trading = MockIBConnection(self.csv_adapter)
                
                return True
            else:
                # Live trading mode - connect to real IB
                self.ib_data = IB()
                self.ib_data.connect(data_host, data_port, clientId=1)
                self.console_ui.log_info(f"Connected to IB market data at {data_host}:{data_port}")
                
                # Trading connection (can be same as data)
                if trading_host == data_host and trading_port == data_port:
                    self.ib_trading = self.ib_data  # Use same connection
                else:
                    self.ib_trading = IB()
                    self.ib_trading.connect(trading_host, trading_port, clientId=2)
                    self.console_ui.log_info(f"Connected to IB trading at {trading_host}:{trading_port}")
                
                # Request delayed data
                self.ib_data.reqMarketDataType(1)
                
                return True
            
        except Exception as e:
            self.console_ui.log_error(f"Failed to connect: {str(e)}")
            return False
            
    def setup_contracts(self):
        """Set up market data contracts"""
        try:
            # Create contracts
            self.contracts = {
                'SPX': Index('SPX', 'CBOE', 'USD'),
                'ES': Future('ES', '202506', 'CME', currency='USD'),  # E-mini S&P 500 June 2025
                'VIX': Index('VIX', 'CBOE', 'USD')
            }
            
            # Qualify contracts
            for symbol, contract in self.contracts.items():
                qualified = self.ib_data.qualifyContracts(contract)
                if qualified:
                    self.contracts[symbol] = qualified[0]
                    self.console_ui.log_info(f"Qualified contract for {symbol}")
                else:
                    self.console_ui.log_warning(f"Could not qualify contract for {symbol}")
                    
            return True
            
        except Exception as e:
            self.console_ui.log_error(f"Failed to setup contracts: {str(e)}")
            return False
            
    def setup_market_data(self):
        """Subscribe to market data"""
        try:
            for symbol, contract in self.contracts.items():
                ticker = self.ib_data.reqMktData(contract, '', False, False)
                self.tickers[symbol] = ticker
                self.console_ui.log_info(f"Subscribed to market data for {symbol}")
                
            # Wait for initial data
            time.sleep(3)
            return True
            
        except Exception as e:
            self.console_ui.log_error(f"Failed to setup market data: {str(e)}")
            return False
            
    def update_market_data(self):
        """Update market data from IB or simulation"""
        try:
            if self.simulation_mode and self.csv_adapter:
                # In simulation mode, update market data from CSV adapter
                current_time = dt.datetime.now()
                self.csv_adapter.update_market_data_for_time(current_time)
                
                for symbol in ['SPX', 'ES', 'VIX']:
                    market_data = self.csv_adapter.get_market_data(symbol)
                    if market_data.get('last'):
                        # Calculate change if we have previous close
                        change = None
                        change_pct = None
                        last_price = market_data['last']
                        previous_close = last_price - 5.0  # Mock previous close
                        
                        if previous_close > 0:
                            change = last_price - previous_close
                            change_pct = (change / previous_close) * 100
                        
                        # Update our market data
                        self.market_data[symbol].update({
                            'last': last_price,
                            'bid': market_data.get('bid'),
                            'ask': market_data.get('ask'),
                            'change': change,
                            'change_pct': change_pct,
                            'timestamp': current_time
                        })
                        
                        # Update console UI
                        self.console_ui.update_market_data(symbol, self.market_data[symbol])
                        
                        # Update live quote manager with SPX price
                        if symbol == 'SPX' and self.live_quote_manager:
                            self.live_quote_manager.update_spx_price(last_price)
                        
                        # Track SPX price ticks for real bars
                        if symbol == 'SPX':
                            self.bar_tracker.add_price_tick(last_price)
            else:
                # Live mode - use real IB tickers
                for symbol, ticker in self.tickers.items():
                    if ticker.last and ticker.last > 0:
                        # Calculate change if we have previous close
                        change = None
                        change_pct = None
                        if ticker.close and ticker.close > 0:
                            change = ticker.last - ticker.close
                            change_pct = (change / ticker.close) * 100
                        
                        # Update our market data
                        self.market_data[symbol].update({
                            'last': ticker.last,
                            'bid': ticker.bid if ticker.bid and ticker.bid > 0 else None,
                            'ask': ticker.ask if ticker.ask and ticker.ask > 0 else None,
                            'change': change,
                            'change_pct': change_pct,
                            'timestamp': dt.datetime.now()
                        })
                        
                        # Update console UI
                        self.console_ui.update_market_data(symbol, self.market_data[symbol])
                        
                        # Update live quote manager with SPX price
                        if symbol == 'SPX' and self.live_quote_manager:
                            self.live_quote_manager.update_spx_price(ticker.last)
                        
                        # Track SPX price ticks for real bars
                        if symbol == 'SPX':
                            self.bar_tracker.add_price_tick(ticker.last)
                    
        except Exception as e:
            self.console_ui.log_error(f"Error updating market data: {str(e)}")
            
    def initialize_database(self):
        """Initialize database connection"""
        try:
            self.db_handler = get_database_handler()
            self.console_ui.log_info("Remote SQL Server database initialized")
            return True
        except Exception as e:
            self.console_ui.log_error(f"Database initialization failed: {str(e)}")
            return False
            
    def initialize_trading_components(self):
        """Initialize quote engine, execution engine, and strategy"""
        try:
            # Initialize database handler
            self.db_handler = get_database_handler()
            
            # Initialize quote engine
            self.quote_engine = OptionQuoteEngine(self.ib_data)
            self.console_ui.log_info("Quote engine initialized")
            
            # Initialize execution engine
            self.execution_engine = OptionExecutionEngine(self.ib_trading, self.db_handler)
            self.console_ui.log_info("Execution engine initialized")
            
            # Initialize strategy
            self.strategy = SPXTradingStrategy(self.quote_engine, self.execution_engine)
            
            # Set price provider for live market data
            self.strategy.set_price_provider(self.get_current_spx_price)
            
            self.console_ui.log_info("SPX strategy initialized")
            
            # Log timing schedule for debugging
            timing_schedule = self.config.timing.get_timing_schedule()
            self.console_ui.log_info("=== TIMING SCHEDULE ===")
            for event_name, timing in timing_schedule.items():
                self.console_ui.log_info(f"{event_name}: {timing['minute']:02d}:{timing['second']:02d}")
            self.console_ui.log_info("=======================")
            
            # Initialize live quote manager
            self.live_quote_manager = LiveQuoteManager(self.ib_data)
            self.console_ui.log_info("Live quote manager initialized")
            
            # Set quote manager and bar tracker in console UI
            self.console_ui.set_quote_manager(self.live_quote_manager)
            self.console_ui.set_bar_tracker(self.bar_tracker)
            self.console_ui.log_info("Console UI components configured")
            
            # Start the trading session
            self.strategy.start_session()
            
            return True
            
        except Exception as e:
            self.console_ui.log_error(f"Failed to initialize trading components: {str(e)}")
            return False
            
    def get_current_spx_price(self) -> Optional[float]:
        """Get current SPX price"""
        return self.market_data['SPX'].get('last')
        
    def get_real_bar_data(self, end_time: dt.datetime) -> Optional[BarData]:
        """Get real bar data from price history"""
        return self.bar_tracker.get_bar_ending_at(end_time)
        
    def check_algorithm_timing(self):
        """Check if it's time to execute algorithm steps"""
        now = dt.datetime.now()
        current_minute = now.minute
        current_second = now.second
        
        # Algorithm timing checks
        if (current_minute == self.config.timing.reference_minute and 
            current_second == 30):
            self.console_ui.log_info("🕐 First bar analysis time - using REAL bar data")
            
            previous_bar_end_time = now - dt.timedelta(seconds=30)
            previous_bar_data = self.get_real_bar_data(previous_bar_end_time)
            previous_close = None
            
            if previous_bar_data:
                previous_close = previous_bar_data.close
                self.console_ui.log_info(f"📈 Previous bar (15:49:30) close: ${previous_close:.2f} (will be Bar 1 open)")
            else:
                previous_close = self.get_current_spx_price()
                self.console_ui.log_warning(f"⚠️ No 15:49:30 bar data, using current price: ${previous_close:.2f}")
            
            bar_data = self.get_real_bar_data(now)
            if bar_data:
                self.console_ui.log_info(f"📊 Real Bar 1: O:{bar_data.open:.2f} H:{bar_data.high:.2f} L:{bar_data.low:.2f} C:{bar_data.close:.2f}")
                self.strategy.process_bar_data(bar_data, 1, previous_close)
                self.strategy.check_analysis_timing(now)
            else:
                self.console_ui.log_warning("No real bar data available for first bar analysis")
                
        elif (current_minute == (self.config.timing.reference_minute + 1) % 60 and 
              current_second == 0):
            self.console_ui.log_info("🕐 Second bar analysis time - using REAL bar data")
            bar_data = self.get_real_bar_data(now)
            if bar_data:
                self.console_ui.log_info(f"📊 Real Bar 2: O:{bar_data.open:.2f} H:{bar_data.high:.2f} L:{bar_data.low:.2f} C:{bar_data.close:.2f}")
                self.strategy.process_bar_data(bar_data, 2)
                self.strategy.check_analysis_timing(now)
            else:
                self.console_ui.log_warning("No real bar data available for second bar analysis")
                
        elif (current_minute == (self.config.timing.reference_minute + 1) % 60 and 
              current_second == 30): 
            self.console_ui.log_info("🕐 Third bar analysis time - using REAL bar data")
            bar_data = self.get_real_bar_data(now)
            if bar_data:
                self.console_ui.log_info(f"📊 Real Bar 3: O:{bar_data.open:.2f} H:{bar_data.high:.2f} L:{bar_data.low:.2f} C:{bar_data.close:.2f}")
                self.strategy.process_bar_data(bar_data, 3)
                self.strategy.check_analysis_timing(now)
            else:
                self.console_ui.log_warning("No real bar data available for third bar analysis")
                
        elif (current_minute == (self.config.timing.reference_minute + 1) % 60 and 
              current_second >= self.config.timing.retry_start_time_min and 
              current_second <= self.config.timing.retry_start_time_max):
            # Enhanced execution timing with randomized start
            if not hasattr(self, '_execution_triggered_today'):
                self._execution_triggered_today = False
            
            if not self._execution_triggered_today:
                self.console_ui.log_info(f"🕐 Enhanced execution time (15:51:{current_second:02d})")
                self.strategy.check_execution_timing(now)
                self._execution_triggered_today = True
                
        elif (current_minute == (self.config.timing.reference_minute + 1) % 60 and 
              current_second > self.config.timing.retry_start_time_max):
            # Reset execution trigger for next day
            if hasattr(self, '_execution_triggered_today'):
                self._execution_triggered_today = False
            
    def run(self, args=None):
        """Main application loop"""
        self.console_ui.log_info("Starting Live SPX Trading Application")
        
        # Log risk configuration
        self.console_ui.log_info("=== RISK CONFIGURATION ===")
        self.console_ui.log_info(f"Default Risk: {self.config.pricing.order_size_risk_percentage}%")
        self.console_ui.log_info(f"Bull Risk: {self.config.pricing.order_size_risk_percentage_bull}%")
        self.console_ui.log_info(f"Bear Risk: {self.config.pricing.order_size_risk_percentage_bear}%")
        self.console_ui.log_info(f"Butterfly Risk: {self.config.pricing.order_size_risk_percentage_butterfly}%")
        self.console_ui.log_info(f"Other Risk: {self.config.pricing.order_size_risk_percentage_other}%")
        self.console_ui.log_info("=========================")
        
        # Initialize database
        db_connected = self.initialize_database()
        
        # Connect to IB with command line arguments
        print("DEBUG: About to connect to IB...")
        ib_result = self.connect_to_ib(args.data_host, args.data_port, args.trading_host, args.trading_port)
        print(f"DEBUG: IB connection result: {ib_result}")
        if not ib_result:
            self.console_ui.log_error("Failed to connect to IB - exiting")
            return False
            
        print("DEBUG: IB connection successful, proceeding to setup...")
        
        # Setup contracts and market data
        print("DEBUG: Setting up contracts...")
        contracts_result = self.setup_contracts()
        print(f"DEBUG: Contracts setup result: {contracts_result}")
        if not contracts_result:
            self.console_ui.log_error("Failed to setup contracts - exiting")
            return False
            
        print("DEBUG: Setting up market data...")
        market_data_result = self.setup_market_data()
        print(f"DEBUG: Market data setup result: {market_data_result}")
        if not market_data_result:
            self.console_ui.log_error("Failed to setup market data - exiting")
            return False
            
        # Initialize trading components
        print("DEBUG: Initializing trading components...")
        trading_result = self.initialize_trading_components()
        print(f"DEBUG: Trading components result: {trading_result}")
        if not trading_result:
            self.console_ui.log_error("Failed to initialize trading - exiting")
            return False
            
        self.console_ui.log_info("All systems initialized - starting main loop")
        
        # Timing optimization variables
        last_quote_update = dt.datetime.now()
        quote_update_interval = 5.0  # Update quotes every 5 seconds
        
        try:
            # Main loop
            while True:
                loop_start = dt.datetime.now()
                
                # Process IB messages (always fast)
                self.ib_data.sleep(0)
                
                # Update market data (always needed for SPX price)
                self.update_market_data()
                
                # Update live quotes less frequently
                if (loop_start - last_quote_update).total_seconds() >= quote_update_interval:
                    if self.live_quote_manager:
                        self.live_quote_manager.update_quotes()
                    last_quote_update = loop_start
                
                # Check algorithm timing (always fast)
                self.check_algorithm_timing()
                
                # Update console UI with strategy state (always fast)
                if self.strategy and hasattr(self.strategy, 'state'):
                    self.console_ui.update_strategy_state(self.strategy.state)
                
                # Display the console UI (optimized based on critical period)
                db_type = type(self.db_handler).__name__ if self.db_handler else None
                self.console_ui.display_all(db_connected, db_type)
                
                # Adaptive sleep timing
                if self.console_ui.is_critical_trading_period():
                    # Fast polling during critical period (15:49-15:52)
                    time.sleep(0.1)  # 100ms sleep = ~900ms total cycle
                else:
                    # Normal polling outside critical period
                    time.sleep(1.0)  # 1000ms sleep
                
        except KeyboardInterrupt:
            self.console_ui.log_info("Interrupted by user - shutting down")
            
        finally:
            self.cleanup()
            
        return True
        
    def cleanup(self):
        """Clean up resources"""
        try:
            # End trading session and save trade reports
            if self.strategy:
                try:
                    self.strategy.end_session()
                    self.console_ui.log_info("Trading session ended and reports saved")
                except Exception as e:
                    self.console_ui.log_error(f"Error ending trading session: {str(e)}")
            
            if self.live_quote_manager:
                self.live_quote_manager.cleanup()
                self.console_ui.log_info("Live quotes cancelled")
                
            if self.db_handler:
                self.db_handler.close()
                self.console_ui.log_info("Database connection closed")
                
            if self.ib_data and self.ib_data.isConnected():
                self.ib_data.disconnect()
                self.console_ui.log_info("IB data connection closed")
                
            if self.ib_trading and self.ib_trading != self.ib_data and self.ib_trading.isConnected():
                self.ib_trading.disconnect()
                self.console_ui.log_info("IB trading connection closed")
                
        except Exception as e:
            self.console_ui.log_error(f"Error during cleanup: {str(e)}")


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Live SPX Trading Application with New Algorithm')
    
    # IB connection settings
    parser.add_argument('--data-host', type=str, default='127.0.0.1',
                       help='IB data connection host (default: 127.0.0.1)')
    parser.add_argument('--data-port', type=int, default=4001,
                       help='IB data connection port (default: 4001)')
    parser.add_argument('--trading-host', type=str, default='127.0.0.1',
                       help='IB trading connection host (default: 127.0.0.1)')
    parser.add_argument('--trading-port', type=int, default=None,
                       help='IB trading connection port (default: same as data-port)')
    
    # Strategy settings
    parser.add_argument('--reference-minute', type=int, default=50,
                       help='Reference minute for first bar (default: 50 for XX:50:00)')
    parser.add_argument('--live-test', action='store_true',
                       help='Enable live test mode (orders cancelled automatically)')
    parser.add_argument('--test-price', type=float, default=0.25,
                       help='Test order price for live test mode (default: 0.25)')
    parser.add_argument('--test-cancel-seconds', type=int, default=15,
                       help='Seconds before canceling test orders (default: 15)')
    parser.add_argument('--live', action='store_true',
                       help='Enable REAL live trading (default is paper trading)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Dry run mode - analyze but do not execute any trades')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug logging')
    
    # Risk management settings
    parser.add_argument('--risk-percentage', type=float, default=7.0,
                       help='Default risk percentage of wallet balance per trade (default: 7.0)')
    parser.add_argument('--bull-risk-percentage', dest='bull_risk_percentage', type=float, default=8.0,
                       help='Risk percentage for bull strategies (default: 8.0)')
    parser.add_argument('--bear-risk-percentage', dest='bear_risk_percentage', type=float, default=7.0,
                       help='Risk percentage for bear strategies (default: 7.0)')
    parser.add_argument('--butterfly-risk-percentage', dest='butterfly_risk_percentage', type=float, default=7.0,
                       help='Risk percentage for butterfly strategies (default: 7.0)')
    parser.add_argument('--other-risk-percentage', dest='other_risk_percentage', type=float, default=7.0,
                       help='Risk percentage for other strategies (default: 7.0)')
    
    # Account selection
    parser.add_argument('--account', type=str,
                       help='Specific account ID to use (e.g., U15269296 for Jerry $7000, U20127530 for Jerry $70)')
    
    # Simulation mode
    parser.add_argument('--simulation', action='store_true',
                       help='Run in simulation mode with CSV data')
    parser.add_argument('--scenario', type=str,
                       help='Specific scenario ID to simulate (e.g., BULL_001)')
    
    return parser.parse_args()


def main():
    """Main entry point"""
    args = parse_args()
    
    # If trading port not explicitly set, use data port
    if args.trading_port is None:
        args.trading_port = args.data_port
        print(f"📡 Using port {args.data_port} for both data and trading connections")
    
    # Set up logging with account-specific naming based on port
    global logger
    logger = setup_logging(args.data_port)
    
    # Set up logging level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create configuration from command line arguments
    config = TradingConfig.create_from_args(args)
    
    # Set global configuration
    from config import set_global_config
    set_global_config(config)
    
    # Create and run the application
    app = LiveTradingApp(config, simulation_mode=args.simulation, simulation_scenario=args.scenario)
    
    # Show simulation info if enabled
    if args.simulation:
        print(f"🧪 SIMULATION MODE ENABLED")
        if args.scenario:
            print(f"   Scenario: {args.scenario}")
        else:
            print(f"   Ready for CSV data input")
    
    # Run with command line arguments
    success = app.run(args)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main()) 