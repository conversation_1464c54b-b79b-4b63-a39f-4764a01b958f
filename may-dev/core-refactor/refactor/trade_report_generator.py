#!/usr/bin/env python3
"""
Trade Report Generator - Comprehensive markdown reports for every trading decision
"""

import os
import datetime as dt
from typing import List, Dict, Optional, Any
from dataclasses import dataclass, field
from pathlib import Path

from strategy_state import StrategyState, DecisionType, BarData, SpanData


@dataclass
class DecisionEvent:
    """Single decision event in the trading process"""
    timestamp: dt.datetime
    event_type: str  # "bar_analysis", "reversal", "narrow_conversion", "execution", etc.
    description: str
    data: Dict[str, Any] = field(default_factory=dict)
    
    def __str__(self) -> str:
        return f"{self.timestamp.strftime('%H:%M:%S')} - {self.event_type}: {self.description}"


class TradeReportGenerator:
    """Generates comprehensive markdown trade reports"""
    
    def __init__(self, is_live_test: bool = False):
        self.is_live_test = is_live_test
        self.events: List[DecisionEvent] = []
        self.strategy_state: Optional[StrategyState] = None
        self.session_start: Optional[dt.datetime] = None
        
        # Create output directory
        self.output_dir = Path("test_trade_reports" if is_live_test else "trade_reports")
        self.output_dir.mkdir(exist_ok=True)
    
    def start_session(self, strategy_state: StrategyState):
        """Start a new trading session"""
        self.strategy_state = strategy_state
        self.session_start = dt.datetime.now()
        self.events = []
        
        self.add_event("session_start", f"Trading session started - Default decision: {strategy_state.current_decision.value}")
    
    def add_event(self, event_type: str, description: str, data: Dict[str, Any] = None):
        """Add a decision event to the report"""
        event = DecisionEvent(
            timestamp=dt.datetime.now(),
            event_type=event_type,
            description=description,
            data=data or {}
        )
        self.events.append(event)
    
    def log_bar_analysis(self, bar_number: int, bar_data: BarData, span_data: SpanData, 
                        decision: DecisionType, reason: str, close_percentage: float):
        """Log bar analysis event"""
        data = {
            'bar_number': bar_number,
            'bar_ohlc': f"O:{bar_data.open:.2f} H:{bar_data.high:.2f} L:{bar_data.low:.2f} C:{bar_data.close:.2f}",
            'span_range': f"{span_data.low:.2f} - {span_data.high:.2f} ({span_data.span_points:.2f} pts)",
            'close_percentage': close_percentage,
            'decision': decision.value,
            'reason': reason
        }
        
        self.add_event(
            "bar_analysis",
            f"Bar {bar_number} Analysis: {decision.value} - Close at {close_percentage:.1f}% of span",
            data
        )
    
    def log_reversal(self, from_decision: DecisionType, to_decision: DecisionType, 
                    bar_number: int, reason: str):
        """Log reversal event"""
        # Only log if there's an actual decision change
        if from_decision == to_decision:
            # This is not actually a reversal - log as confirmation instead
            data = {
                'decision': to_decision.value,
                'bar_number': bar_number,
                'reason': reason
            }
            
            self.add_event(
                "decision_confirmation",
                f"CONFIRMATION: {to_decision.value} decision maintained (Bar {bar_number})",
                data
            )
            return
        
        # This is an actual reversal
        data = {
            'from_decision': from_decision.value,
            'to_decision': to_decision.value,
            'bar_number': bar_number,
            'reason': reason
        }
        
        self.add_event(
            "reversal",
            f"REVERSAL: {from_decision.value} → {to_decision.value} (Bar {bar_number})",
            data
        )
    
    def log_narrow_conversion(self, from_decision: DecisionType, to_decision: DecisionType, 
                            span3_points: float):
        """Log narrow conversion event"""
        data = {
            'from_decision': from_decision.value,
            'to_decision': to_decision.value,
            'span3_points': span3_points
        }
        
        self.add_event(
            "narrow_conversion",
            f"NARROW CONVERSION: {from_decision.value} → {to_decision.value} (SPAN3: {span3_points:.2f} < 4.0)",
            data
        )
    
    def log_iron_butterfly_strike_selection(self, close_price: float, span3: SpanData, 
                                          direction: str, reason: str):
        """Log iron butterfly strike selection"""
        data = {
            'close_price': close_price,
            'span3_range': f"{span3.low:.2f} - {span3.high:.2f}",
            'direction': direction,
            'reason': reason
        }
        
        self.add_event(
            "strike_selection",
            f"Iron Butterfly Strike: {direction} - {reason}",
            data
        )
    
    def log_execution_attempt(self, decision: DecisionType, strike: float, 
                            order_price: float, strategy_details: str):
        """Log execution attempt"""
        data = {
            'decision': decision.value,
            'strike': strike,
            'order_price': order_price,
            'strategy_details': strategy_details
        }
        
        self.add_event(
            "execution_attempt",
            f"EXECUTION: {decision.value} at strike {strike:.0f} for ${order_price:.2f}",
            data
        )
    
    def log_execution_result(self, success: bool, trade_id: str = None, 
                           fill_price: float = None, error_message: str = None):
        """Log execution result"""
        data = {
            'success': success,
            'trade_id': trade_id,
            'fill_price': fill_price,
            'error_message': error_message
        }
        
        if success:
            # Handle None fill_price for GTD orders that are submitted but not filled yet
            if fill_price is not None:
                self.add_event(
                    "execution_success",
                    f"✅ ORDER FILLED: ID {trade_id} at ${fill_price:.2f}",
                    data
                )
            else:
                self.add_event(
                    "execution_success",
                    f"✅ ORDER SUBMITTED: ID {trade_id} (GTD - awaiting fill)",
                    data
                )
        else:
            self.add_event(
                "execution_failure",
                f"❌ ORDER FAILED: {error_message}",
                data
            )
    
    def log_dual_trade_intention(self, narrow_decision: DecisionType, ibf_strike: float, 
                               narrow_strategy: str):
        """Log dual trade intention (narrow + IBF)"""
        data = {
            'narrow_decision': narrow_decision.value,
            'ibf_strike': ibf_strike,
            'narrow_strategy': narrow_strategy,
            'dual_type': 'iron_butterfly'
        }
        
        self.add_event(
            "dual_trade_intention",
            f"DUAL TRADE: {narrow_decision.value} + Iron Butterfly @ strike {ibf_strike:.0f}",
            data
        )
    
    def log_overshoot_analysis(self, current_price: float, target_price: float, 
                              overshoot_strikes: int, qualifies: bool):
        """Log overshoot analysis"""
        data = {
            'current_price': current_price,
            'target_price': target_price,
            'overshoot_strikes': overshoot_strikes,
            'qualifies': qualifies
        }
        
        status = "QUALIFIES" if qualifies else "No Action"
        self.add_event(
            "overshoot_analysis",
            f"Overshoot Check: {overshoot_strikes} strikes ({status})",
            data
        )
    
    def generate_report(self) -> str:
        """Generate comprehensive markdown report"""
        if not self.strategy_state:
            return "No trading session data available"
        
        # Generate filename
        timestamp = self.session_start.strftime("%Y%m%d_%H%M%S")
        final_decision = self.strategy_state.current_decision.value
        mode = "TEST" if self.is_live_test else "LIVE"
        filename = f"{timestamp}_{final_decision}_{mode}.md"
        
        # Generate report content
        report = self._generate_markdown_content()
        
        # Save to file
        filepath = self.output_dir / filename
        with open(filepath, 'w') as f:
            f.write(report)
        
        print(f"📊 Trade report saved: {filepath}")
        return str(filepath)
    
    def _generate_markdown_content(self) -> str:
        """Generate the actual markdown content"""
        state = self.strategy_state
        session_duration = (dt.datetime.now() - self.session_start).total_seconds()
        
        # Header
        mode = "🧪 LIVE TEST" if self.is_live_test else "🔴 LIVE TRADING"
        
        markdown = f"""# SPX Trading Report - {mode}

## Session Summary
- **Date:** {self.session_start.strftime('%Y-%m-%d')}
- **Time:** {self.session_start.strftime('%H:%M:%S')} - {dt.datetime.now().strftime('%H:%M:%S')}
- **Duration:** {session_duration:.1f} seconds
- **Final Decision:** **{state.current_decision.value.replace('_', ' ').title()}**
- **Decision Type:** {state.current_decision.value}
- **Trade Executed:** {'Yes' if state.is_trade_executed() else 'No'}

## Market Data Analysis

### Bar Data Summary
"""
        
        # Bar data table
        markdown += "| Bar | Time | Open | High | Low | Close | Range |\n"
        markdown += "|-----|------|------|------|-----|-------|-------|\n"
        
        for i, (bar, span) in enumerate([(state.first_bar, state.span1), 
                                        (state.second_bar, state.span2), 
                                        (state.third_bar, state.span3)], 1):
            if bar and span:
                markdown += f"| {i} | {bar.timestamp.strftime('%H:%M:%S')} | {bar.open:.2f} | {bar.high:.2f} | {bar.low:.2f} | {bar.close:.2f} | {span.span_points:.2f} |\n"
        
        # Span calculations
        markdown += f"\n### Span Calculations\n"
        if state.span1:
            markdown += f"- **SPAN1:** {state.span1.low:.2f} - {state.span1.high:.2f} = {state.span1.span_points:.2f} points\n"
        if state.span2:
            markdown += f"- **SPAN2:** {state.span2.low:.2f} - {state.span2.high:.2f} = {state.span2.span_points:.2f} points\n"
        if state.span3:
            markdown += f"- **SPAN3:** {state.span3.low:.2f} - {state.span3.high:.2f} = {state.span3.span_points:.2f} points\n"
        
        # Decision timeline
        markdown += f"\n## Decision Timeline\n\n"
        
        for i, event in enumerate(self.events, 1):
            icon = self._get_event_icon(event.event_type)
            markdown += f"### {i}. {icon} {event.event_type.replace('_', ' ').title()}\n"
            markdown += f"**Time:** {event.timestamp.strftime('%H:%M:%S')}\n\n"
            markdown += f"**Description:** {event.description}\n\n"
            
            if event.data:
                markdown += "**Details:**\n"
                for key, value in event.data.items():
                    markdown += f"- **{key.replace('_', ' ').title()}:** {value}\n"
            markdown += "\n---\n\n"
        
        # Strategy logic breakdown
        markdown += "## Strategy Logic Breakdown\n\n"
        
        # Thresholds used
        markdown += "### Decision Thresholds\n"
        markdown += "- **Bear Signal:** Close < 12% of span\n"
        markdown += "- **Bull Signal:** Close > 88% of span\n"
        markdown += "- **Iron Butterfly:** Close between 12%-88% of span\n"
        markdown += "- **Narrow Threshold:** SPAN3 < 4.0 points\n\n"
        
        # Final analysis
        if state.current_analysis:
            analysis = state.current_analysis
            markdown += "### Final Analysis\n"
            markdown += f"- **Decision:** {analysis.decision_type.value}\n"
            markdown += f"- **Reason:** {analysis.reason}\n"
            if analysis.close_percentage is not None:
                markdown += f"- **Close Percentage:** {analysis.close_percentage:.1f}%\n"
            if hasattr(analysis, 'iron_butterfly_strike_direction') and analysis.iron_butterfly_strike_direction:
                markdown += f"- **Strike Direction:** {analysis.iron_butterfly_strike_direction}\n"
        
        # Trade execution details
        if state.trade_execution:
            execution = state.trade_execution
            markdown += f"\n## Trade Execution\n\n"
            
            # Prominently display the decision label
            decision_label = execution.decision_type.value.replace('_', ' ').title()
            markdown += f"- **Decision:** **{decision_label}**\n"
            markdown += f"- **Strategy:** {execution.decision_type.value}\n"
            markdown += f"- **Status:** {execution.execution_status.value}\n"
            
            # Handle None values safely
            if execution.strike_price is not None:
                markdown += f"- **Primary Strike:** {execution.strike_price:.0f}\n"
            else:
                markdown += f"- **Primary Strike:** N/A\n"
            
            # Add detailed strike information if available in combo definition
            if hasattr(execution, 'combo_definition') and execution.combo_definition:
                markdown += f"\n### Strike Details\n"
                for i, leg in enumerate(execution.combo_definition.legs, 1):
                    position_type = "**Long**" if leg.action.value == "BUY" else "**Short**"
                    option_type_full = "Call" if leg.option_type.value == "C" else "Put"
                    markdown += f"- **Leg {i}:** {position_type} {leg.strike:.0f} {option_type_full}\n"
            
            if execution.execution_price:
                markdown += f"- **Execution Price:** ${execution.execution_price:.2f}\n"
            if execution.trade_id:
                markdown += f"- **Trade ID:** {execution.trade_id}\n"
            if execution.error_message:
                markdown += f"- **Error:** {execution.error_message}\n"
        
        # Technical details
        markdown += f"\n## Technical Details\n\n"
        markdown += f"- **Algorithm Version:** SPX New Algorithm v3.0\n"
        markdown += f"- **Environment:** {'Live Test Mode' if self.is_live_test else 'Live Trading'}\n"
        markdown += f"- **Total Events:** {len(self.events)}\n"
        
        # Footer
        markdown += f"\n---\n*Report generated at {dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n"
        
        return markdown
    
    def _get_event_icon(self, event_type: str) -> str:
        """Get icon for event type"""
        icons = {
            "session_start": "🚀",
            "bar_analysis": "📊",
            "reversal": "🔄",
            "decision_confirmation": "✔️",
            "narrow_conversion": "📏",
            "dual_trade_intention": "🦋",
            "strike_selection": "🎯",
            "execution_attempt": "⚡",
            "execution_success": "✅",
            "execution_failure": "❌",
            "overshoot_analysis": "📈"
        }
        return icons.get(event_type, "📋") 