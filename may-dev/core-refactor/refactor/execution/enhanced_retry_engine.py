#!/usr/bin/env python3
"""
Enhanced Retry Engine for Option Execution
Implements intelligent retry logic with dynamic optimization
"""

import logging
import datetime as dt
import time
import random
from typing import Dict, Optional, Any, List
from ib_insync import IB

from .order_result import OrderResult
from combo_orders import ComboDefinition, OptionPricingUtils
from config import get_global_config

logger = logging.getLogger(__name__)
detailed_logger = logging.getLogger('trading_detailed')


class EnhancedRetryEngine:
    """Enhanced retry logic with dynamic optimization"""
    
    def __init__(self, ib_connection: IB, base_execution_engine):
        self.ib = ib_connection
        self.base_engine = base_execution_engine
        self.config = get_global_config()
        
        # Track active orders for optimization
        self.active_orders: Dict[str, Any] = {}
        self.first_order_params = None  # Store most conservative order for fallback
    
    def execute_combo_order_with_enhanced_retries(self, combo_definition: ComboDefinition,
                                                 limit_price: float, quantity: int = 1, 
                                                 timeout_seconds: Optional[int] = None) -> OrderResult:
        """
        Execute combo order with enhanced retry logic according to plan:
        
        1. Initial start time: 15:51:31-15:51:36 (randomized)
        2. Initial execution: Submit order and store as fallback
        3. Enhanced retry loop: 3 attempts with 3-second intervals
        4. Dynamic optimization: Re-quote and improve parameters
        5. Conservative fallback: Submit most conservative order if not filled
        """
        try:
            detailed_logger.info(f"=== ENHANCED RETRY EXECUTION STARTING ===")
            detailed_logger.info(f"Strategy: {combo_definition.combo_type.value}")
            detailed_logger.info(f"Initial limit price: ${limit_price:.2f}")
            detailed_logger.info(f"Quantity: {quantity}")
            
            # (a) Get randomized start time
            initial_start_second = self.config.timing.get_randomized_start_time()
            logger.info(f"🎯 Enhanced retry execution starting at 15:51:{initial_start_second:02d}")
            
            # Wait for initial start time
            self._wait_for_exact_time(51, initial_start_second)
            
            # (b) Initial execution - store the most conservative order
            detailed_logger.info("=== INITIAL EXECUTION (Conservative Fallback) ===")
            initial_result = self.base_engine.execute_combo_order(combo_definition, limit_price, quantity, timeout_seconds)
            self.first_order_params = {
                'combo_definition': combo_definition,
                'limit_price': limit_price,
                'quantity': quantity,
                'timeout_seconds': timeout_seconds
            }
            
            if initial_result.success and initial_result.fill_price is not None:
                detailed_logger.info("✅ Initial order filled immediately!")
                return initial_result
            
            detailed_logger.info(f"Initial order placed: {initial_result.trade_id}")
            
            # (c) Enhanced retry loop with 3 recalculation attempts
            for attempt in range(1, self.config.timing.max_retry_attempts + 1):
                detailed_logger.info(f"=== ENHANCED RETRY ATTEMPT {attempt}/{self.config.timing.max_retry_attempts} ===")
                
                # Wait for retry interval
                time.sleep(self.config.timing.retry_interval_seconds)
                
                # Check if any order was filled during wait
                if self._check_for_filled_orders():
                    logger.info("✅ Order filled during enhanced retry process!")
                    return self._get_filled_order_result()
                
                # Get updated market data and optimize
                optimized_combo, optimized_price = self._optimize_combo_for_retry(
                    combo_definition, limit_price, attempt
                )
                
                if self._should_resubmit_with_optimization(optimized_combo, optimized_price, combo_definition, limit_price):
                    detailed_logger.info("🎯 Resubmitting with optimized parameters")
                    
                    # Cancel existing orders
                    self._cancel_active_orders()
                    time.sleep(0.5)  # Brief wait for cancellation
                    
                    # Submit optimized order
                    optimized_result = self.base_engine.execute_combo_order(
                        optimized_combo, optimized_price, quantity, timeout_seconds
                    )
                    
                    if optimized_result.success and optimized_result.fill_price is not None:
                        detailed_logger.info(f"✅ Optimized order filled on attempt {attempt}!")
                        return optimized_result
                    
                    detailed_logger.info(f"Optimized order placed: {optimized_result.trade_id}")
                else:
                    detailed_logger.info("No optimization needed, continuing with existing orders")
            
            # (d) Final wait and check
            detailed_logger.info("=== FINAL WAIT AND CHECK ===")
            time.sleep(self.config.timing.final_wait_seconds)
            
            if self._check_for_filled_orders():
                logger.info("✅ Order filled during final wait!")
                return self._get_filled_order_result()
            
            # (e) Conservative fallback
            detailed_logger.info("=== CONSERVATIVE FALLBACK ===")
            self._cancel_active_orders()
            time.sleep(0.5)
            
            # Submit the most conservative order (leave in book)
            fallback_result = self.base_engine.execute_combo_order(
                self.first_order_params['combo_definition'],
                self.first_order_params['limit_price'],
                self.first_order_params['quantity'],
                timeout_seconds='leave_in_book'
            )
            
            logger.info("📋 Submitting conservative order (left in book)")
            detailed_logger.info("=== ENHANCED RETRY EXECUTION COMPLETE ===")
            
            return fallback_result
            
        except Exception as e:
            detailed_logger.error(f"=== ENHANCED RETRY EXECUTION FAILED ===")
            detailed_logger.error(f"Exception: {str(e)}")
            detailed_logger.error(f"Exception type: {type(e).__name__}")
            import traceback
            detailed_logger.error(f"Traceback:\n{traceback.format_exc()}")
            logger.error(f"❌ Error in enhanced retry execution: {str(e)}")
            
            return OrderResult(success=False, error_message=f"Enhanced retry execution failed: {str(e)}")
    
    def _wait_for_exact_time(self, target_minute: int, target_second: int):
        """Wait until exact time (minute:second)"""
        while True:
            now = dt.datetime.now()
            if now.minute == target_minute and now.second >= target_second:
                break
            time.sleep(0.1)
    
    def _optimize_combo_for_retry(self, original_combo: ComboDefinition, 
                                 original_limit_price: float, attempt: int) -> tuple:
        """Optimize combo definition and price for retry attempt"""
        try:
            # Get current SPX price
            current_spx_price = self._get_current_spx_price()
            if current_spx_price is None:
                return original_combo, original_limit_price
            
            # Get current ATM strike
            atm_strike = self._get_atm_strike(current_spx_price)
            
            # Check if strike optimization is needed (>$2.5 away from ATM)
            if hasattr(original_combo, 'center_strike'):
                strike_distance = abs(atm_strike - original_combo.center_strike)
                if strike_distance > 2.5:
                    detailed_logger.info(f"Strike optimization: ATM={atm_strike}, Current={original_combo.center_strike}, Distance={strike_distance}")
                    
                    # Create new combo with current ATM strike
                    optimized_combo = self._create_optimized_combo(original_combo, atm_strike)
                    
                    # Re-quote with new strikes
                    optimized_price = self._get_optimized_price(optimized_combo, original_limit_price, attempt)
                    
                    return optimized_combo, optimized_price
            
            # Price optimization only (no strike change)
            optimized_price = self._get_optimized_price(original_combo, original_limit_price, attempt)
            return original_combo, optimized_price
            
        except Exception as e:
            detailed_logger.error(f"Error in combo optimization: {str(e)}")
            return original_combo, original_limit_price
    
    def _create_optimized_combo(self, original_combo: ComboDefinition, new_atm_strike: float) -> ComboDefinition:
        """Create optimized combo with new ATM strike"""
        from combo_orders import ComboOrderFactory
        
        combo_type = original_combo.combo_type.value
        
        if combo_type == 'bull_spread':
            return ComboOrderFactory.create_bull_spread(new_atm_strike)
        elif combo_type == 'bear_spread':
            return ComboOrderFactory.create_bear_spread(new_atm_strike)
        elif combo_type == 'iron_butterfly':
            return ComboOrderFactory.create_iron_butterfly(new_atm_strike)
        elif combo_type == 'wide_iron_butterfly':
            return ComboOrderFactory.create_wide_iron_butterfly(new_atm_strike)
        else:
            return original_combo
    
    def _get_optimized_price(self, combo_definition: ComboDefinition, 
                           original_price: float, attempt: int) -> float:
        """Get optimized price for retry attempt"""
        try:
            # Get fresh quote
            quote_result = self.base_engine.quote_engine.get_combo_quote(combo_definition)
            
            if not quote_result.is_valid or quote_result.mid_price is None:
                return original_price
            
            mid_price = quote_result.mid_price
            
            if combo_definition.is_credit_strategy:
                # Credit strategy: try for slightly better credit
                optimized_price = mid_price - 0.05  # Give up 5c of credit for better fill
                return OptionPricingUtils.round_to_nickel(optimized_price)
            else:
                # Debit strategy: MID + 0.05c, but must not exceed $2.50
                raw_price = mid_price + 0.05
                optimized_price = min(raw_price, 2.50)  # Cap at $2.50
                
                # If price would exceed $2.50, this should trigger strike optimization
                if raw_price > 2.50:
                    detailed_logger.info(f"Price limit hit: MID+0.05c = ${raw_price:.2f} > $2.50, needs strike optimization")
                
                return OptionPricingUtils.round_to_nickel(optimized_price, round_up=True)
            
        except Exception as e:
            detailed_logger.error(f"Error getting optimized price: {str(e)}")
            return original_price
    
    def _should_resubmit_with_optimization(self, optimized_combo: ComboDefinition, 
                                         optimized_price: float,
                                         original_combo: ComboDefinition, 
                                         original_price: float) -> bool:
        """Determine if resubmission with optimization is worthwhile"""
        
        # Check for strike optimization
        if hasattr(optimized_combo, 'center_strike') and hasattr(original_combo, 'center_strike'):
            if optimized_combo.center_strike != original_combo.center_strike:
                detailed_logger.info(f"Strike optimization detected: {original_combo.center_strike} → {optimized_combo.center_strike}")
                return True
        
        # Check for price improvement
        price_diff = abs(optimized_price - original_price)
        if price_diff >= 0.05:  # At least 5c improvement
            price_direction = "better" if (
                (optimized_combo.is_credit_strategy and optimized_price > original_price) or
                (not optimized_combo.is_credit_strategy and optimized_price < original_price)
            ) else "worse"
            
            if price_direction == "better":
                detailed_logger.info(f"Price improvement detected: ${original_price:.2f} → ${optimized_price:.2f}")
                return True
        
        return False
    
    def _cancel_active_orders(self):
        """Cancel all active orders"""
        try:
            for order_id in list(self.active_orders.keys()):
                try:
                    self.ib.cancelOrder(order_id)
                    detailed_logger.info(f"Cancelled order: {order_id}")
                except Exception as e:
                    detailed_logger.warning(f"Failed to cancel order {order_id}: {str(e)}")
                    
            self.active_orders.clear()
            
        except Exception as e:
            detailed_logger.error(f"Error cancelling active orders: {str(e)}")
    
    def _check_for_filled_orders(self) -> bool:
        """Check if any orders were filled"""
        try:
            # This would check IB for filled orders
            # Implementation depends on IB API integration
            return False
            
        except Exception as e:
            detailed_logger.error(f"Error checking for filled orders: {str(e)}")
            return False
    
    def _get_filled_order_result(self) -> OrderResult:
        """Get result for filled order"""
        # This would return the actual filled order result
        # Implementation depends on IB API integration
        return OrderResult(success=True, trade_id="FILLED_001", fill_price=2.45)
    
    def _get_current_spx_price(self) -> Optional[float]:
        """Get current SPX price"""
        try:
            # Use the base engine's price provider
            return self.base_engine._get_current_spx_price()
        except:
            return None
    
    def _get_atm_strike(self, spx_price: float) -> float:
        """Get ATM strike based on SPX price"""
        from combo_orders import StrikeCalculator
        return StrikeCalculator.get_atm_strike(spx_price) 