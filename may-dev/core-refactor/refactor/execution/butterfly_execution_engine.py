#!/usr/bin/env python3
"""
Butterfly Execution Engine
Handles iron butterfly execution with dynamic pricing and fallback logic
"""

import logging
from typing import Optional, Tuple, Dict

from .order_result import OrderResult
from combo_orders import ComboOrderFactory, OptionPricingUtils
from config import get_global_config

logger = logging.getLogger(__name__)


class ButterflyExecutionEngine:
    """Iron butterfly execution with dynamic pricing"""
    
    def __init__(self, ib_connection, base_execution_engine):
        self.ib = ib_connection
        self.base_engine = base_execution_engine
        self.config = get_global_config()
    
    def execute_iron_butterfly(self, center_strike: float, quantity: Optional[int] = None) -> OrderResult:
        """Execute narrow iron butterfly with fixed pricing"""
        combo_def = ComboOrderFactory.create_narrow_iron_butterfly(center_strike)
        logger.info(f"🎯 NARROW IRON BUTTERFLY: Fixed pricing for {center_strike} strike")

        # Get quote for contract qualification only
        quote_result = self.base_engine.quote_engine.get_combo_quote(combo_def)

        if not (quote_result.is_valid and quote_result.mid_price is not None):
            logger.error(f"❌ INVALID QUOTE for narrow iron butterfly: {quote_result.error_message}")
            return OrderResult(success=False, error_message="Invalid quote for narrow iron butterfly")

        mid_price = quote_result.mid_price
        logger.info(f"💰 Market Quote: Bid={quote_result.bid:.2f}, Ask={quote_result.ask:.2f}, Mid={mid_price:.2f}")

        # Use fixed pricing at minimum credit level - no dynamic adjustments
        min_credit_config = self.config.pricing.narrow_iron_butterfly_credit_min
        limit_price = -abs(min_credit_config)  # Negative for credit strategy
        
        logger.info(f"✅ USING FIXED PRICE: ${limit_price:.2f} (credit of ${abs(limit_price):.2f}) - will be left in book")
        
        # In live test mode, override with test price but log the real price
        if self.config.live_test.enabled:
            actual_price = limit_price
            limit_price = -abs(self.config.pricing.narrow_iron_butterfly_test_credit)  # Negative for credit
            logger.info(f"🧪 LIVE TEST: Real price ${actual_price:.2f} → Test price ${limit_price:.2f}")
        
        return self.base_engine.execute_combo_order(combo_def, limit_price, quantity, timeout_seconds='leave_in_book')
    
    def execute_wide_iron_butterfly(self, center_strike: float, quantity: Optional[int] = None) -> OrderResult:
        """Execute wide iron butterfly with dynamic pricing"""
        combo_def = ComboOrderFactory.create_wide_iron_butterfly(center_strike)
        logger.info(f"🎯 WIDE IRON BUTTERFLY: Dynamic pricing for {center_strike} strike")

        quote_result = self.base_engine.quote_engine.get_combo_quote(combo_def)

        if not (quote_result.is_valid and quote_result.mid_price is not None):
            logger.error(f"❌ INVALID QUOTE for wide iron butterfly: {quote_result.error_message}")
            return OrderResult(success=False, error_message="Invalid quote for wide iron butterfly")

        mid_price = quote_result.mid_price
        logger.info(f"💰 Quote: Bid={quote_result.bid:.2f}, Ask={quote_result.ask:.2f}, Mid={mid_price:.2f}")

        # Calculate limit price with buffer (e.g., give up $0.05 of credit)
        limit_price = OptionPricingUtils.calculate_limit_price_with_buffer(
            mid_price,
            is_credit_strategy=True,
            buffer=0.05
        )

        # Safety check: ensure the calculated credit is still above our minimum
        min_credit_config = self.config.pricing.wide_iron_butterfly_credit_min
        if abs(limit_price) < min_credit_config:
            logger.warning(f"❌ REJECTED: Dynamic credit ${abs(limit_price):.2f} is below minimum of ${min_credit_config:.2f}")
            return OrderResult(success=False, error_message=f"Dynamic credit ${abs(limit_price):.2f} below min ${min_credit_config:.2f}")

        logger.info(f"✅ USING DYNAMIC PRICE: ${limit_price:.2f} (credit of ${abs(limit_price):.2f})")
        
        # In live test mode, override with test price but log the real price
        if self.config.live_test.enabled:
            actual_price = limit_price
            limit_price = -abs(self.config.pricing.wide_iron_butterfly_test_credit)  # Negative for credit
            logger.info(f"🧪 LIVE TEST: Real price ${actual_price:.2f} → Test price ${limit_price:.2f}")
        
        # Calculate risk-adjusted order size if not specified
        if quantity is None:
            quantity = self.base_engine.get_risk_adjusted_order_size(limit_price, combo_def)
            logger.info(f"📊 Risk-Adjusted Order Size: {quantity} contracts")
        
        return self.base_engine.execute_combo_order(combo_def, limit_price, quantity, timeout_seconds=9)
    
    def execute_iron_butterfly_with_timeout(self, center_strike: float, 
                                          order_price: float, timeout_seconds: int) -> Tuple[bool, Dict]:
        """Execute iron butterfly with specific price and timeout"""
        combo_def = ComboOrderFactory.create_iron_butterfly(center_strike)
        result = self.base_engine.execute_combo_order(combo_def, order_price, 1, timeout_seconds)
        
        fill_details = {
            'filled': result.success,
            'fill_time': (result.execution_time - dt.datetime.now()).total_seconds() if result.success else timeout_seconds,
            'fill_price': result.fill_price if result.success else None,
            'timeout_seconds': timeout_seconds,
            'error': result.error_message if not result.success else None
        }
        
        return result.success, fill_details 