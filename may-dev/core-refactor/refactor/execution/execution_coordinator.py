#!/usr/bin/env python3
"""
Execution Coordinator - Main interface for all option execution
Orchestrates modular execution engines with enhanced retry logic
"""

import logging
from typing import Optional

from .order_result import OrderResult
from .enhanced_retry_engine import EnhancedRetryEngine
from .spread_execution_engine import SpreadExecutionEngine
from .butterfly_execution_engine import ButterflyExecutionEngine
from combo_orders import ComboDefinition

logger = logging.getLogger(__name__)


class ExecutionCoordinator:
    """Main execution coordinator with modular engines"""
    
    def __init__(self, ib_connection, base_execution_engine):
        self.ib = ib_connection
        self.base_engine = base_execution_engine
        
        # Initialize modular execution engines
        self.enhanced_retry_engine = EnhancedRetryEngine(ib_connection, base_execution_engine)
        self.spread_engine = SpreadExecutionEngine(ib_connection, base_execution_engine)
        self.butterfly_engine = ButterflyExecutionEngine(ib_connection, base_execution_engine)
        
        logger.info("🚀 Execution Coordinator initialized with modular engines")
    
    # Enhanced retry methods (delegate to spread engine)
    def execute_bull_spread_with_enhanced_retries(self, current_price: float, 
                                                 quantity: Optional[int] = None, 
                                                 timeout_seconds: Optional[int] = None) -> OrderResult:
        """Execute bull spread with enhanced retry logic"""
        return self.spread_engine.execute_bull_spread_with_enhanced_retries(
            current_price, quantity, timeout_seconds
        )
    
    def execute_bear_spread_with_enhanced_retries(self, current_price: float, 
                                                 quantity: Optional[int] = None, 
                                                 timeout_seconds: Optional[int] = None) -> OrderResult:
        """Execute bear spread with enhanced retry logic"""
        return self.spread_engine.execute_bear_spread_with_enhanced_retries(
            current_price, quantity, timeout_seconds
        )
    
    def execute_combo_order_with_enhanced_retries(self, combo_definition: ComboDefinition,
                                                 limit_price: float, quantity: int = 1, 
                                                 timeout_seconds: Optional[int] = None) -> OrderResult:
        """Execute any combo order with enhanced retry logic"""
        return self.enhanced_retry_engine.execute_combo_order_with_enhanced_retries(
            combo_definition, limit_price, quantity, timeout_seconds
        )
    
    # Iron butterfly methods (delegate to butterfly engine)
    def execute_iron_butterfly(self, center_strike: float, quantity: Optional[int] = None) -> OrderResult:
        """Execute narrow iron butterfly"""
        return self.butterfly_engine.execute_iron_butterfly(center_strike, quantity)
    
    def execute_wide_iron_butterfly(self, center_strike: float, quantity: Optional[int] = None) -> OrderResult:
        """Execute wide iron butterfly"""
        return self.butterfly_engine.execute_wide_iron_butterfly(center_strike, quantity)
    
    def execute_iron_butterfly_with_timeout(self, center_strike: float, 
                                          order_price: float, timeout_seconds: int):
        """Execute iron butterfly with specific timeout"""
        return self.butterfly_engine.execute_iron_butterfly_with_timeout(
            center_strike, order_price, timeout_seconds
        )
    
    # Pass-through methods to base engine for compatibility
    def execute_combo_order(self, combo_definition: ComboDefinition, limit_price: float, 
                          quantity: int = 1, timeout_seconds: Optional[int] = None) -> OrderResult:
        """Execute combo order using base engine"""
        return self.base_engine.execute_combo_order(combo_definition, limit_price, quantity, timeout_seconds)
    
    def execute_bull_spread(self, current_price: float, quantity: Optional[int] = None, 
                          timeout_seconds: Optional[int] = None) -> OrderResult:
        """Execute bull spread using base engine (legacy)"""
        return self.base_engine.execute_bull_spread(current_price, quantity, timeout_seconds)
    
    def execute_bear_spread(self, current_price: float, quantity: Optional[int] = None, 
                          timeout_seconds: Optional[int] = None) -> OrderResult:
        """Execute bear spread using base engine (legacy)"""
        return self.base_engine.execute_bear_spread(current_price, quantity, timeout_seconds)
    
    # Risk management and utility methods
    def get_risk_adjusted_order_size(self, limit_price: float, combo_definition: ComboDefinition) -> int:
        """Get risk-adjusted order size"""
        return self.base_engine.get_risk_adjusted_order_size(limit_price, combo_definition)
    
    def record_trade_report_to_database(self, report_content: str, strategy_state):
        """Record trade report to database"""
        return self.base_engine.record_trade_report_to_database(report_content, strategy_state) 