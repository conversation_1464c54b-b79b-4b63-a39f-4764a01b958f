#!/usr/bin/env python3
"""
Spread Execution Engine with Enhanced Retry Logic
Handles bull and bear spread execution with intelligent optimization
"""

import logging
from typing import Optional

from .order_result import OrderResult
from .enhanced_retry_engine import EnhancedRetryEngine
from combo_orders import ComboOrderFactory, StrikeCalculator
from config import get_global_config

logger = logging.getLogger(__name__)


class SpreadExecutionEngine:
    """Spread execution with enhanced retry logic"""
    
    def __init__(self, ib_connection, base_execution_engine):
        self.ib = ib_connection
        self.base_engine = base_execution_engine
        self.config = get_global_config()
        
        # Initialize enhanced retry engine
        self.retry_engine = EnhancedRetryEngine(ib_connection, base_execution_engine)
    
    def execute_bull_spread_with_enhanced_retries(self, current_price: float, 
                                                 quantity: Optional[int] = None, 
                                                 timeout_seconds: Optional[int] = None) -> OrderResult:
        """Execute bull spread with enhanced retry logic"""
        try:
            logger.info(f"🎯 BULL SPREAD: Enhanced retry execution for SPX={current_price:.2f}")
            
            # Get strike sequence to try
            strike_pairs = StrikeCalculator.get_bull_strike_sequence(current_price)
            logger.info(f"📊 Strike sequence: {strike_pairs}")
            
            # Try each strike pair with enhanced retry logic
            for attempt, (buy_strike, sell_strike) in enumerate(strike_pairs, 1):
                logger.info(f"🔍 Enhanced retry attempt {attempt}: {buy_strike}/{sell_strike}")
                
                # Create combo definition
                combo_def = ComboOrderFactory.create_bull_spread_from_strikes(buy_strike, sell_strike)
                
                # Get quote to validate pricing
                quote_result = self.base_engine.quote_engine.get_combo_quote(combo_def)
                
                if not quote_result.is_valid:
                    logger.warning(f"❌ Invalid quote for {buy_strike}/{sell_strike}")
                    continue
                
                # Check if quote is within price limit
                mid_price = quote_result.mid_price
                limit_price = min(mid_price + 0.05, self.config.pricing.spread_price_limit)
                
                if limit_price > self.config.pricing.spread_price_limit:
                    logger.warning(f"❌ Price ${limit_price:.2f} exceeds limit ${self.config.pricing.spread_price_limit:.2f}")
                    continue
                
                logger.info(f"✅ Using strikes {buy_strike}/{sell_strike} @ ${limit_price:.2f}")
                
                # Use enhanced retry logic
                return self.retry_engine.execute_combo_order_with_enhanced_retries(
                    combo_def, limit_price, quantity, timeout_seconds
                )
            
            # If all attempts failed
            logger.error(f"❌ All bull spread attempts failed")
            return OrderResult(success=False, error_message="All bull spread strikes exceeded price limit")
            
        except Exception as e:
            logger.error(f"Error in bull spread enhanced retry execution: {str(e)}")
            return OrderResult(success=False, error_message=f"Bull spread enhanced retry failed: {str(e)}")
    
    def execute_bear_spread_with_enhanced_retries(self, current_price: float, 
                                                 quantity: Optional[int] = None, 
                                                 timeout_seconds: Optional[int] = None) -> OrderResult:
        """Execute bear spread with enhanced retry logic"""
        try:
            logger.info(f"🎯 BEAR SPREAD: Enhanced retry execution for SPX={current_price:.2f}")
            
            # Get strike sequence to try
            strike_pairs = StrikeCalculator.get_bear_strike_sequence(current_price)
            logger.info(f"📊 Strike sequence: {strike_pairs}")
            
            # Try each strike pair with enhanced retry logic
            for attempt, (buy_strike, sell_strike) in enumerate(strike_pairs, 1):
                logger.info(f"🔍 Enhanced retry attempt {attempt}: {buy_strike}/{sell_strike}")
                
                # Create combo definition
                combo_def = ComboOrderFactory.create_bear_spread_from_strikes(buy_strike, sell_strike)
                
                # Get quote to validate pricing
                quote_result = self.base_engine.quote_engine.get_combo_quote(combo_def)
                
                if not quote_result.is_valid:
                    logger.warning(f"❌ Invalid quote for {buy_strike}/{sell_strike}")
                    continue
                
                # Check if quote is within price limit
                mid_price = quote_result.mid_price
                limit_price = min(mid_price + 0.05, self.config.pricing.spread_price_limit)
                
                if limit_price > self.config.pricing.spread_price_limit:
                    logger.warning(f"❌ Price ${limit_price:.2f} exceeds limit ${self.config.pricing.spread_price_limit:.2f}")
                    continue
                
                logger.info(f"✅ Using strikes {buy_strike}/{sell_strike} @ ${limit_price:.2f}")
                
                # Use enhanced retry logic
                return self.retry_engine.execute_combo_order_with_enhanced_retries(
                    combo_def, limit_price, quantity, timeout_seconds
                )
            
            # If all attempts failed
            logger.error(f"❌ All bear spread attempts failed")
            return OrderResult(success=False, error_message="All bear spread strikes exceeded price limit")
            
        except Exception as e:
            logger.error(f"Error in bear spread enhanced retry execution: {str(e)}")
            return OrderResult(success=False, error_message=f"Bear spread enhanced retry failed: {str(e)}") 