#!/usr/bin/env python3
"""
Order Result Classes for Option Execution
"""

import datetime as dt
from typing import Optional


class OrderResult:
    """Result of order execution"""
    
    def __init__(self, success: bool, trade_id: Optional[str] = None, 
                 fill_price: Optional[float] = None, error_message: Optional[str] = None,
                 order_price: Optional[float] = None, order_status: Optional[str] = None,
                 ib_execution_id: Optional[str] = None, commission: Optional[float] = None,
                 strike_price: Optional[float] = None, buy_strike: Optional[float] = None, 
                 sell_strike: Optional[float] = None):
        self.success = success
        self.trade_id = trade_id
        self.fill_price = fill_price
        self.error_message = error_message
        self.execution_time = dt.datetime.now()
        
        # Additional data for database recording
        self.order_price = order_price  # The limit price we submitted
        self.order_status = order_status  # Final IB order status
        self.ib_execution_id = ib_execution_id  # IB execution ID if filled
        self.commission = commission  # Commission if available
        
        # Strike information
        self.strike_price = strike_price  # Main/center strike
        self.buy_strike = buy_strike      # Buy leg strike
        self.sell_strike = sell_strike    # Sell leg strike
    
    def __str__(self) -> str:
        if self.success:
            if self.fill_price:
                base = f"Order filled: ID {self.trade_id} @ ${self.fill_price:.2f}"
                if self.buy_strike and self.sell_strike:
                    base += f" ({self.buy_strike}/{self.sell_strike})"
                return base
            else:
                return f"Order placed: ID {self.trade_id} @ ${self.order_price:.2f} (GTD)"
        else:
            return f"Order failed: {self.error_message}" 