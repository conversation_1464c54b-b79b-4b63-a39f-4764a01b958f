#!/usr/bin/env python3
"""
Unified Quote Engine for Option Combos
"""

import logging
import datetime as dt
from typing import Dict, List, Optional, Any, Tuple
from ib_insync import IB, Contract, Option, Ticker
import time

from combo_orders import (
    ComboDefinition, ComboQuoteRequest, ComboQuoteResult, 
    ComboOrderBuilder, ComboType, OptionLeg, OptionType
)
from config import get_global_config

logger = logging.getLogger(__name__)


class ContractCache:
    """Cache for option contracts to avoid repeated lookups"""
    
    def __init__(self):
        self.contracts: Dict[str, Contract] = {}
        self.tickers: Dict[str, Ticker] = {}
        self.last_update: Dict[str, dt.datetime] = {}
    
    def _get_contract_key(self, symbol: str, strike: float, option_type: str, 
                         expiry: str, trading_class: str) -> str:
        """Generate cache key for contract"""
        return f"{symbol}_{strike}_{option_type}_{expiry}_{trading_class}"
    
    def get_contract(self, symbol: str, strike: float, option_type: str, 
                    expiry: str, trading_class: str = "SPXW") -> Optional[Contract]:
        """Get cached contract or None if not found"""
        key = self._get_contract_key(symbol, strike, option_type, expiry, trading_class)
        return self.contracts.get(key)
    
    def store_contract(self, contract: Contract, symbol: str, strike: float, 
                      option_type: str, expiry: str, trading_class: str):
        """Store contract in cache"""
        key = self._get_contract_key(symbol, strike, option_type, expiry, trading_class)
        self.contracts[key] = contract
        self.last_update[key] = dt.datetime.now()
    
    def get_ticker(self, contract_key: str) -> Optional[Ticker]:
        """Get cached ticker for contract"""
        return self.tickers.get(contract_key)
    
    def store_ticker(self, contract_key: str, ticker: Ticker):
        """Store ticker in cache"""
        self.tickers[contract_key] = ticker
    
    def clear_expired(self, max_age_hours: int = 24):
        """Clear contracts older than max_age_hours"""
        cutoff = dt.datetime.now() - dt.timedelta(hours=max_age_hours)
        expired_keys = [
            key for key, timestamp in self.last_update.items() 
            if timestamp < cutoff
        ]
        
        for key in expired_keys:
            self.contracts.pop(key, None)
            self.tickers.pop(key, None)
            self.last_update.pop(key, None)
        
        if expired_keys:
            logger.info(f"Cleared {len(expired_keys)} expired contracts from cache")


class OptionQuoteEngine:
    """Unified quote engine for all option combo types"""
    
    def __init__(self, ib_connection: IB):
        self.ib = ib_connection
        self.contract_cache = ContractCache()
        self.config = get_global_config()
        
        # Request live market data (critical fix!)
        try:
            self.ib.reqMarketDataType(1)  # Type 1 = Live market data
            logger.info("✅ Requested live market data for quote engine")
        except Exception as e:
            logger.warning(f"Failed to request live market data: {e}")
        
        # Quote request tracking
        self.active_requests: Dict[str, dt.datetime] = {}
        self.quote_cache: Dict[str, ComboQuoteResult] = {}
        
    def get_combo_quote(self, combo_definition: ComboDefinition, 
                       expiry_date: str = None) -> ComboQuoteResult:
        """Get quote for any combo order type"""
        if expiry_date is None:
            expiry_date = dt.datetime.now().strftime("%Y%m%d")
        
        # Enhanced logging for live test mode
        if self.config.live_test.enabled:
            logger.info(f"📋 Quote Request: {combo_definition.description}")
            for i, leg in enumerate(combo_definition.legs):
                logger.info(f"   Leg {i+1}: {leg.action.value} {leg.strike}{leg.option_type.value}")
        
        quote_request = ComboQuoteRequest(
            combo_definition=combo_definition,
            expiry_date=expiry_date,
            symbol=self.config.orders.option_symbol,
            trading_class=self.config.orders.option_trading_class,
            exchange=self.config.orders.combo_order_exchange
        )
        
        result = self._execute_quote_request(quote_request)
        
        # Enhanced result logging for live test mode
        if self.config.live_test.enabled and result.is_valid:
            logger.info(f"📊 Individual Leg Quotes:")
            for leg_key, quotes in result.individual_leg_quotes.items():
                logger.info(f"   {leg_key}: Bid={quotes.get('bid', 'N/A')} Ask={quotes.get('ask', 'N/A')} Action={quotes.get('action', 'N/A')}")
            logger.info(f"📈 Combo Result: Bid={result.bid:.2f} Ask={result.ask:.2f} Mid={result.mid_price:.2f}")
        elif self.config.live_test.enabled:
            logger.info(f"❌ Quote Request Failed: {result.error_message}")
        
        return result
    
    def get_wide_butterfly_quote(self, center_strike: float) -> float:
        """Get wide iron butterfly quote for $6.50 credit check"""
        from combo_orders import ComboOrderFactory
        combo_def = ComboOrderFactory.create_wide_iron_butterfly(center_strike)
        quote_result = self.get_combo_quote(combo_def)
        
        if quote_result.is_valid and quote_result.bid is not None:
            return abs(quote_result.bid)  # Take absolute value for credit
        return 0.0
    
    def check_wide_butterfly_fallback_threshold(self, center_strike: float) -> bool:
        """Check if wide iron butterfly meets fallback threshold ($6.00+)"""
        try:
            quote = self.get_wide_butterfly_quote(center_strike)
            fallback_threshold = self.config.pricing.wide_iron_butterfly_fallback_min
            
            meets_threshold = quote >= fallback_threshold
            logger.info(f"🔍 Wide butterfly fallback check: ${quote:.2f} {'≥' if meets_threshold else '<'} ${fallback_threshold:.2f}")
            
            return meets_threshold
            
        except Exception as e:
            logger.error(f"Error checking wide butterfly fallback threshold: {str(e)}")
            return False
    
    def get_straddle_quote(self, center_strike: float, is_wide: bool = False) -> float:
        """Get straddle quote"""
        try:
            logger.info(f"Getting {'wide' if is_wide else 'narrow'} straddle quote for strike {center_strike}")
            return 0.0
        except Exception as e:
            logger.error(f"Error getting straddle quote: {str(e)}")
            return 0.0
    
    def get_spread_quote(self, center_strike: float, is_bull: bool = True, 
                        is_narrow: bool = False, spread_width: int = 5,
                        expiry_date: str = None) -> ComboQuoteResult:
        """Get spread quote (bull/bear, narrow/regular)"""
        from combo_orders import ComboOrderFactory
        
        if is_bull:
            if is_narrow:
                combo_def = ComboOrderFactory.create_one_better_narrow_bull(center_strike)
            else:
                combo_def = ComboOrderFactory.create_bull_spread(center_strike, spread_width)
        else:
            if is_narrow:
                combo_def = ComboOrderFactory.create_one_better_narrow_bear(center_strike)
            else:
                combo_def = ComboOrderFactory.create_bear_spread(center_strike, spread_width)
        
        return self.get_combo_quote(combo_def, expiry_date)
    
    def get_quote_from_decision(self, decision_type: str, center_strike: float,
                               expiry_date: str = None) -> ComboQuoteResult:
        """Get quote based on strategy decision type"""
        from combo_orders import ComboOrderFactory
        
        try:
            combo_def = ComboOrderFactory.create_from_decision_type(decision_type, center_strike)
            return self.get_combo_quote(combo_def, expiry_date)
        except ValueError as e:
            logger.error(f"Invalid decision type for quote: {decision_type}")
            return ComboQuoteResult(
                combo_definition=None,
                error_message=str(e)
            )
    
    def _execute_quote_request(self, quote_request: ComboQuoteRequest) -> ComboQuoteResult:
        """Execute the actual quote request"""
        try:
            # Step 1: Get/qualify all required option contracts
            contracts_success = self._get_option_contracts(quote_request)
            if not contracts_success:
                return ComboQuoteResult(
                    combo_definition=quote_request.combo_definition,
                    error_message="Failed to get option contracts"
                )
            
            # Step 2: Get individual leg quotes
            individual_quotes = self._get_individual_leg_quotes(quote_request)
            
            # Step 3: Calculate combo quote from individual legs (primary method)
            calculated_quotes = self._calculate_combo_from_legs(quote_request, individual_quotes)
            
            # Step 4: Create result with calculated quotes
            result = ComboQuoteResult(
                combo_definition=quote_request.combo_definition,
                bid=calculated_quotes.get('bid') if calculated_quotes else None,
                ask=calculated_quotes.get('ask') if calculated_quotes else None,
                last=None,  # Not available from individual legs
                individual_leg_quotes=individual_quotes,
                quote_timestamp=dt.datetime.now().isoformat()
            )
            
            # Validate quote quality
            if not result.is_valid:
                result.error_message = "No valid quotes available"
            
            return result
            
        except Exception as e:
            logger.error(f"Error executing quote request: {str(e)}")
            return ComboQuoteResult(
                combo_definition=quote_request.combo_definition,
                error_message=str(e)
            )
    
    def _get_option_contracts(self, quote_request: ComboQuoteRequest) -> bool:
        """Get and qualify all option contracts for the combo"""
        try:
            required_contracts = quote_request.get_required_contracts()
            
            for i, contract_spec in enumerate(required_contracts):
                leg = quote_request.combo_definition.legs[i]
                
                # Check cache first
                cached_contract = self.contract_cache.get_contract(
                    contract_spec['symbol'],
                    contract_spec['strike'],
                    contract_spec['option_type'],
                    contract_spec['expiry'],
                    contract_spec['trading_class']
                )
                
                if cached_contract:
                    leg.contract = cached_contract
                    continue
                
                # Create and qualify new contract
                option_contract = Option(
                    symbol=contract_spec['symbol'],
                    lastTradeDateOrContractMonth=contract_spec['expiry'],
                    strike=contract_spec['strike'],
                    right=contract_spec['option_type'],
                    exchange=contract_spec['exchange'],
                    tradingClass=contract_spec['trading_class']
                )
                
                # Qualify the contract
                qualified_contracts = self.ib.qualifyContracts(option_contract)
                if not qualified_contracts:
                    logger.error(f"Failed to qualify contract: {contract_spec}")
                    return False
                
                qualified_contract = qualified_contracts[0]
                leg.contract = qualified_contract
                
                # Cache the contract
                self.contract_cache.store_contract(
                    qualified_contract,
                    contract_spec['symbol'],
                    contract_spec['strike'],
                    contract_spec['option_type'],
                    contract_spec['expiry'],
                    contract_spec['trading_class']
                )
            
            return True
            
        except Exception as e:
            logger.error(f"Error getting option contracts: {str(e)}")
            return False
    
    def _get_individual_leg_quotes(self, quote_request: ComboQuoteRequest) -> Dict[str, Dict[str, float]]:
        """Get quotes for individual legs"""
        leg_quotes = {}
        
        try:
            for leg in quote_request.combo_definition.legs:
                if leg.contract is None:
                    continue
                
                leg_key = f"{leg.strike}{leg.option_type.value}"
                
                # Check if we already have a ticker for this contract
                ticker = self.contract_cache.get_ticker(leg_key)
                if ticker is None:
                    # Request market data for the leg
                    ticker = self.ib.reqMktData(leg.contract)
                    self.contract_cache.store_ticker(leg_key, ticker)
                
                # Wait briefly for quote
                self.ib.sleep(0.5)
                
                leg_quotes[leg_key] = {
                    'bid': ticker.bid,
                    'ask': ticker.ask,
                    'last': ticker.last,
                    'action': leg.action.value
                }
            
            return leg_quotes
            
        except Exception as e:
            logger.error(f"Error getting individual leg quotes: {str(e)}")
            return {}
    
    def _get_real_combo_quote(self, quote_request: ComboQuoteRequest) -> Dict[str, Optional[float]]:
        """Get real market quote for the complete combo"""
        try:
            # Build combo contract
            combo_contract = ComboOrderBuilder.build_combo_contract(
                quote_request.combo_definition,
                quote_request.symbol,
                quote_request.exchange,
                quote_request.currency
            )
            
            # Request market data for combo
            combo_ticker = self.ib.reqMktData(combo_contract)
            
            # Wait for quote (longer timeout for combo quotes)
            max_wait = self.config.connection.request_timeout
            wait_time = 0
            while wait_time < max_wait:
                self.ib.sleep(0.5)
                wait_time += 0.5
                
                if combo_ticker.bid is not None or combo_ticker.ask is not None:
                    break
            
            return {
                'bid': combo_ticker.bid,
                'ask': combo_ticker.ask,
                'last': combo_ticker.last
            }
            
        except Exception as e:
            logger.error(f"Error getting real combo quote: {str(e)}")
            return {'bid': None, 'ask': None, 'last': None}
    
    def _calculate_combo_from_legs(self, quote_request: ComboQuoteRequest, 
                                  leg_quotes: Dict[str, Dict[str, float]]) -> Optional[Dict[str, float]]:
        """Calculate combo quote from individual leg quotes (matches LiveQuoteManager logic)"""
        try:
            net_bid = 0.0
            net_ask = 0.0
            
            for leg in quote_request.combo_definition.legs:
                leg_key = f"{leg.strike}{leg.option_type.value}"
                leg_quote = leg_quotes.get(leg_key)
                
                if not leg_quote:
                    logger.warning(f"Missing quote for leg {leg_key}")
                    return None
                
                # Get bid/ask, default to 0 if not available
                bid = leg_quote['bid'] if leg_quote['bid'] and leg_quote['bid'] > 0 else 0.0
                ask = leg_quote['ask'] if leg_quote['ask'] and leg_quote['ask'] > 0 else 0.0
                
                if bid <= 0 or ask <= 0:
                    logger.warning(f"Invalid quote for leg {leg_key}: bid={bid}, ask={ask}")
                    return None
                
                # Calculate net based on leg action (same logic as LiveQuoteManager)
                if leg.action.value == "BUY":
                    net_bid -= ask * leg.ratio  # We pay ask when buying
                    net_ask -= bid * leg.ratio  # We get bid when buying
                else:  # SELL
                    net_bid += bid * leg.ratio  # We get bid when selling
                    net_ask += ask * leg.ratio  # We pay ask when selling
            
            logger.debug(f"Calculated combo quote: bid={net_bid:.2f}, ask={net_ask:.2f}")
            
            return {
                'bid': net_bid,
                'ask': net_ask
            }
            
        except Exception as e:
            logger.error(f"Error calculating combo from legs: {str(e)}")
            return None
    
    def get_middle_price(self, combo_definition: ComboDefinition, 
                        expiry_date: str = None) -> Optional[float]:
        """Get middle price for combo (bid + ask) / 2"""
        quote_result = self.get_combo_quote(combo_definition, expiry_date)
        return quote_result.mid_price
    
    def get_effective_price_for_order(self, combo_definition: ComboDefinition,
                                     order_side: str = "BUY",
                                     price_improvement: float = 0.05,
                                     expiry_date: str = None) -> Optional[float]:
        """Get effective price for placing an order with price improvement"""
        quote_result = self.get_combo_quote(combo_definition, expiry_date)
        
        if not quote_result.is_valid:
            return None
        
        # For credit strategies placed as BUY orders, we want to buy at the ask (receive credit)
        if combo_definition.is_credit_strategy:
            # Credit strategy: BUY order at ask price (we receive the credit)
            base_price = quote_result.ask
            if base_price is not None:
                return base_price + price_improvement  # Less negative = less credit but better fill chance
        else:
            # Debit strategy: BUY order at ask price (we pay the debit) 
            base_price = quote_result.ask
            if base_price is not None:
                return base_price + price_improvement
        
        return None
    
    def validate_quote_meets_requirements(self, quote_result: ComboQuoteResult) -> Tuple[bool, str]:
        """Validate that quote meets strategy requirements"""
        if not quote_result.is_valid:
            return False, quote_result.error_message or "Invalid quote"
        
        combo_type = quote_result.combo_definition.combo_type
        
        # Check credit requirements for credit strategies
        if quote_result.combo_definition.is_credit_strategy:
            credit = quote_result.get_credit_for_sale()
            if credit is None:
                return False, "No credit quote available"
            
            required_credit = self.config.get_effective_butterfly_credit(
                'narrow' if combo_type == ComboType.NARROW_IRON_BUTTERFLY else 'wide'
            )
            
            if credit < required_credit:
                return False, f"Credit ${credit:.2f} below required ${required_credit:.2f}"
        
        # Check debit limits for debit strategies
        else:
            debit = quote_result.get_debit_for_purchase()
            if debit is None:
                return False, "No debit quote available"
            
            max_debit = (self.config.pricing.narrow_spread_limit 
                        if 'narrow' in combo_type.value.lower()
                        else self.config.pricing.spread_price_limit)
            
            if debit > max_debit:
                return False, f"Debit ${debit:.2f} exceeds limit ${max_debit:.2f}"
        
        return True, "Quote meets requirements"
    
    def cleanup_expired_data(self):
        """Clean up expired contracts and quotes"""
        self.contract_cache.clear_expired()
        
        # Clear old quote cache entries
        cutoff = dt.datetime.now() - dt.timedelta(hours=1)
        expired_quote_keys = [
            key for key, timestamp in self.active_requests.items()
            if timestamp < cutoff
        ]
        
        for key in expired_quote_keys:
            self.active_requests.pop(key, None)
            self.quote_cache.pop(key, None)
    
    def get_quote_summary(self, quote_result: ComboQuoteResult) -> str:
        """Get human-readable summary of quote result"""
        if not quote_result.is_valid:
            return f"Quote Error: {quote_result.error_message}"
        
        combo_type = quote_result.combo_definition.combo_type.value
        
        if quote_result.combo_definition.is_credit_strategy:
            credit = quote_result.get_credit_for_sale()
            return f"{combo_type}: ${credit:.2f} credit"
        else:
            debit = quote_result.get_debit_for_purchase()
            return f"{combo_type}: ${debit:.2f} debit" 