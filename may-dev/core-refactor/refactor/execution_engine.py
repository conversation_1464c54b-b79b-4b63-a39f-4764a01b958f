#!/usr/bin/env python3
"""
Unified Execution Engine for Option Trades
"""

import logging
import datetime as dt
import time
import math
from typing import Dict, Optional, Tuple, Any, List
from ib_insync import IB, LimitOrder, MarketOrder

from combo_orders import ComboDefinition, ComboOrderBuilder, ComboOrderFactory, OptionPricingUtils, ComboType
from config import get_global_config
from execution import ExecutionCoordinator, OrderResult

logger = logging.getLogger(__name__)
detailed_logger = logging.getLogger('trading_detailed')
    """Result of order execution"""
    
    def __init__(self, success: bool, trade_id: Optional[str] = None, 
                 fill_price: Optional[float] = None, error_message: Optional[str] = None,
                 order_price: Optional[float] = None, order_status: Optional[str] = None,
                 ib_execution_id: Optional[str] = None, commission: Optional[float] = None,
                 strike_price: Optional[float] = None, buy_strike: Optional[float] = None, 
                 sell_strike: Optional[float] = None):
        self.success = success
        self.trade_id = trade_id
        self.fill_price = fill_price
        self.error_message = error_message
        self.execution_time = dt.datetime.now()
        
        # Additional data for database recording
        self.order_price = order_price  # The limit price we submitted
        self.order_status = order_status  # Final IB order status
        self.ib_execution_id = ib_execution_id  # IB execution ID if filled
        self.commission = commission  # Commission if available
        
        # Strike information
        self.strike_price = strike_price  # Main/center strike
        self.buy_strike = buy_strike      # Buy leg strike
        self.sell_strike = sell_strike    # Sell leg strike
    
    def __str__(self) -> str:
        if self.success:
            if self.fill_price:
                base = f"Order filled: ID {self.trade_id} @ ${self.fill_price:.2f}"
                if self.buy_strike and self.sell_strike:
                    base += f" ({self.buy_strike}/{self.sell_strike})"
                return base
            else:
                return f"Order placed: ID {self.trade_id} @ ${self.order_price:.2f} (GTD)"
        else:
            return f"Order failed: {self.error_message}"


class OptionExecutionEngine:
    """Unified execution engine for all option combo types"""
    
    def __init__(self, ib_connection: IB, db_handler=None):
        self.ib = ib_connection
        self.db = db_handler
        self.config = get_global_config()
        
        # Risk management parameters
        self.wallet_balance = 0.0  # Will be set from account balance
        
        # Track active orders
        self.active_orders: Dict[str, Any] = {}
        
        # Create quote engine for contract qualification
        from quote_engine import OptionQuoteEngine
        self.quote_engine = OptionQuoteEngine(ib_connection)
        
        # Initialize wallet balance
        self._initialize_wallet_balance()
        
        # Initialize modular execution coordinator with enhanced retry logic
        self.execution_coordinator = ExecutionCoordinator(ib_connection, self)
    
    def _initialize_wallet_balance(self):
        """Get current account balance and set wallet_balance"""
        try:
            # Get account summary
            account_summary = self.ib.accountSummary()
            
            # Find the net liquidation value
            for item in account_summary:
                if item.tag == 'NetLiquidation' and item.currency == 'USD':
                    self.wallet_balance = float(item.value)
                    logger.info(f"💰 Wallet Balance: ${self.wallet_balance:,.2f}")
                    detailed_logger.info(f"Account balance initialized: ${self.wallet_balance:,.2f}")
                    return
            
            logger.warning("Could not find USD NetLiquidation value in account summary")
            detailed_logger.warning("Failed to get wallet balance - using default 6000")
            self.wallet_balance = 6000.0  # Default fallback
            
        except Exception as e:
            logger.error(f"Error getting account balance: {str(e)}")
            detailed_logger.error(f"Wallet balance initialization failed: {str(e)}")
            self.wallet_balance = 6000.0  # Default fallback
    
    def get_risk_adjusted_order_size(self, limit_price: float, combo_definition: ComboDefinition) -> int:
        """Calculate order size based on risk allocation
        
        Args:
            limit_price: The limit price for the order
            combo_definition: The combo strategy definition
            
        Returns:
            Number of contracts to order
        """
        try:
            # Calculate risk budget from config based on strategy type
            risk_percentage = self.config.pricing.get_strategy_risk_percentage(combo_definition.combo_type.value)
            risk_budget = self.wallet_balance * (risk_percentage / 100.0)
            strategy_type = combo_definition.combo_type.value
            risk_type = "BULL" if risk_percentage == 8.0 else "OTHER"
            detailed_logger.info(f"💰 Risk Budget ({risk_type}): ${self.wallet_balance:,.2f} × {risk_percentage}% = ${risk_budget:.2f}")
            logger.info(f"📊 Using {risk_percentage}% risk for {strategy_type} strategy")
            
            # Calculate per-contract risk based on strategy type
            per_contract_risk = self._calculate_per_contract_risk(limit_price, combo_definition)
            detailed_logger.info(f"⚠️ Per-Contract Risk: ${per_contract_risk:.2f}")
            
            if per_contract_risk <= 0:
                logger.warning("Invalid per-contract risk calculation - using quantity 1")
                return 1
            
            # Calculate order size
            order_size = math.floor(risk_budget / per_contract_risk) # Floor operation
            
            # Ensure minimum order size of 1
            order_size = max(1, order_size)
            
            detailed_logger.info(f"📊 Order Size Calculation:")
            detailed_logger.info(f"   Risk Budget: ${risk_budget:.2f}")
            detailed_logger.info(f"   Per-Contract Risk: ${per_contract_risk:.2f}")
            detailed_logger.info(f"   Calculated Size: {risk_budget:.2f} ÷ {per_contract_risk:.2f} = {order_size}")
            
            logger.info(f"📊 Risk-Adjusted Order Size: {order_size} contracts (${per_contract_risk:.2f}/contract)")
            
            return order_size
            
        except Exception as e:
            logger.error(f"Error calculating risk-adjusted order size: {str(e)}")
            detailed_logger.error(f"Risk calculation failed: {str(e)}")
            return 1  # Default to 1 contract
    
    def _calculate_per_contract_risk(self, limit_price: float, combo_definition: ComboDefinition) -> float:
        """Calculate risk per contract based on strategy type
        
        Args:
            limit_price: The limit price for the order  
            combo_definition: The combo strategy definition
            
        Returns:
            Risk amount per contract in USD
        """
        if combo_definition.is_credit_strategy:
            # Credit strategy: Risk = wing_width - abs(credit_received)
            wing_width = self._get_wing_width(combo_definition)
            credit_received = abs(limit_price)
            max_loss = wing_width - credit_received
            per_contract_risk = max_loss * 100  # Multiply by 100 for contract multiplier
            
            detailed_logger.info(f"   Credit Strategy Risk: ({wing_width} - {credit_received:.2f}) × 100 = ${per_contract_risk:.2f}")
            
        else:
            # Debit strategy: Risk = premium paid
            per_contract_risk = abs(limit_price) * 100  # Multiply by 100 for contract multiplier
            
            detailed_logger.info(f"   Debit Strategy Risk: {abs(limit_price):.2f} × 100 = ${per_contract_risk:.2f}")
        
        return per_contract_risk
    
    def _get_wing_width(self, combo_definition: ComboDefinition) -> float:
        """Get the wing width for butterfly strategies
        
        Args:
            combo_definition: The combo strategy definition
            
        Returns:
            Wing width in points
        """
        # For butterfly strategies, calculate actual wing width from strikes
        if combo_definition.combo_type in [ComboType.IRON_BUTTERFLY, ComboType.WIDE_IRON_BUTTERFLY, ComboType.NARROW_IRON_BUTTERFLY]:
            strikes = combo_definition.get_strikes()
            if len(strikes) >= 2:
                # For iron butterflies, wing width is from center to outer strikes
                center_strike = combo_definition.center_strike
                max_strike = max(strikes)
                min_strike = min(strikes)
                
                # Wing width is the distance from center to the furthest strike
                wing_width = max(abs(max_strike - center_strike), abs(center_strike - min_strike))
                
                detailed_logger.info(f"🔧 Calculated wing width from actual strikes: {wing_width}")
                detailed_logger.info(f"   Center: {center_strike}, Min: {min_strike}, Max: {max_strike}")
                
                return wing_width
            else:
                # Fallback to type-based defaults
                if combo_definition.combo_type == ComboType.NARROW_IRON_BUTTERFLY:
                    detailed_logger.info(f"🔧 Fallback wing width for narrow butterfly: 5.0")
                    return 5.0
                else:
                    detailed_logger.info(f"🔧 Fallback wing width for wide butterfly: 10.0")
                    return 10.0
        else:
            # For other strategies (spreads), calculate from actual strikes
            strikes = combo_definition.get_strikes()
            if len(strikes) >= 2:
                width = max(strikes) - min(strikes)
                detailed_logger.info(f"🔧 Calculated spread width from actual strikes: {width}")
                return width
            else:
                detailed_logger.info(f"🔧 Default wing width: 5.0")
                return 5.0  # Default to 5 points
    
    def _get_quote_for_combo(self, combo_definition: ComboDefinition):
        """Get quote for combo to qualify option contracts"""
        return self.quote_engine.get_combo_quote(combo_definition)
    
    def execute_combo_order(self, combo_definition: ComboDefinition, 
                           limit_price: float, quantity: Optional[int] = None,
                           timeout_seconds: Optional[int] = None) -> OrderResult:
        """Execute any combo order with unified interface"""
        try:
            # Calculate risk-adjusted order size if not provided
            if quantity is None:
                quantity = self.get_risk_adjusted_order_size(limit_price, combo_definition)
            
            detailed_logger.info(f"=== STARTING ORDER EXECUTION ===")
            detailed_logger.info(f"Strategy: {combo_definition.combo_type.value}")
            detailed_logger.info(f"Description: {combo_definition.description}")
            detailed_logger.info(f"Limit Price: ${limit_price:.2f}")
            detailed_logger.info(f"Quantity: {quantity} (risk-adjusted)")
            detailed_logger.info(f"Is Credit Strategy: {combo_definition.is_credit_strategy}")
            
            logger.info(f"Executing {combo_definition.combo_type.value} order: "
                       f"${limit_price:.2f} x {quantity}")
            
            # First, get quotes to qualify the option contracts
            detailed_logger.info("Getting quotes to qualify option contracts...")
            quote_result = self._get_quote_for_combo(combo_definition)
            if not quote_result.is_valid:
                detailed_logger.error(f"Failed to get valid quote: {quote_result.error_message}")
                return OrderResult(success=False, error_message=f"Quote failed: {quote_result.error_message}")
            
            # Build the combo contract
            detailed_logger.info("Building combo contract...")
            combo_contract = ComboOrderBuilder.build_combo_contract(combo_definition)
            detailed_logger.info(f"Combo contract legs: {len(combo_contract.comboLegs)}")
            
            for i, leg in enumerate(combo_contract.comboLegs):
                detailed_logger.info(f"  Leg {i+1}: ConId {leg.conId}, Action {leg.action}, Ratio {leg.ratio}")
            
            # Create the order - Credit strategies use BUY orders to receive credit
            order_side = "BUY" if combo_definition.is_credit_strategy else "BUY"  # All combos use BUY
            detailed_logger.info(f"Order side: {order_side}")
            
            # Ensure the limit price is nickel-rounded before any other adjustments
            if not OptionPricingUtils.validate_nickel_price(limit_price):
                # If incoming price isn't nickel-rounded, fix it
                old_price = limit_price
                limit_price = OptionPricingUtils.round_to_nickel(
                    abs(limit_price), 
                    round_up=not combo_definition.is_credit_strategy
                )
                if combo_definition.is_credit_strategy:
                    limit_price = -limit_price  # Keep negative for credit strategies
                detailed_logger.info(f"🪙 Nickel correction: ${old_price:.2f} → ${limit_price:.2f}")
                logger.info(f"🪙 Rounded to nickel: ${old_price:.2f} → ${limit_price:.2f}")
            
            # Adjust price for live test mode if enabled
            strategy_type = combo_definition.combo_type.value
            effective_price = self.config.get_effective_order_price(limit_price, strategy_type)

            if effective_price != limit_price:
                detailed_logger.info(f"🧪 Live test mode adjustment: ${limit_price:.2f} -> ${effective_price:.2f}")
                logger.info(f"🧪 Live test mode: Using ${effective_price:.2f} instead of ${limit_price:.2f}")
            else:
                detailed_logger.info(f"Using calculated price: ${limit_price:.2f}")

            # Final nickel validation for the effective price
            if not OptionPricingUtils.validate_nickel_price(effective_price):
                detailed_logger.warning(f"⚠️ Final effective price ${effective_price:.2f} is not a nickel increment!")
                logger.warning(f"⚠️ Non-nickel price will be submitted: ${effective_price:.2f}")

            if combo_definition.is_credit_strategy:
                if effective_price > 0:
                    effective_price = -abs(effective_price)
            else:
                effective_price = abs(effective_price)
                        
            
            order = LimitOrder(order_side, quantity, effective_price)
            detailed_logger.info(f"Created limit order: {order_side} {quantity} @ ${effective_price:.2f}")
            
            # Set account on order if available in config
            if self.config.account_id:
                order.account = self.config.account_id
                detailed_logger.info(f"Set account on order: {self.config.account_id}")
            else:
                # Fallback to getting account from IB connection
                accounts = self.ib.managedAccounts()
                if accounts:
                    order.account = accounts[0]
                    detailed_logger.info(f"Set account on order (from IB): {accounts[0]}")
            
            # Place the order
            detailed_logger.info("Placing order with IB...")
            trade = self.ib.placeOrder(combo_contract, order)
            order_id = trade.order.orderId
            
            detailed_logger.info(f"Order placed successfully! Order ID: {order_id}")
            detailed_logger.info(f"Trade object status: {trade.orderStatus.status}")
            logger.info(f"Order placed: ID {order_id} for {combo_definition.description}")
            
            # Track the order
            self.active_orders[str(order_id)] = {
                'trade': trade,
                'combo_definition': combo_definition,
                'start_time': dt.datetime.now(),
                'timeout_seconds': timeout_seconds or self.config.orders.order_timeout_seconds
            }
            
            detailed_logger.info(f"Waiting for fill (timeout: {timeout_seconds or self.config.orders.order_timeout_seconds}s)...")
            
            # Wait for fill or timeout
            result = self._wait_for_fill(trade, timeout_seconds)
            
            # Enhance the result with additional data for database recording
            result.order_price = effective_price
            
            detailed_logger.info(f"Order result: Success={result.success}")
            if result.success:
                if result.fill_price:
                    detailed_logger.info(f"Fill price: ${result.fill_price:.2f}")
                detailed_logger.info(f"Trade ID: {result.trade_id}")
            else:
                detailed_logger.error(f"Order failed: {result.error_message}")
            
            # Clean up tracking
            self.active_orders.pop(str(order_id), None)
            
            # Record ALL orders to database - success, failure, test mode, everything
            if self.db:
                detailed_logger.info("Recording trade to database...")
                self._record_trade_to_database(combo_definition, result, limit_price, quantity)
            else:
                detailed_logger.warning("No database handler available - trade not recorded")
            
            detailed_logger.info(f"=== ORDER EXECUTION COMPLETE ===")
            return result
            
        except Exception as e:
            detailed_logger.error(f"=== ORDER EXECUTION FAILED ===")
            detailed_logger.error(f"Exception: {str(e)}")
            detailed_logger.error(f"Exception type: {type(e).__name__}")
            import traceback
            detailed_logger.error(f"Traceback:\n{traceback.format_exc()}")
            logger.error(f"Error executing combo order: {str(e)}")
            return OrderResult(success=False, error_message=str(e))
    
    def execute_bull_spread(self, current_price: float, quantity: Optional[int] = None, timeout_seconds: Optional[int] = None) -> OrderResult:
        """Execute bull call spread with smart strike selection and quote checking"""
        from combo_orders import StrikeCalculator, ComboOrderFactory
        
        logger.info(f"🎯 BULL SPREAD: Smart strike selection for SPX={current_price:.2f}")
        
        strike_pairs = StrikeCalculator.get_bull_strike_sequence(current_price)
        logger.info(f"📊 Strike sequence to try: {strike_pairs}")
        
        for attempt, (buy_strike, sell_strike) in enumerate(strike_pairs, 1):
            logger.info(f"🔍 Attempt {attempt}: Getting quote for {buy_strike}/{sell_strike}")
            
            combo_def = ComboOrderFactory.create_bull_spread_from_strikes(buy_strike, sell_strike)
            quote_result = self.quote_engine.get_combo_quote(combo_def)
            
            if quote_result.is_valid and quote_result.mid_price is not None:
                mid_price = quote_result.mid_price
                logger.info(f"💰 Quote: Bid={quote_result.bid:.2f}, Ask={quote_result.ask:.2f}, Mid={mid_price:.2f}")

                # Calculate limit price with buffer and nickel rounding
                limit_price = OptionPricingUtils.calculate_limit_price_with_buffer(
                    mid_price, 
                    is_credit_strategy=False, 
                    buffer=0.05
                )
                
                # Check if the calculated debit is within our configured limit
                if limit_price <= self.config.pricing.spread_price_limit:
                    logger.info(f"✅ SELECTED: {buy_strike}/{sell_strike} at ${limit_price:.2f}")
                    
                    # In live test mode, override with test price
                    if self.config.live_test.enabled:
                        actual_price = limit_price
                        limit_price = self.config.live_test.test_order_price
                        logger.info(f"🧪 LIVE TEST: Real price ${actual_price:.2f} → Test price ${limit_price:.2f}")
                    
                    result = self.execute_combo_order(combo_def, limit_price, quantity, timeout_seconds)
                    
                    # Add strike information to result
                    if result:
                        result.strike_price = buy_strike  # Use buy strike as main strike
                        result.buy_strike = buy_strike
                        result.sell_strike = sell_strike
                    
                    return result
                else:
                    logger.info(f"❌ REJECTED: Final price ${limit_price:.2f} > limit ${self.config.pricing.spread_price_limit:.2f}")
            else:
                logger.info(f"❌ INVALID QUOTE: {quote_result.error_message}")
        
        logger.error(f"❌ ALL ATTEMPTS FAILED: No suitable strikes found")
        return OrderResult(success=False, error_message="No suitable strikes within price limit")
    
    def execute_bear_spread(self, current_price: float, quantity: Optional[int] = None, timeout_seconds: Optional[int] = None) -> OrderResult:
        """Execute bear put spread with smart strike selection and quote checking"""
        from combo_orders import StrikeCalculator, ComboOrderFactory
        
        logger.info(f"🎯 BEAR SPREAD: Smart strike selection for SPX={current_price:.2f}")
        
        strike_pairs = StrikeCalculator.get_bear_strike_sequence(current_price)
        logger.info(f"📊 Strike sequence to try: {strike_pairs}")
        
        for attempt, (buy_strike, sell_strike) in enumerate(strike_pairs, 1):
            logger.info(f"🔍 Attempt {attempt}: Getting quote for {buy_strike}/{sell_strike}")
            
            combo_def = ComboOrderFactory.create_bear_spread_from_strikes(buy_strike, sell_strike)
            quote_result = self.quote_engine.get_combo_quote(combo_def)
            
            if quote_result.is_valid and quote_result.mid_price is not None:
                mid_price = quote_result.mid_price
                logger.info(f"💰 Quote: Bid={quote_result.bid:.2f}, Ask={quote_result.ask:.2f}, Mid={mid_price:.2f}")
                
                # Calculate limit price with buffer and nickel rounding
                limit_price = OptionPricingUtils.calculate_limit_price_with_buffer(
                    mid_price, 
                    is_credit_strategy=False, 
                    buffer=0.05
                )
                
                # Check if the calculated debit is within our configured limit
                if limit_price <= self.config.pricing.spread_price_limit:
                    logger.info(f"✅ SELECTED: {buy_strike}/{sell_strike} at ${limit_price:.2f}")
                    
                    # In live test mode, override with test price
                    if self.config.live_test.enabled:
                        actual_price = limit_price
                        limit_price = self.config.live_test.test_order_price
                        logger.info(f"🧪 LIVE TEST: Real price ${actual_price:.2f} → Test price ${limit_price:.2f}")
                    
                    result = self.execute_combo_order(combo_def, limit_price, quantity, timeout_seconds)
                    
                    # Add strike information to result
                    if result:
                        result.strike_price = buy_strike  # Use buy strike as main strike
                        result.buy_strike = buy_strike
                        result.sell_strike = sell_strike
                    
                    return result
                else:
                    logger.info(f"❌ REJECTED: Final price ${limit_price:.2f} > limit ${self.config.pricing.spread_price_limit:.2f}")
            else:
                logger.info(f"❌ INVALID QUOTE: {quote_result.error_message}")
        
        logger.error(f"❌ ALL ATTEMPTS FAILED: No suitable strikes found")
        return OrderResult(success=False, error_message="No suitable strikes within price limit")
    
    def execute_narrow_bull_spread(self, center_strike: float, quantity: Optional[int] = None) -> OrderResult:
        """Execute 'one better' narrow bull spread with lower price limit"""
        combo_def = ComboOrderFactory.create_one_better_narrow_bull(center_strike)
        limit_price = self.config.pricing.narrow_spread_limit
        
        # Ensure debit limit is nickel-rounded (round up for debit strategies)
        limit_price = OptionPricingUtils.round_to_nickel(limit_price, round_up=True)
        
        return self.execute_combo_order(combo_def, limit_price, quantity)
    
    def execute_narrow_bear_spread(self, center_strike: float, quantity: Optional[int] = None) -> OrderResult:
        """Execute 'one better' narrow bear spread with lower price limit"""
        combo_def = ComboOrderFactory.create_one_better_narrow_bear(center_strike)
        limit_price = self.config.pricing.narrow_spread_limit
        
        # Ensure debit limit is nickel-rounded (round up for debit strategies)
        limit_price = OptionPricingUtils.round_to_nickel(limit_price, round_up=True)
        
        return self.execute_combo_order(combo_def, limit_price, quantity)
    
    def execute_narrow_iron_butterfly(self, center_strike: float, quantity: int = 1) -> OrderResult:
        """Execute narrow iron butterfly"""
        logger.info(f"Executing narrow iron butterfly at strike {center_strike}")
        return OrderResult(success=True, trade_id="NARROW_BF_001")
    
    def execute_iron_butterfly(self, center_strike: float, quantity: Optional[int] = None) -> OrderResult:
        """Execute iron butterfly (narrow) with fixed pricing at minimum credit"""
        combo_def = ComboOrderFactory.create_narrow_iron_butterfly(center_strike)
        logger.info(f"🎯 NARROW IRON BUTTERFLY: Fixed pricing for {center_strike} strike")

        # Get quote for contract qualification only
        quote_result = self.quote_engine.get_combo_quote(combo_def)

        if not (quote_result.is_valid and quote_result.mid_price is not None):
            logger.error(f"❌ INVALID QUOTE for narrow iron butterfly: {quote_result.error_message}")
            return OrderResult(success=False, error_message="Invalid quote for narrow iron butterfly")

        mid_price = quote_result.mid_price
        logger.info(f"💰 Market Quote: Bid={quote_result.bid:.2f}, Ask={quote_result.ask:.2f}, Mid={mid_price:.2f}")

        # Use fixed pricing at minimum credit level - no dynamic adjustments
        min_credit_config = self.config.pricing.narrow_iron_butterfly_credit_min
        limit_price = -abs(min_credit_config)  # Negative for credit strategy
        
        logger.info(f"✅ USING FIXED PRICE: ${limit_price:.2f} (credit of ${abs(limit_price):.2f}) - will be left in book")
        
        # In live test mode, override with test price but log the real price
        if self.config.live_test.enabled:
            actual_price = limit_price
            limit_price = -abs(self.config.pricing.narrow_iron_butterfly_test_credit)  # Negative for credit
            logger.info(f"🧪 LIVE TEST: Real price ${actual_price:.2f} → Test price ${limit_price:.2f}")
        
        return self.execute_combo_order(combo_def, limit_price, quantity, timeout_seconds='leave_in_book')
    
    def execute_iron_butterfly_with_timeout(self, center_strike: float, 
                                          order_price: float, timeout_seconds: int) -> Tuple[bool, Dict]:
        """Execute iron butterfly with specific price and timeout"""
        combo_def = ComboOrderFactory.create_iron_butterfly(center_strike)
        result = self.execute_combo_order(combo_def, order_price, 1, timeout_seconds)
        
        fill_details = {
            'filled': result.success,
            'fill_time': (result.execution_time - dt.datetime.now()).total_seconds() if result.success else timeout_seconds,
            'fill_price': result.fill_price if result.success else None,
            'timeout_seconds': timeout_seconds,
            'error': result.error_message if not result.success else None
        }
        
        return result.success, fill_details
    
    def execute_wide_iron_butterfly(self, center_strike: float, quantity: Optional[int] = None) -> OrderResult:
        """Execute wide iron butterfly with dynamic pricing"""
        combo_def = ComboOrderFactory.create_wide_iron_butterfly(center_strike)
        logger.info(f"🎯 WIDE IRON BUTTERFLY: Dynamic pricing for {center_strike} strike")

        quote_result = self.quote_engine.get_combo_quote(combo_def)

        if not (quote_result.is_valid and quote_result.mid_price is not None):
            logger.error(f"❌ INVALID QUOTE for wide iron butterfly: {quote_result.error_message}")
            return OrderResult(success=False, error_message="Invalid quote for wide iron butterfly")

        mid_price = quote_result.mid_price
        logger.info(f"💰 Quote: Bid={quote_result.bid:.2f}, Ask={quote_result.ask:.2f}, Mid={mid_price:.2f}")

        # Calculate limit price with buffer (e.g., give up $0.05 of credit)
        limit_price = OptionPricingUtils.calculate_limit_price_with_buffer(
            mid_price,
            is_credit_strategy=True,
            buffer=0.05
        )

        # Safety check: ensure the calculated credit is still above our minimum
        min_credit_config = self.config.pricing.wide_iron_butterfly_credit_min
        if abs(limit_price) < min_credit_config:
            logger.warning(f"❌ REJECTED: Dynamic credit ${abs(limit_price):.2f} is below minimum of ${min_credit_config:.2f}")
            return OrderResult(success=False, error_message=f"Dynamic credit ${abs(limit_price):.2f} below min ${min_credit_config:.2f}")

        logger.info(f"✅ USING DYNAMIC PRICE: ${limit_price:.2f} (credit of ${abs(limit_price):.2f})")
        
        # In live test mode, override with test price but log the real price
        if self.config.live_test.enabled:
            actual_price = limit_price
            limit_price = -abs(self.config.pricing.wide_iron_butterfly_test_credit)  # Negative for credit
            logger.info(f"🧪 LIVE TEST: Real price ${actual_price:.2f} → Test price ${limit_price:.2f}")
        
        # Calculate risk-adjusted order size if not specified
        if quantity is None:
            quantity = self.get_risk_adjusted_order_size(limit_price, combo_def)
            logger.info(f"📊 Risk-Adjusted Order Size: {quantity} contracts")
        
        return self.execute_combo_order(combo_def, limit_price, quantity, timeout_seconds=9)
    
    def execute_one_better_narrow_bull(self, atm_strike: float, quantity: int = 1, execution_price: float = None) -> OrderResult:
        """Execute 'one better strike' narrow bull spread (GTD 15:58:59)"""
        # Get current SPX price for validation if not provided
        if execution_price is None:
            execution_price = self._get_current_spx_price()
        
        combo_def = ComboOrderFactory.create_one_better_narrow_bull(atm_strike, execution_price)
        limit_price = self.config.pricing.narrow_spread_limit
        
        # Calculate risk-adjusted order size for $2.00 spread
        if quantity == 1:  # Only use dynamic sizing when default quantity is passed
            quantity = self.get_risk_adjusted_order_size(limit_price, combo_def)
            logger.info(f"📊 Risk-Adjusted Order Size: {quantity} contracts")
        
        # Use special timeout handling for GTD orders
        return self.execute_combo_order(combo_def, limit_price, quantity, timeout_seconds='until_15_58_59')
    
    def execute_one_better_narrow_bear(self, atm_strike: float, quantity: int = 1, execution_price: float = None) -> OrderResult:
        """Execute 'one better strike' narrow bear spread (GTD 15:58:59)"""
        # Get current SPX price for validation if not provided
        if execution_price is None:
            execution_price = self._get_current_spx_price()
        
        combo_def = ComboOrderFactory.create_one_better_narrow_bear(atm_strike, execution_price)
        limit_price = self.config.pricing.narrow_spread_limit
        
        # Calculate risk-adjusted order size for $2.00 spread
        if quantity == 1:  # Only use dynamic sizing when default quantity is passed
            quantity = self.get_risk_adjusted_order_size(limit_price, combo_def)
            logger.info(f"📊 Risk-Adjusted Order Size: {quantity} contracts")
        
        # Use special timeout handling for GTD orders
        return self.execute_combo_order(combo_def, limit_price, quantity, timeout_seconds='until_15_58_59')
    
    def execute_overshoot_reversal_bull(self, target_strike: float, quantity: int = 1) -> OrderResult:
        """Execute overshoot reversal bull spread at $1.05"""
        combo_def = ComboOrderFactory.create_overshoot_reversal_bull(target_strike)
        limit_price = self.config.pricing.overshoot_reversal_limit
        
        return self.execute_combo_order(combo_def, limit_price, quantity)
    
    def execute_overshoot_reversal_bear(self, target_strike: float, quantity: int = 1) -> OrderResult:
        """Execute overshoot reversal bear spread at $1.05"""
        combo_def = ComboOrderFactory.create_overshoot_reversal_bear(target_strike)
        limit_price = self.config.pricing.overshoot_reversal_limit
        
        return self.execute_combo_order(combo_def, limit_price, quantity)
    
    def _wait_for_fill(self, trade, timeout_seconds: Optional[int] = None) -> OrderResult:
        """Wait for order to fill or timeout"""
        # Handle special timeout cases
        if timeout_seconds == 'leave_in_book':
            # For orders that should stay in book (narrow butterfly)
            detailed_logger.info(f"Leave-in-book order: Waiting only for submission confirmation")
            
            start_time = time.time()
            submission_timeout = 10  # Only wait 10 seconds for submission
            
            detailed_logger.info(f"--- Waiting for submission confirmation on Leave-in-Book Order {trade.order.orderId} ---")
            detailed_logger.info(f"Submission timeout: {submission_timeout} seconds")
            detailed_logger.info(f"Initial status: {trade.orderStatus.status if hasattr(trade, 'orderStatus') else 'Unknown'}")
            
            last_status = None
            
            # Wait only for order to be submitted, then leave it in book
            while (time.time() - start_time) < submission_timeout:
                self.ib.sleep(0.5)
                elapsed = time.time() - start_time
                
                # Log status updates
                if hasattr(trade, 'orderStatus') and trade.orderStatus:
                    current_status = trade.orderStatus.status
                    if current_status != last_status:
                        detailed_logger.info(f"Status change: {last_status} -> {current_status} (elapsed: {elapsed:.1f}s)")
                        last_status = current_status
                        
                        # Once submitted, we're done - leave order active
                        if current_status in ['Submitted', 'PreSubmitted']:
                            detailed_logger.info(f"✅ Order successfully submitted and left in book")
                            detailed_logger.info(f"Order will remain active until filled or manually cancelled")
                            logger.info(f"✅ Order {trade.order.orderId} submitted and left in order book")
                            
                            return OrderResult(
                                success=True,
                                trade_id=str(trade.order.orderId),
                                fill_price=None,  # No fill yet, just submitted
                                order_status=current_status
                            )
            
            # If we get here, submission failed
            detailed_logger.error(f"❌ Leave-in-book order failed to submit within {submission_timeout}s")
            logger.error(f"Order {trade.order.orderId} failed to submit")
            
            final_status = trade.orderStatus.status if hasattr(trade, 'orderStatus') and trade.orderStatus else "Unknown"
            return OrderResult(
                success=False,
                trade_id=str(trade.order.orderId),
                error_message=f"Order failed to submit within {submission_timeout} seconds",
                order_status=final_status
            )
        
        elif timeout_seconds == 'until_15_58_59':
            # For GTD orders, we only wait for submission confirmation, not fill
            detailed_logger.info(f"GTD order: Waiting only for submission confirmation, not fill")
            
            start_time = time.time()
            submission_timeout = 10  # Only wait 10 seconds for submission
            
            detailed_logger.info(f"--- Waiting for submission confirmation on GTD Order {trade.order.orderId} ---")
            detailed_logger.info(f"Submission timeout: {submission_timeout} seconds")
            detailed_logger.info(f"Initial status: {trade.orderStatus.status if hasattr(trade, 'orderStatus') else 'Unknown'}")
            
            last_status = None
            
            # Wait only for order to be submitted, not filled
            while (time.time() - start_time) < submission_timeout:
                self.ib.sleep(0.5)
                elapsed = time.time() - start_time
                
                # Log status updates
                if hasattr(trade, 'orderStatus') and trade.orderStatus:
                    current_status = trade.orderStatus.status
                    if current_status != last_status:
                        detailed_logger.info(f"Status change: {last_status} -> {current_status} (elapsed: {elapsed:.1f}s)")
                        last_status = current_status
                        
                        # Once submitted, we're done - don't wait for fill
                        if current_status in ['Submitted', 'PreSubmitted']:
                            detailed_logger.info(f"✅ GTD order successfully submitted")
                            detailed_logger.info(f"Order will remain active until 15:58:59")
                            logger.info(f"✅ GTD Order {trade.order.orderId} submitted successfully - will remain active until 15:58:59")
                            
                            return OrderResult(
                                success=True,
                                trade_id=str(trade.order.orderId),
                                fill_price=None,  # No fill yet, just submitted
                                order_status=current_status
                            )
            
            # If we get here, submission failed
            detailed_logger.error(f"❌ GTD order failed to submit within {submission_timeout}s")
            logger.error(f"GTD Order {trade.order.orderId} failed to submit")
            
            final_status = trade.orderStatus.status if hasattr(trade, 'orderStatus') and trade.orderStatus else "Unknown"
            return OrderResult(
                success=False,
                trade_id=str(trade.order.orderId),
                error_message=f"GTD order failed to submit within {submission_timeout} seconds",
                order_status=final_status
            )
        
        # Regular order handling (wait for fill)
        timeout = timeout_seconds or self.config.orders.order_timeout_seconds
        start_time = time.time()
        
        detailed_logger.info(f"--- Waiting for fill on Order {trade.order.orderId} ---")
        detailed_logger.info(f"Timeout: {timeout:.0f} seconds")
        detailed_logger.info(f"Initial status: {trade.orderStatus.status if hasattr(trade, 'orderStatus') else 'Unknown'}")
        
        last_status = None
        status_count = 0
        
        while not trade.isDone() and (time.time() - start_time) < timeout:
            self.ib.sleep(0.5)
            status_count += 1
            elapsed = time.time() - start_time
            
            # Log status updates
            if hasattr(trade, 'orderStatus') and trade.orderStatus:
                current_status = trade.orderStatus.status
                if current_status != last_status:
                    detailed_logger.info(f"Status change: {last_status} -> {current_status} (elapsed: {elapsed:.1f}s)")
                    last_status = current_status
                    
                # Log periodic updates
                if status_count % 10 == 0:  # Every 5 seconds
                    detailed_logger.info(f"Still waiting... Status: {current_status}, Elapsed: {elapsed:.1f}s")
                    
                logger.debug(f"Order {trade.order.orderId} status: {current_status}")
        
        elapsed_total = time.time() - start_time
        detailed_logger.info(f"Wait loop completed. Total elapsed: {elapsed_total:.1f}s")
        detailed_logger.info(f"Trade done status: {trade.isDone()}")
        
        # Check final status
        if trade.isDone():
            detailed_logger.info("Trade is done, checking fill status...")
            
            if hasattr(trade, 'fills') and trade.fills:
                # Order filled - extract all execution details
                detailed_logger.info(f"Order has {len(trade.fills)} fills")
                fill = trade.fills[0]
                fill_price = fill.execution.price
                
                # Extract execution details
                execution_id = fill.execution.execId if hasattr(fill.execution, 'execId') else None
                commission = None
                if hasattr(trade, 'commissionReport') and trade.commissionReport:
                    commission = trade.commissionReport.commission
                
                detailed_logger.info(f"✅ Fill details: Price=${fill_price:.2f}, Qty={fill.execution.shares}")
                detailed_logger.info(f"Fill time: {fill.execution.time}")
                if execution_id:
                    detailed_logger.info(f"Execution ID: {execution_id}")
                if commission:
                    detailed_logger.info(f"Commission: ${commission:.2f}")
                
                logger.info(f"✅ Order {trade.order.orderId} filled at ${fill_price:.2f}")
                
                return OrderResult(
                    success=True,
                    trade_id=str(trade.order.orderId),
                    fill_price=fill_price,
                    order_status="Filled",
                    ib_execution_id=execution_id,
                    commission=commission
                )
            else:
                # Order completed but not filled (cancelled, etc.)
                status = trade.orderStatus.status if hasattr(trade, 'orderStatus') else "Unknown"
                detailed_logger.error(f"❌ Order completed but not filled")
                detailed_logger.error(f"Final status: {status}")
                detailed_logger.error(f"Has fills: {hasattr(trade, 'fills')}")
                detailed_logger.error(f"Fills list: {trade.fills if hasattr(trade, 'fills') else 'No fills attribute'}")
                
                logger.warning(f"Order {trade.order.orderId} completed but not filled: {status}")
                
                return OrderResult(
                    success=False,
                    trade_id=str(trade.order.orderId),
                    error_message=f"Order not filled: {status}",
                    order_status=status
                )
        else:
            # Timeout - cancel the order
            status = trade.orderStatus.status if hasattr(trade, 'orderStatus') else "Unknown"
            detailed_logger.warning(f"⏰ Order timed out after {timeout}s")
            detailed_logger.warning(f"Final status: {status}")
            logger.warning(f"⏰ Order {trade.order.orderId} timed out after {timeout}s, cancelling")
            
            try:
                detailed_logger.info("Attempting to cancel order...")
                self.ib.cancelOrder(trade.order)
                detailed_logger.info("Cancel request sent successfully")
                logger.info(f"🚫 Order {trade.order.orderId} cancelled due to timeout")
            except Exception as e:
                detailed_logger.error(f"Error during cancellation: {str(e)}")
                logger.error(f"Error cancelling order: {str(e)}")
            
            return OrderResult(
                success=False,
                trade_id=str(trade.order.orderId),
                error_message=f"Order timed out after {timeout} seconds",
                order_status=status
            )
    
    def _get_current_spx_price(self):
        """Get current SPX price from IB connection"""
        try:
            from ib_insync import Index
            spx_contract = Index('SPX', 'CBOE', 'USD')
            
            # Get current ticker data
            ticker = self.ib.reqMktData(spx_contract, '', False, False)
            self.ib.sleep(0.5)  # Give it a moment to populate
            
            if ticker and ticker.last and ticker.last > 0:
                self.ib.cancelMktData(spx_contract)  # Clean up
                return ticker.last
            elif ticker and ticker.close and ticker.close > 0:
                self.ib.cancelMktData(spx_contract)  # Clean up
                return ticker.close
            else:
                self.ib.cancelMktData(spx_contract)  # Clean up
                return None
                
        except Exception as e:
            detailed_logger.warning(f"Could not get SPX price: {str(e)}")
            return None
    
    def _get_account_info(self):
        """Get dynamic account information based on connected account"""
        try:
            # Get account from IB connection
            accounts = self.ib.managedAccounts()
            if accounts:
                account_id = accounts[0]  # Use first account
                
                # Map account ID to account name
                if account_id == '*********':
                    account_name = 'coupledlogic'
                elif account_id == '*********':
                    account_name = 'phaedrustrader'
                elif account_id == '*********':
                    account_name = 'jerryzhang'       # Main trading account
                elif account_id == '*********':
                    account_name = 'jerryzhang_test'  # Test account
                elif account_id == '********':
                    account_name = 'lucyzhang'        # Lucy Zhang account
                else:
                    account_name = None
                    
                return account_id, account_name
            else:
                detailed_logger.warning("No managed accounts found - using fallback values")
                return None, None
                
        except Exception as e:
            detailed_logger.error(f"Error getting account info: {str(e)}")
            return None, None
    
    def _record_trade_to_database(self, combo_definition: ComboDefinition, 
                                 order_result: OrderResult, limit_price: float, quantity: int = 1):
        """Record ALL trade attempts to database - successful, failed, test mode, everything"""
        try:
            if not self.db:
                detailed_logger.warning("No database handler - skipping database recording")
                return
            
            # Determine status based on order result
            if order_result.success:
                if order_result.fill_price is not None:
                    status = 'filled'  # Actually filled
                else:
                    status = 'pending'  # GTD order placed but not filled yet
            else:
                status = 'failed'  # Order failed or cancelled
            
            # Calculate proper expiry (next Friday for weekly SPX)
            today = dt.datetime.now()
            days_ahead = 4 - today.weekday()  # Friday is 4
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
            expiry_date = today + dt.timedelta(days_ahead)
            
            # Get dynamic account information
            account_id, account_name = self._get_account_info()
            
            # Build comprehensive trade data
            trade_data = {
                'account_id': account_id,
                'account_name': account_name,
                'contract_id': combo_definition.center_strike,  # Use strike as contract ID placeholder
                'symbol': self.config.orders.option_symbol,  # SPX
                'expiry': expiry_date.strftime('%Y%m%d'),  # Proper expiry calculation
                'strike': float(combo_definition.center_strike),
                'right': self._get_database_right_code(combo_definition),  # P/C/B
                'order_type': 'LIMIT',
                'quantity': quantity,
                'entry_price': order_result.fill_price,  # Will be None for GTD orders
                'exit_price': None,  # Always None for new trades
                'entry_time': order_result.execution_time.strftime("%Y-%m-%d %H:%M:%S"),
                'exit_time': None,  # Always None for new trades
                'status': status,  # filled/pending/failed
                'pnl': None,  # Always None for new trades
                'commission': order_result.commission,  # Will be None for most test trades
                'order_id': int(order_result.trade_id) if order_result.trade_id and order_result.trade_id.isdigit() else None,
                'exit_order_id': None,  # Always None for new trades
                'ib_execution_id': order_result.ib_execution_id,  # IB execution ID if available
                'notes': self._create_enhanced_database_notes(combo_definition, order_result, limit_price)
            }
            
            detailed_logger.info(f"Recording to database: Status={status}, Strike={combo_definition.center_strike}, OrderID={order_result.trade_id}")
            
            # Try to get trade report content and record with report if available
            trade_report_content = self._get_current_trade_report()
            if trade_report_content:
                trade_id = self.db.record_trade_with_report(trade_data, trade_report_content)
                if trade_id:
                    logger.info(f"📊 Trade recorded in database with ID: {trade_id} (Status: {status}) + Trade Report")
                    detailed_logger.info(f"Database record created successfully: ID {trade_id} with trade report ({len(trade_report_content)} chars)")
                else:
                    logger.warning("Database record creation with trade report returned no ID")
            else:
                # Fallback to regular recording without trade report
                trade_id = self.db.record_trade(trade_data)
                if trade_id:
                    logger.info(f"📊 Trade recorded in database with ID: {trade_id} (Status: {status})")
                    detailed_logger.info(f"Database record created successfully: ID {trade_id}")
                    
                    # Try to update with trade report afterwards if it becomes available
                    self._schedule_trade_report_update(trade_id)
                else:
                    logger.warning("Database record creation returned no ID")
                
        except Exception as e:
            logger.error(f"Error recording trade to database: {str(e)}")
            detailed_logger.error(f"Database recording failed: {str(e)}")
            import traceback
            detailed_logger.error(f"Database error traceback:\n{traceback.format_exc()}")
    
    def _get_current_trade_report(self) -> Optional[str]:
        """Get current trade report content if available"""
        # This method is now deprecated - trade report should be passed directly
        # to record_trade_report_to_database by the strategy
        detailed_logger.debug("Trade report should be provided by strategy, not retrieved here")
        return None
    
    def _schedule_trade_report_update(self, trade_id: int):
        """Schedule an update to add trade report to existing trade record"""
        try:
            # This could be implemented as a background task or called after session ends
            # For now, we'll just log that it should be updated
            detailed_logger.info(f"Trade ID {trade_id} scheduled for trade report update")
            
            # Store the trade ID for later update (you could implement this as needed)
            if not hasattr(self, '_pending_report_updates'):
                self._pending_report_updates = []
            self._pending_report_updates.append(trade_id)
            
        except Exception as e:
            detailed_logger.warning(f"Could not schedule trade report update for trade {trade_id}: {str(e)}")
    
    def update_pending_trade_reports(self):
        """Update any pending trades with trade reports"""
        try:
            if not hasattr(self, '_pending_report_updates') or not self._pending_report_updates:
                return
            
            trade_report_content = self._get_current_trade_report()
            if not trade_report_content:
                detailed_logger.debug("No trade report content available for pending updates")
                return
            
            for trade_id in self._pending_report_updates:
                try:
                    success = self.db.update_trade_report(trade_id, trade_report_content)
                    if success:
                        logger.info(f"📊 Updated trade {trade_id} with trade report")
                        detailed_logger.info(f"Trade report added to existing trade {trade_id}")
                    else:
                        logger.warning(f"Failed to update trade {trade_id} with trade report")
                except Exception as e:
                    logger.error(f"Error updating trade {trade_id} with trade report: {str(e)}")
            
            # Clear the pending updates
            self._pending_report_updates.clear()
            
        except Exception as e:
            logger.error(f"Error updating pending trade reports: {str(e)}")
    
    def record_trade_report_to_database(self, report_content: str, strategy_state):
        """Record the complete trade report to trade_reports table"""
        try:
            if not self.db:
                detailed_logger.warning("No database handler - skipping trade report recording")
                return
                
            detailed_logger.info("Recording trade report to database...")
            
            # Get account info
            account_id, account_name = self._get_account_info()
            
            # Extract data from strategy state
            session_start = strategy_state.session_start_time
            session_end = strategy_state.session_end_time or dt.datetime.now()
            duration = (session_end - session_start).total_seconds() if session_start else None
            
            # Count events and extract key metrics  
            total_events = len(strategy_state.decision_history) + len(strategy_state.overshoot_executions)
            if strategy_state.trade_execution:
                total_events += 1  # Add main trade execution
            
            # Get final decision and execution info
            final_decision = strategy_state.current_decision.value if strategy_state.current_decision else None
            trade_executed = strategy_state.is_trade_executed()
            execution_status = None
            primary_strike = None
            execution_price = None
            retry_attempts = 0
            error_message = None
            
            if strategy_state.trade_execution:
                execution_status = strategy_state.trade_execution.execution_status.value
                primary_strike = strategy_state.trade_execution.strike_price
                execution_price = strategy_state.trade_execution.execution_price
                retry_attempts = strategy_state.trade_execution.retry_count
                error_message = strategy_state.trade_execution.error_message
                
                detailed_logger.info(f"🔍 Trade execution data: Status={execution_status}, Strike={primary_strike}, Price={execution_price}, Retries={retry_attempts}")
            else:
                detailed_logger.info("🔍 No trade execution found in strategy state")
            
            # Get span and bar data
            span3_value = None
            close_percentage = None
            bar_count = 0
            
            # Count bars FIRST
            if strategy_state.first_bar: bar_count += 1
            if strategy_state.second_bar: bar_count += 1  
            if strategy_state.third_bar: bar_count += 1
            
            # Get SPAN3 value
            if hasattr(strategy_state, 'span3') and strategy_state.span3:
                span3_value = strategy_state.span3.span_points
                
                # Get close percentage from latest bar if available
                latest_bar = strategy_state.get_latest_bar()
                if latest_bar:
                    close_percentage = strategy_state.span3.get_close_percentage(latest_bar.close)
            
            detailed_logger.info(f"📊 Extracted metrics: SPAN3={span3_value}, Close%={close_percentage}, Bars={bar_count}")
            
            # Determine trading mode
            trading_mode = 'TEST' if self.config.live_test.enabled else 'LIVE'
            
            # Build report data
            report_data = {
                'account_id': account_id,
                'account_name': account_name,
                'session_date': session_start.date() if session_start else dt.date.today(),
                'session_start_time': session_start,
                'session_end_time': session_end,
                'session_duration_seconds': int(duration) if duration else None,
                'trading_mode': trading_mode,
                'final_decision': final_decision,
                'trade_executed': trade_executed,
                'execution_status': execution_status,
                'primary_strike': primary_strike,
                'execution_price': execution_price,
                'retry_attempts': retry_attempts,
                'bar_count': bar_count,
                'span3_value': span3_value,
                'close_percentage': close_percentage,
                'total_events': total_events,
                'error_message': error_message[:500] if error_message else None,  # Truncate if too long
                'report_content': report_content,
                'algorithm_version': 'SPX New Algorithm v3.0'
            }
            
            detailed_logger.info(f"Trade report data prepared: Mode={trading_mode}, Decision={final_decision}, Events={total_events}")
            
            # Record to database
            report_id = self.db.record_trade_report(report_data)
            if report_id:
                logger.info(f"📋 Trade report recorded in database with ID: {report_id} ({trading_mode} mode)")
                detailed_logger.info(f"Trade report database record created: ID {report_id}")
                return report_id
            else:
                logger.warning("Failed to record trade report to database")
                detailed_logger.warning("Trade report database recording returned no ID")
                return None
                
        except Exception as e:
            logger.error(f"Error recording trade report to database: {str(e)}")
            detailed_logger.error(f"Trade report database recording failed: {str(e)}")
            import traceback
            detailed_logger.error(f"Trade report database error traceback:\n{traceback.format_exc()}")
            return None
    
    def _get_database_right_code(self, combo_definition: ComboDefinition) -> str:
        """Get single character code for database - P/C/B as requested"""
        combo_type = combo_definition.combo_type.value.lower()
        
        if 'iron_butterfly' in combo_type or 'straddle' in combo_type:
            return 'B'  # Both (puts and calls)
        elif 'bull' in combo_type:
            return 'C'  # Call spread  
        elif 'bear' in combo_type:
            return 'P'  # Put spread
        else:
            return 'B'  # Default to Both for complex strategies
    
    def _create_enhanced_database_notes(self, combo_definition: ComboDefinition, 
                              order_result: OrderResult, limit_price: float) -> str:
        """Create detailed notes for database with all available information"""
        notes_parts = []
        
        # SPX price at execution time
        try:
            spx_price = self._get_current_spx_price()
            if spx_price:
                notes_parts.append(f"SPX Price: ${spx_price:.2f}")
        except:
            pass  # Don't fail the whole recording if SPX price unavailable
        
        # Strategy information with decision label
        strategy_name = combo_definition.combo_type.value.replace('_', ' ').title()
        notes_parts.append(f"Decision: {strategy_name}")
        notes_parts.append(f"Strategy: {combo_definition.description}")
        
        # Detailed strike information with Long/Short positions
        strike_details = []
        for leg in combo_definition.legs:
            position_type = "Long" if leg.action.value == "BUY" else "Short"
            option_type_full = "Call" if leg.option_type.value == "C" else "Put"
            strike_details.append(f"{position_type} {leg.strike} {option_type_full}")
        notes_parts.append(f"Strikes: {' / '.join(strike_details)}")
        
        # Price information
        notes_parts.append(f"Limit Price: ${limit_price:.2f}")
        if order_result.order_price and order_result.order_price != limit_price:
            notes_parts.append(f"Effective Price: ${order_result.order_price:.2f}")
        
        # Fill information
        if order_result.fill_price:
            notes_parts.append(f"Fill Price: ${order_result.fill_price:.2f}")
        else:
            notes_parts.append("Fill Price: Pending")
        
        # Execution details
        notes_parts.append(f"Execution Time: {order_result.execution_time.strftime('%H:%M:%S')}")
        
        # Order status
        if order_result.order_status:
            notes_parts.append(f"Order Status: {order_result.order_status}")
        
        # Test mode indicator
        if self.config.live_test.enabled:
            notes_parts.append("🧪 LIVE TEST MODE")
        
        # Error information if failed
        if not order_result.success and order_result.error_message:
            notes_parts.append(f"Error: {order_result.error_message}")
        
        # IB execution details
        if order_result.ib_execution_id:
            notes_parts.append(f"IB Execution ID: {order_result.ib_execution_id}")
        
        return " | ".join(notes_parts)
    
    def cancel_all_orders(self) -> int:
        """Cancel all active orders"""
        cancelled_count = 0
        
        for order_id, order_info in list(self.active_orders.items()):
            try:
                trade = order_info['trade']
                if not trade.isDone():
                    self.ib.cancelOrder(trade.order)
                    cancelled_count += 1
                    logger.info(f"Cancelled order {order_id}")
            except Exception as e:
                logger.error(f"Error cancelling order {order_id}: {str(e)}")
        
        self.active_orders.clear()
        return cancelled_count
    
    def get_active_order_count(self) -> int:
        """Get number of active orders"""
        return len(self.active_orders)
    
    def get_order_status(self, order_id: str) -> Optional[str]:
        """Get status of specific order"""
        order_info = self.active_orders.get(order_id)
        if order_info and hasattr(order_info['trade'], 'orderStatus'):
            return order_info['trade'].orderStatus.status
        return None
    
    def cancel_narrow_orders(self) -> int:
        """Cancel narrow bull/bear orders specifically"""
        cancelled_count = 0
        
        for order_id, order_info in list(self.active_orders.items()):
            try:
                combo_def = order_info['combo_definition']
                if combo_def.combo_type.value in ['one_better_narrow_bull', 'one_better_narrow_bear']:
                    trade = order_info['trade']
                    if not trade.isDone():
                        self.ib.cancelOrder(trade.order)
                        cancelled_count += 1
                        logger.info(f"Cancelled narrow order {order_id}")
            except Exception as e:
                logger.error(f"Error cancelling narrow order {order_id}: {str(e)}")
        
        return cancelled_count 

    def execute_combo_order_with_enhanced_retries(self, combo_definition: ComboDefinition, 
                                                limit_price: float, quantity: Optional[int] = None,
                                                timeout_seconds: Optional[int] = None) -> OrderResult:
        """
        Execute combo order with enhanced retry logic according to plan:
        1. Initial start at 15:51:36
        2. Wait 3 seconds, requote and recalculate
        3. If more aggressive (higher price or better strikes), cancel and resubmit
        4. Do this recalculation 3 times if necessary
        5. If not filled 3 seconds after last submission, cancel and submit conservative order
        """
        import random
        import time as time_module
        
        # Initial start time is fixed at 15:51:36
        initial_start_second = self.config.timing.retry_initial_start_second
        logger.info(f"🎯 Enhanced retry execution starting at 15:51:{initial_start_second:02d}")
        
        # Calculate order size if not provided
        if quantity is None:
            quantity = self.get_risk_adjusted_order_size(limit_price, combo_definition)
        
        # Store the very first order (most conservative) for fallback
        first_order_result = None
        first_order_price = limit_price
        first_order_combo = combo_definition
        
        # Track all active orders for cancellation
        active_order_ids = []
        
        try:
            # (b) Get initial quote and submit initial order
            logger.info(f"🚀 Initial execution attempt at 15:51:{initial_start_second:02d}")
            initial_result = self.execute_combo_order(combo_definition, limit_price, quantity, timeout_seconds)
            
            # Store the very first order for fallback
            first_order_result = initial_result
            first_order_price = limit_price
            first_order_combo = combo_definition
            
            if initial_result.success:
                logger.info("✅ Initial execution successful!")
                return initial_result
            
            # Store initial order ID if available
            if hasattr(initial_result, 'trade_id') and initial_result.trade_id:
                active_order_ids.append(initial_result.trade_id)
            
            # (c) Enhanced retry loop with 3 recalculation attempts
            for retry_attempt in range(self.config.timing.max_retry_attempts):
                logger.info(f"🔄 Recalculation attempt {retry_attempt + 1}/{self.config.timing.max_retry_attempts}")
                
                # Wait 3 seconds
                logger.info(f"⏳ Waiting {self.config.timing.retry_interval_seconds} seconds...")
                time_module.sleep(self.config.timing.retry_interval_seconds)
                
                # Get updated market data
                current_spx_price = self._get_current_spx_price()
                if current_spx_price is None:
                    logger.warning("⚠️ Could not get current SPX price for retry")
                    continue
                
                logger.info(f"📊 Updated SPX price: {current_spx_price:.2f}")
                
                # Re-quote and recalculate
                updated_combo_def, updated_limit_price = self._optimize_combo_for_retry(
                    combo_definition, current_spx_price, limit_price
                )
                
                # Check if new price is more aggressive (higher) or strike is more aggressive
                should_resubmit = self._should_resubmit_with_optimization(
                    updated_limit_price, limit_price, updated_combo_def, combo_definition
                )
                
                if should_resubmit:
                    logger.info(f"🎯 Resubmitting with optimized parameters:")
                    logger.info(f"   Original price: ${limit_price:.2f} -> New price: ${updated_limit_price:.2f}")
                    if hasattr(updated_combo_def, 'center_strike') and hasattr(combo_definition, 'center_strike'):
                        logger.info(f"   Original strike: {combo_definition.center_strike} -> New strike: {updated_combo_def.center_strike}")
                    
                    # Cancel existing orders
                    self._cancel_active_orders(active_order_ids)
                    active_order_ids.clear()
                    
                    # Submit new order with optimized parameters
                    retry_result = self.execute_combo_order(updated_combo_def, updated_limit_price, quantity, timeout_seconds)
                    
                    if retry_result.success:
                        logger.info(f"✅ Recalculation {retry_attempt + 1} successful!")
                        return retry_result
                    
                    # Store new order ID
                    if hasattr(retry_result, 'trade_id') and retry_result.trade_id:
                        active_order_ids.append(retry_result.trade_id)
                else:
                    logger.info("📊 No optimization needed - continuing with existing order")
            
            # (c) If not filled 3 seconds after last submission, cancel order and submit the very first order
            logger.info(f"⏳ Waiting {self.config.timing.final_wait_seconds} seconds after last submission...")
            time_module.sleep(self.config.timing.final_wait_seconds)
            
            # Check if any order was filled during the process
            final_check_result = self._check_for_filled_orders(active_order_ids)
            if final_check_result and final_check_result.success:
                logger.info("✅ Order filled during enhanced retry process!")
                return final_check_result
            
            # Cancel all orders and submit the very first order (most conservative)
            logger.info("🔄 All recalculation attempts failed - submitting very first order")
            self._cancel_active_orders(active_order_ids)
            
            # Submit the very first order and leave it in the book
            if first_order_result:
                logger.info(f"📋 Submitting very first order at ${first_order_price:.2f}")
                fallback_result = self.execute_combo_order(
                    first_order_combo, first_order_price, quantity, 
                    timeout_seconds=None  # No timeout - leave in book
                )
                
                if fallback_result.success:
                    logger.info("✅ Very first order submitted successfully")
                    return fallback_result
                else:
                    logger.error("❌ Very first order submission failed")
                    return fallback_result
            else:
                logger.error("❌ No very first order available for fallback")
                return OrderResult(success=False, error_message="No very first order available for fallback")
                
        except Exception as e:
            logger.error(f"❌ Error in enhanced retry execution: {str(e)}")
            # Cancel any remaining orders
            self._cancel_active_orders(active_order_ids)
            return OrderResult(success=False, error_message=f"Enhanced retry execution failed: {str(e)}")
    
    def _optimize_combo_for_retry(self, original_combo: ComboDefinition, 
                                 current_spx_price: float, original_limit_price: float) -> Tuple[ComboDefinition, float]:
        """
        Optimize combo definition and limit price based on current market conditions
        
        Returns:
            Tuple of (optimized_combo_definition, optimized_limit_price)
        """
        from combo_orders import ComboOrderFactory
        
        # Get current ATM strike
        atm_strike = self._get_atm_strike(current_spx_price)
        
        # Check if we need to move to more aggressive strikes
        original_strike = original_combo.center_strike if hasattr(original_combo, 'center_strike') else atm_strike
        strike_distance = abs(atm_strike - original_strike)
        
        # If original strike is more than $2.5 away from current ATM, move to more aggressive strike
        if strike_distance > 2.5:
            logger.info(f"🎯 Strike optimization needed: distance = {strike_distance:.1f} > 2.5")
            
            # Create new combo with current ATM strike
            if original_combo.combo_type.value in ['bull_spread', 'narrow_bull_spread']:
                optimized_combo = ComboOrderFactory.create_bull_spread(atm_strike)
            elif original_combo.combo_type.value in ['bear_spread', 'narrow_bear_spread']:
                optimized_combo = ComboOrderFactory.create_bear_spread(atm_strike)
            elif original_combo.combo_type.value in ['iron_butterfly', 'narrow_iron_butterfly']:
                optimized_combo = ComboOrderFactory.create_narrow_iron_butterfly(atm_strike)
            else:
                # For other combo types, try to recreate with current strike
                optimized_combo = original_combo
                if hasattr(optimized_combo, 'center_strike'):
                    optimized_combo.center_strike = atm_strike
        else:
            optimized_combo = original_combo
        
        # Get updated quote for price optimization
        try:
            quote_result = self.quote_engine.get_combo_quote(optimized_combo)
            if quote_result.is_valid and quote_result.ask is not None and quote_result.bid is not None:
                if optimized_combo.is_credit_strategy:
                    # Credit strategy: want higher credit (more aggressive)
                    raw_price = abs(quote_result.bid) + 0.05  # Add 0.05 for more aggressive credit
                    optimized_price = self._round_to_nickel(raw_price)
                    logger.info(f"💰 Credit optimization: ${original_limit_price:.2f} -> ${optimized_price:.2f}")
                else:
                    # Debit strategy: MID + 0.05c, but must not exceed $2.50
                    mid_price = (abs(quote_result.ask) + abs(quote_result.bid)) / 2
                    raw_price = mid_price + 0.05
                    
                    # If this would exceed $2.50, we need to move strikes instead
                    if raw_price > 2.50:
                        logger.info(f"⚠️ Optimized price ${raw_price:.2f} exceeds $2.50 limit")
                        
                        # Move strikes to get better pricing
                        if optimized_combo.combo_type.value in ['bull_spread', 'narrow_bull_spread']:
                            # For bull spreads, move strikes UP (more aggressive)
                            new_strike = atm_strike + 5  # Move up 5 points
                            logger.info(f"🎯 Moving bull spread strikes UP: {atm_strike} -> {new_strike}")
                            optimized_combo = ComboOrderFactory.create_bull_spread(new_strike)
                            
                            # Get new quote with moved strikes
                            new_quote_result = self.quote_engine.get_combo_quote(optimized_combo)
                            if new_quote_result.is_valid and new_quote_result.ask is not None and new_quote_result.bid is not None:
                                new_mid_price = (abs(new_quote_result.ask) + abs(new_quote_result.bid)) / 2
                                raw_price = new_mid_price + 0.05
                                optimized_price = self._round_to_nickel(raw_price)
                                logger.info(f"💰 New bull spread price: ${optimized_price:.2f}")
                            else:
                                optimized_price = original_limit_price
                                
                        elif optimized_combo.combo_type.value in ['bear_spread', 'narrow_bear_spread']:
                            # For bear spreads, move strikes DOWN (more aggressive)
                            new_strike = atm_strike - 5  # Move down 5 points
                            logger.info(f"🎯 Moving bear spread strikes DOWN: {atm_strike} -> {new_strike}")
                            optimized_combo = ComboOrderFactory.create_bear_spread(new_strike)
                            
                            # Get new quote with moved strikes
                            new_quote_result = self.quote_engine.get_combo_quote(optimized_combo)
                            if new_quote_result.is_valid and new_quote_result.ask is not None and new_quote_result.bid is not None:
                                new_mid_price = (abs(new_quote_result.ask) + abs(new_quote_result.bid)) / 2
                                raw_price = new_mid_price + 0.05
                                optimized_price = self._round_to_nickel(raw_price)
                                logger.info(f"💰 New bear spread price: ${optimized_price:.2f}")
                            else:
                                optimized_price = original_limit_price
                        else:
                            # For other strategies, use original price
                            optimized_price = original_limit_price
                    else:
                        optimized_price = self._round_to_nickel(raw_price)
                        logger.info(f"💰 Debit optimization (MID+0.05): ${original_limit_price:.2f} -> ${optimized_price:.2f}")
            else:
                optimized_price = original_limit_price
                logger.warning("⚠️ Could not get valid quote for price optimization")
        except Exception as e:
            logger.warning(f"⚠️ Quote optimization failed: {str(e)}")
            optimized_price = original_limit_price
        
        return optimized_combo, optimized_price
    
    def _round_to_nickel(self, price: float) -> float:
        """Round price to nearest nickel (0.05)"""
        return round(price / 0.05) * 0.05
    
    def _should_resubmit_with_optimization(self, new_price: float, original_price: float,
                                         new_combo: ComboDefinition, original_combo: ComboDefinition) -> bool:
        """Determine if we should resubmit with optimized parameters"""
        
        # Check if new price is more aggressive
        # For debit strategies: lower price is more aggressive (pay less), but must stay within $2.50
        # For credit strategies: higher price is more aggressive (receive more)
        if new_combo.is_credit_strategy:
            price_improvement = new_price > original_price  # Higher credit is better
        else:
            # For debit strategies, we want lower price (pay less) but not if it exceeds $2.50
            if new_price <= 2.50:
                price_improvement = new_price < original_price  # Lower debit is better
            else:
                price_improvement = False  # Don't resubmit if price exceeds $2.50
        
        # Check if strike is more aggressive (closer to current ATM)
        strike_improvement = False
        if hasattr(new_combo, 'center_strike') and hasattr(original_combo, 'center_strike'):
            current_spx = self._get_current_spx_price()
            if current_spx:
                atm_strike = self._get_atm_strike(current_spx)
                new_distance = abs(new_combo.center_strike - atm_strike)
                original_distance = abs(original_combo.center_strike - atm_strike)
                strike_improvement = new_distance < original_distance
        
        should_resubmit = price_improvement or strike_improvement
        
        if should_resubmit:
            logger.info(f"✅ Optimization detected:")
            if price_improvement:
                strategy_type = "credit" if new_combo.is_credit_strategy else "debit"
                logger.info(f"   Price improvement ({strategy_type}): ${original_price:.2f} -> ${new_price:.2f}")
            if strike_improvement:
                logger.info(f"   Strike improvement: {original_combo.center_strike} -> {new_combo.center_strike}")
        
        return should_resubmit
    
    def _is_more_conservative(self, price1: float, price2: float, combo_definition: ComboDefinition = None) -> bool:
        """Check if price1 is more conservative than price2"""
        # For debit strategies: higher price is more conservative (pay more)
        # For credit strategies: lower price is more conservative (receive less)
        if combo_definition and combo_definition.is_credit_strategy:
            return price1 < price2  # Lower credit is more conservative
        else:
            return price1 > price2  # Higher debit is more conservative
    
    def _cancel_active_orders(self, order_ids: List[str]) -> None:
        """Cancel all active orders"""
        if not order_ids:
            return
        
        logger.info(f"🔄 Cancelling {len(order_ids)} active orders")
        for order_id in order_ids:
            try:
                # Find and cancel the order
                for trade in self.ib.trades():
                    if hasattr(trade, 'order') and hasattr(trade.order, 'orderId'):
                        if str(trade.order.orderId) == order_id:
                            self.ib.cancelOrder(trade.order)
                            logger.info(f"   Cancelled order {order_id}")
                            break
            except Exception as e:
                logger.warning(f"⚠️ Failed to cancel order {order_id}: {str(e)}")
    
    def _check_for_filled_orders(self, order_ids: List[str]) -> Optional[OrderResult]:
        """Check if any of the active orders were filled"""
        if not order_ids:
            return None
        
        for order_id in order_ids:
            try:
                for trade in self.ib.trades():
                    if hasattr(trade, 'order') and hasattr(trade.order, 'orderId'):
                        if str(trade.order.orderId) == order_id:
                            if trade.orderStatus.status == 'Filled':
                                logger.info(f"✅ Order {order_id} was filled!")
                                return OrderResult(
                                    success=True,
                                    trade_id=order_id,
                                    fill_price=trade.orderStatus.avgFillPrice,
                                    order_price=trade.order.lmtPrice,
                                    order_status=trade.orderStatus.status
                                )
            except Exception as e:
                logger.warning(f"⚠️ Error checking order {order_id}: {str(e)}")
        
        return None
    
    def _get_atm_strike(self, spx_price: float) -> float:
        """Get ATM strike based on SPX price"""
        # Round to nearest 5
        return round(spx_price / 5) * 5 

    def execute_bull_spread_with_enhanced_retries(self, current_price: float, quantity: Optional[int] = None, timeout_seconds: Optional[int] = None) -> OrderResult:
        """Execute bull spread with enhanced retry logic - delegate to execution coordinator"""
        return self.execution_coordinator.execute_bull_spread_with_enhanced_retries(
            current_price, quantity, timeout_seconds
        )
        try:
            from combo_orders import ComboOrderFactory
            
            # Create bull spread combo definition
            atm_strike = self._get_atm_strike(current_price)
            combo_def = ComboOrderFactory.create_bull_spread(atm_strike)
            
            # Get initial quote and limit price
            quote_result = self.quote_engine.get_combo_quote(combo_def)
            if not quote_result.is_valid or quote_result.ask is None:
                return OrderResult(success=False, error_message="Could not get valid quote for bull spread")
            
            limit_price = abs(quote_result.ask)
            
            logger.info(f"🐂 Bull spread execution with enhanced retries:")
            logger.info(f"   ATM Strike: {atm_strike}")
            logger.info(f"   Initial Limit Price: ${limit_price:.2f}")
            
            # Use enhanced retry logic
            return self.execute_combo_order_with_enhanced_retries(combo_def, limit_price, quantity, timeout_seconds)
            
        except Exception as e:
            logger.error(f"Error in bull spread enhanced retry execution: {str(e)}")
            return OrderResult(success=False, error_message=f"Bull spread enhanced retry failed: {str(e)}")
    
    def execute_bear_spread_with_enhanced_retries(self, current_price: float, quantity: Optional[int] = None, timeout_seconds: Optional[int] = None) -> OrderResult:
        """Execute bear spread with enhanced retry logic - delegate to execution coordinator"""
        return self.execution_coordinator.execute_bear_spread_with_enhanced_retries(
            current_price, quantity, timeout_seconds
        )
        try:
            from combo_orders import ComboOrderFactory
            
            # Create bear spread combo definition
            atm_strike = self._get_atm_strike(current_price)
            combo_def = ComboOrderFactory.create_bear_spread(atm_strike)
            
            # Get initial quote and limit price
            quote_result = self.quote_engine.get_combo_quote(combo_def)
            if not quote_result.is_valid or quote_result.ask is None:
                return OrderResult(success=False, error_message="Could not get valid quote for bear spread")
            
            limit_price = abs(quote_result.ask)
            
            logger.info(f"🐻 Bear spread execution with enhanced retries:")
            logger.info(f"   ATM Strike: {atm_strike}")
            logger.info(f"   Initial Limit Price: ${limit_price:.2f}")
            
            # Use enhanced retry logic
            return self.execute_combo_order_with_enhanced_retries(combo_def, limit_price, quantity, timeout_seconds)
            
        except Exception as e:
            logger.error(f"Error in bear spread enhanced retry execution: {str(e)}")
            return OrderResult(success=False, error_message=f"Bear spread enhanced retry failed: {str(e)}") 