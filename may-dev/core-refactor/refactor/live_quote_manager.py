#!/usr/bin/env python3
"""
Live Quote Manager for SPX Options
Maintains live quotes for all potential trading strategies
"""

import logging
import datetime as dt
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from ib_insync import IB, Option, Ticker
import math

from combo_orders import StrikeCalculator

logger = logging.getLogger(__name__)


@dataclass
class StrategyQuote:
    """Net quote for a complete strategy"""
    name: str
    strikes: List[float]
    net_bid: Optional[float]
    net_ask: Optional[float]
    net_mid: Optional[float]
    individual_quotes: Dict[str, Dict]
    last_updated: dt.datetime
    is_valid: bool = True


class LiveQuoteManager:
    """Manages live quotes for all potential SPX trading strategies"""
    
    def __init__(self, ib: IB):
        self.ib = ib
        self.current_spx_price = None
        self.expiry_date = None
        
        # Live option contracts and their tickers
        self.option_contracts = {}  # {(strike, right): contract}
        self.option_tickers = {}    # {(strike, right): ticker}
        
        # Strategy quotes
        self.strategy_quotes = {}
        
        # Track which strikes we're monitoring
        self.monitored_strikes = set()
        
    def update_spx_price(self, spx_price: float):
        """Update SPX price and refresh strategy quotes if needed"""
        if self.current_spx_price is None or abs(spx_price - self.current_spx_price) > 2.5:
            logger.info(f"SPX price updated: {self.current_spx_price} -> {spx_price}")
            self.current_spx_price = spx_price
            self._setup_strategy_quotes()
            
    def _get_expiry_date(self) -> str:
        """Get today's expiry date in YYYYMMDD format"""
        return dt.date.today().strftime("%Y%m%d")
        
    def _calculate_strategy_strikes(self) -> Dict[str, List[Tuple[float, str]]]:
        """Calculate all strikes needed for the 12 strategies"""
        if not self.current_spx_price:
            return {}
            
        spx = self.current_spx_price
        atm_strike = StrikeCalculator.get_atm_strike(spx)
        
        strategies = {}
        
        # Bull Spreads (Buy lower strike call, Sell higher strike call)
        # 1. One Better Bull: Buy call 1 below ATM, Sell ATM call  
        strategies['bull_one_better'] = [
            (atm_strike - 5, 'C'),  # Buy
            (atm_strike, 'C')       # Sell
        ]
        
        # 2. Nearest Bull: Buy ATM call, Sell 1 above ATM call
        strategies['bull_nearest'] = [
            (atm_strike, 'C'),      # Buy  
            (atm_strike + 5, 'C')   # Sell
        ]
        
        # 3. Next Worst Bull: Buy 1 above ATM call, Sell 2 above ATM call
        strategies['bull_next_worst'] = [
            (atm_strike + 5, 'C'),  # Buy
            (atm_strike + 10, 'C')  # Sell
        ]
        
        # Bear Spreads (Buy higher strike put, Sell lower strike put)
        # Better bears = higher strikes (cheaper, more OTM)
        # Worse bears = lower strikes (more expensive, closer to ITM)
        
        # 4. One Better Bear: Buy 1 above ATM put, Sell ATM put (better positioning)
        strategies['bear_one_better'] = [
            (atm_strike + 5, 'P'),  # Buy
            (atm_strike, 'P')       # Sell
        ]
        
        # 5. Nearest Bear: Buy ATM put, Sell 1 below ATM put (middle positioning)
        strategies['bear_nearest'] = [
            (atm_strike, 'P'),      # Buy
            (atm_strike - 5, 'P')   # Sell
        ]
        
        # 6. Next Worst Bear: Buy 1 below ATM put, Sell 2 below ATM put (worst positioning)
        strategies['bear_next_worst'] = [
            (atm_strike - 5, 'P'),  # Buy
            (atm_strike - 10, 'P')  # Sell
        ]
        
        # Iron Butterflies at ATM (closest strike)
        center_atm = atm_strike
        
        # 7. Wide Iron Butterfly ATM: ±10 wings
        strategies['wide_butterfly_atm'] = [
            (center_atm - 10, 'P'),  # Buy lower put
            (center_atm, 'C'),       # Sell center call  
            (center_atm, 'P'),       # Sell center put
            (center_atm + 10, 'C')   # Buy upper call
        ]
        
        # 8. Narrow Iron Butterfly ATM: ±5 wings  
        strategies['narrow_butterfly_atm'] = [
            (center_atm - 5, 'P'),   # Buy lower put
            (center_atm, 'C'),       # Sell center call
            (center_atm, 'P'),       # Sell center put  
            (center_atm + 5, 'C')    # Buy upper call
        ]
        
        # Iron Butterflies at nearest strike up (ATM + 5)
        center_up = atm_strike + 5
        
        # 9. Wide Iron Butterfly Up: ±10 wings
        strategies['wide_butterfly_up'] = [
            (center_up - 10, 'P'),  # Buy lower put
            (center_up, 'C'),       # Sell center call  
            (center_up, 'P'),       # Sell center put
            (center_up + 10, 'C')   # Buy upper call
        ]
        
        # 10. Narrow Iron Butterfly Up: ±5 wings  
        strategies['narrow_butterfly_up'] = [
            (center_up - 5, 'P'),   # Buy lower put
            (center_up, 'C'),       # Sell center call
            (center_up, 'P'),       # Sell center put  
            (center_up + 5, 'C')    # Buy upper call
        ]
        
        # Iron Butterflies at nearest strike down (ATM - 5)
        center_down = atm_strike - 5
        
        # 11. Wide Iron Butterfly Down: ±10 wings
        strategies['wide_butterfly_down'] = [
            (center_down - 10, 'P'), # Buy lower put
            (center_down, 'C'),      # Sell center call
            (center_down, 'P'),      # Sell center put
            (center_down + 10, 'C')  # Buy upper call
        ]
        
        # 12. Narrow Iron Butterfly Down: ±5 wings
        strategies['narrow_butterfly_down'] = [
            (center_down - 5, 'P'),  # Buy lower put  
            (center_down, 'C'),      # Sell center call
            (center_down, 'P'),      # Sell center put
            (center_down + 5, 'C')   # Buy upper call
        ]
        
        # Additional strategies for overshoot reversals
        # 11. Overshoot Bull: If SPX moves 1+ strikes above target
        strategies['overshoot_bull'] = [
            (atm_strike + 5, 'C'),   # Buy call above current
            (atm_strike + 10, 'C')   # Sell call further out
        ]
        
        # 12. Overshoot Bear: If SPX moves 1+ strikes below target  
        strategies['overshoot_bear'] = [
            (atm_strike - 5, 'P'),   # Buy put below current
            (atm_strike - 10, 'P')   # Sell put further out
        ]
        
        return strategies
        
    def _setup_strategy_quotes(self):
        """Set up option contracts for all strategies"""
        if not self.current_spx_price:
            return
            
        logger.info(f"Setting up strategy quotes for SPX {self.current_spx_price}")
        
        # Get all strikes needed
        strategies = self._calculate_strategy_strikes()
        
        # Collect all unique option contracts needed
        all_strikes = set()
        for strategy_name, strikes_rights in strategies.items():
            for strike, right in strikes_rights:
                all_strikes.add((strike, right))
                
        # Get expiry date
        self.expiry_date = self._get_expiry_date()
        
        # Create and qualify option contracts
        new_contracts = {}
        new_tickers = {}
        
        for strike, right in all_strikes:
            contract_key = (strike, right)
            
            # Skip if we already have this contract
            if contract_key in self.option_contracts:
                new_contracts[contract_key] = self.option_contracts[contract_key]
                new_tickers[contract_key] = self.option_tickers[contract_key]
                continue
                
            # Create new option contract
            # For same-day expiry, use SPXW trading class to avoid ambiguity
            contract = Option('SPX', self.expiry_date, strike, right, 'SMART', tradingClass='SPXW')
            
            try:
                # Qualify the contract
                qualified = self.ib.qualifyContracts(contract)
                if qualified:
                    qualified_contract = qualified[0]
                    new_contracts[contract_key] = qualified_contract
                    
                    # Subscribe to market data
                    ticker = self.ib.reqMktData(qualified_contract, '', False, False)
                    new_tickers[contract_key] = ticker
                    
                    logger.debug(f"Subscribed to {right} {strike} SPXW quote")
                else:
                    logger.warning(f"Could not qualify {right} {strike}")
                    
            except Exception as e:
                logger.error(f"Error setting up {right} {strike}: {str(e)}")
                
        # Update our stored contracts and tickers
        self.option_contracts = new_contracts
        self.option_tickers = new_tickers
        
        # Initialize strategy quotes
        self._update_strategy_quotes()
        
        logger.info(f"Live quotes setup complete: {len(self.option_contracts)} option contracts")
        
    def _update_strategy_quotes(self):
        """Update all strategy net quotes from individual option quotes"""
        if not self.current_spx_price:
            return
            
        strategies = self._calculate_strategy_strikes()
        
        for strategy_name, strikes_rights in strategies.items():
            net_bid = 0.0
            net_ask = 0.0
            individual_quotes = {}
            all_valid = True
            
            for i, (strike, right) in enumerate(strikes_rights):
                contract_key = (strike, right)
                
                if contract_key not in self.option_tickers:
                    all_valid = False
                    continue
                    
                ticker = self.option_tickers[contract_key]
                
                # Get bid/ask, default to 0 if not available
                bid = ticker.bid if ticker.bid and ticker.bid > 0 else 0.0
                ask = ticker.ask if ticker.ask and ticker.ask > 0 else 0.0
                last = ticker.last if ticker.last and ticker.last > 0 else None
                
                # Store individual quote
                individual_quotes[f"{right}_{strike}"] = {
                    'strike': strike,
                    'right': right,
                    'bid': bid,
                    'ask': ask,
                    'last': last
                }
                
                # Calculate net based on strategy leg direction
                # For spreads: leg 0 = buy, leg 1 = sell
                # For butterflies: legs 0,3 = buy, legs 1,2 = sell
                if strategy_name.startswith(('bull_', 'bear_', 'overshoot_')):
                    # Spreads: Buy first leg, Sell second leg
                    if i == 0:  # Buy leg
                        net_bid -= ask  # We pay the ask when buying
                        net_ask -= bid  # We get the bid when buying
                    else:  # Sell leg  
                        net_bid += bid  # We get the bid when selling
                        net_ask += ask  # We pay the ask when selling
                        
                elif strategy_name.startswith(('wide_butterfly', 'narrow_butterfly')):
                    # Iron Butterflies: Buy legs 0,3, Sell legs 1,2
                    if i in [0, 3]:  # Buy legs
                        net_bid -= ask
                        net_ask -= bid
                    else:  # Sell legs
                        net_bid += bid
                        net_ask += ask
                        
            # Create strategy quote
            net_mid = (net_bid + net_ask) / 2 if all_valid else None
            
            self.strategy_quotes[strategy_name] = StrategyQuote(
                name=strategy_name,
                strikes=[strike for strike, _ in strikes_rights],
                net_bid=net_bid if all_valid else None,
                net_ask=net_ask if all_valid else None,
                net_mid=net_mid,
                individual_quotes=individual_quotes,
                last_updated=dt.datetime.now(),
                is_valid=all_valid
            )
            
    def update_quotes(self):
        """Update all strategy quotes (call this periodically)"""
        self._update_strategy_quotes()
        
    def get_strategy_quote(self, strategy_name: str) -> Optional[StrategyQuote]:
        """Get current quote for a specific strategy"""
        return self.strategy_quotes.get(strategy_name)
        
    def get_all_strategy_quotes(self) -> Dict[str, StrategyQuote]:
        """Get all current strategy quotes"""
        return self.strategy_quotes.copy()
        
    def get_individual_option_quote(self, strike: float, right: str) -> Optional[Dict]:
        """Get current quote for a specific option"""
        contract_key = (strike, right)
        if contract_key not in self.option_tickers:
            return None
            
        ticker = self.option_tickers[contract_key]
        return {
            'strike': strike,
            'right': right,
            'bid': ticker.bid if ticker.bid and ticker.bid > 0 else None,
            'ask': ticker.ask if ticker.ask and ticker.ask > 0 else None,
            'last': ticker.last if ticker.last and ticker.last > 0 else None,
            'volume': getattr(ticker, 'volume', None),
            'timestamp': dt.datetime.now()
        }
        
    def is_quote_valid(self, strategy_name: str, max_age_seconds: int = 5) -> bool:
        """Check if a strategy quote is valid and recent"""
        if strategy_name not in self.strategy_quotes:
            return False
            
        quote = self.strategy_quotes[strategy_name]
        if not quote.is_valid:
            return False
            
        # Check age
        age = (dt.datetime.now() - quote.last_updated).total_seconds()
        return age <= max_age_seconds
        
    def get_quote_for_execution(self, strategy_name: str) -> Optional[StrategyQuote]:
        """Get a quote suitable for immediate execution (validated)"""
        if not self.is_quote_valid(strategy_name):
            # Force update and check again
            self.update_quotes()
            if not self.is_quote_valid(strategy_name):
                return None
                
        return self.get_strategy_quote(strategy_name)
        
    def cleanup(self):
        """Cancel all market data subscriptions"""
        try:
            for ticker in self.option_tickers.values():
                self.ib.cancelMktData(ticker.contract)
            logger.info("Cancelled all option market data subscriptions")
        except Exception as e:
            logger.error(f"Error cleaning up market data: {str(e)}") 