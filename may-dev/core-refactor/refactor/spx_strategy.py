#!/usr/bin/env python3
"""
SPX Trading Strategy - Complete implementation of new algorithm
"""

import logging
import datetime as dt
from typing import Optional, Dict, Any

from strategy_state import (
    StrategyState, BarData, DecisionType, DecisionAnalysis, 
    SpanData, OvershootAnalysis, TradeExecution, ExecutionStatus
)
from config import get_global_config
from quote_engine import OptionQuoteEngine
from execution_engine import OptionExecutionEngine
from combo_orders import StrikeCalculator
from trade_report_generator import TradeReportGenerator

logger = logging.getLogger(__name__)


class SPXTradingStrategy:
    """Complete SPX trading strategy implementation"""
    
    def __init__(self, quote_engine: OptionQuoteEngine, execution_engine: OptionExecutionEngine):
        self.quote_engine = quote_engine
        self.execution_engine = execution_engine
        self.config = get_global_config()
        self.state = StrategyState()
        
        # External price provider for live market data
        self.price_provider = None
        
        # Get timing schedule from config
        self.timing = self.config.timing.get_timing_schedule()
        
        # Initialize trade report generator
        self.report_generator = TradeReportGenerator(is_live_test=self.config.live_test.enabled)
        
        logger.info("🚀 SPX Trading Strategy initialized with new algorithm")
    
    def set_price_provider(self, price_provider_callback):
        """Set external price provider for live market data"""
        self.price_provider = price_provider_callback
    
    def start_session(self):
        """Start a new trading session"""
        # Get current SPX price as fallback for Bar 1 open (prefer actual 15:49:30 bar close)
        current_spx_price = None
        if self.price_provider:
            current_spx_price = self.price_provider()
        else:
            current_spx_price = self._get_current_spx_price()
        
        self.state.start_session(start_price=current_spx_price)
        self.report_generator.start_session(self.state)
        logger.info(f"🚀 Trading session started at {self.state.session_start_time.strftime('%H:%M:%S')}")
        logger.info(f"📋 Default Decision: {self.state.current_decision.value}")
        if current_spx_price:
            logger.info(f"📈 Session start SPX price: ${current_spx_price:.2f} (fallback for Bar 1 open)")
    
    def end_session(self):
        """End the current trading session"""
        self.state.end_session()
        duration = self.state.get_session_duration()
        logger.info(f"🏁 Trading session ended. Duration: {duration}")
        
        # Only generate trade report and record to database if trading activity occurred
        if self._has_trading_activity():
            try:
                report_path = self.report_generator.generate_report()
                logger.info(f"📄 Final trade report saved: {report_path}")
                
                # Get the markdown content for database storage
                report_content = self.report_generator._generate_markdown_content()
                
                # Record to new trade_reports table
                if self.execution_engine:
                    report_id = self.execution_engine.record_trade_report_to_database(
                        report_content, self.state
                    )
                    if report_id:
                        logger.info(f"📋 Trade report recorded in trade_reports table with ID: {report_id}")
                    else:
                        logger.warning("Failed to record trade report to trade_reports table")
                        
            except Exception as e:
                logger.error(f"Failed to generate final trade report: {str(e)}")
            
            # Update any pending trade reports in the database
            if self.execution_engine:
                try:
                    self.execution_engine.update_pending_trade_reports()
                    logger.info("📊 Trade reports updated in database")
                except Exception as e:
                    logger.warning(f"Failed to update trade reports: {str(e)}")
        else:
            logger.info("📄 No trading activity - skipping trade report generation and database recording")
        
        # Always generate a basic file report for debugging (but not database record)
        try:
            report_path = self.report_generator.generate_report()
            logger.info(f"📄 Session report saved: {report_path}")
        except Exception as e:
            logger.error(f"Failed to generate session report: {str(e)}")
    
    def _has_trading_activity(self) -> bool:
        """Check if any trading activity occurred during the session"""
        # Check if any trade execution was attempted
        if self.state.trade_execution:
            return True
            
        # Check if any overshoot reversals were attempted
        if self.state.overshoot_executions:
            return True
            
        # Check if wide iron butterfly was attempted
        if self.state.wide_iron_butterfly_attempted:
            return True
            
        return False
    
    def get_report_path(self) -> Optional[str]:
        """Get the path to the last generated trade report"""
        try:
            return self.report_generator.generate_report()
        except Exception as e:
            logger.error(f"Error generating report path: {str(e)}")
            return None
    
    def process_bar_data(self, bar_data: BarData, bar_number: int, previous_close: Optional[float] = None) -> bool:
        """Process incoming bar data and calculate spans"""
        try:
            # For Bar 1, use the provided previous_close (from 15:49:30 bar) if available
            # For Bars 2 and 3, previous_close will be determined automatically from previous bars
            if bar_number == 1 and previous_close is not None:
                logger.info(f"📈 Using previous bar close ${previous_close:.2f} as Bar 1 open")
            elif bar_number == 1 and self.state.session_start_price:
                # Fallback to session start price if no previous close provided
                previous_close = self.state.session_start_price
                logger.info(f"📈 Fallback: Using session start price ${previous_close:.2f} as Bar 1 open")
            
            # Store the bar data and calculate spans
            self.state.set_bar_data(bar_number, bar_data, previous_close)
            logger.info(f"📊 Bar {bar_number} processed: {self.state.get_latest_bar()}")
            
            # Log span calculations
            if bar_number == 1 and self.state.span1:
                logger.info(f"📏 {self.state.span1}")
            elif bar_number == 2 and self.state.span2:
                logger.info(f"📏 {self.state.span2}")
            elif bar_number == 3 and self.state.span3:
                logger.info(f"📏 {self.state.span3}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error processing bar {bar_number}: {str(e)}")
            return False
    
    def check_analysis_timing(self, current_time: dt.datetime) -> bool:
        """Check if it's time to analyze bars and make decisions"""
        current_minute = current_time.minute
        current_second = current_time.second
        
        # t = 15:50:30 - First bar analysis
        if (current_minute == self.timing['first_bar_analysis']['minute'] and
            current_second == self.timing['first_bar_analysis']['second']):
            return self._analyze_first_bar()
        
        # t = 15:51:00 - Second bar analysis and reversals
        elif (current_minute == self.timing['second_bar_analysis']['minute'] and
              current_second == self.timing['second_bar_analysis']['second']):
            return self._analyze_second_bar()
        
        # t = 15:51:30 - Third bar analysis and final decisions
        elif (current_minute == self.timing['third_bar_analysis']['minute'] and
              current_second == self.timing['third_bar_analysis']['second']):
            return self._analyze_third_bar()
        
        return False
    
    def check_execution_timing(self, current_time: dt.datetime) -> bool:
        """Check if it's time to execute trades"""
        detailed_logger = logging.getLogger('trading_detailed')
        current_minute = current_time.minute
        current_second = current_time.second
        
        # Log the expected timings for debugging
        expected_execution = f"{self.timing['spread_execution']['minute']:02d}:{self.timing['spread_execution']['second']:02d}"
        detailed_logger.info(f"Checking execution timing: {current_minute:02d}:{current_second:02d} (Expected: {expected_execution})")
        
        # Debug the timing values
        detailed_logger.info(f"Timing comparison: current_minute={current_minute}, expected_minute={self.timing['spread_execution']['minute']}")
        detailed_logger.info(f"Timing comparison: current_second={current_second}, expected_second={self.timing['spread_execution']['second']}")
        detailed_logger.info(f"Minute match: {current_minute == self.timing['spread_execution']['minute']}")
        detailed_logger.info(f"Second match: {current_second == self.timing['spread_execution']['second']}")
        
        # t = 15:51:31 - Main execution
        if (current_minute == self.timing['spread_execution']['minute'] and
            current_second == self.timing['spread_execution']['second']):
            detailed_logger.info(f"=== MAIN EXECUTION TIME REACHED ===")
            detailed_logger.info(f"Expected: {self.timing['spread_execution']['minute']:02d}:{self.timing['spread_execution']['second']:02d}")
            detailed_logger.info(f"Current decision: {self.state.current_decision}")
            detailed_logger.info(f"Strategy state: {self.state}")
            return self._execute_main_strategy()
        
        # t = 15:51:40 - Wide iron butterfly fallback check
        elif (current_minute == self.timing['wide_iron_butterfly_fallback']['minute'] and
              current_second == self.timing['wide_iron_butterfly_fallback']['second']):
            detailed_logger.info(f"=== WIDE IRON BUTTERFLY FALLBACK TIME ===")
            return self._check_wide_iron_butterfly_fallback()
        
        # t = 15:58:55 - Overshoot checks
        elif (current_minute == self.timing['overshoot_check']['minute'] and
              current_second == self.timing['overshoot_check']['second']):
            detailed_logger.info(f"=== OVERSHOOT CHECK TIME ===")
            return self._check_overshoots()
        
        # t = 15:58:59 - Cancel narrow orders
        elif (current_minute == self.timing['cancel_narrow']['minute'] and
              current_second == self.timing['cancel_narrow']['second']):
            detailed_logger.info(f"=== CANCEL NARROW ORDERS TIME ===")
            return self._cancel_narrow_orders()
        
        return False
    
    def _analyze_first_bar(self) -> bool:
        """t = 15:50:30 - Analyze first bar using SPAN1"""
        
        # Guard against duplicate analysis only if bar data hasn't changed
        if (self.state._first_bar_analyzed and 
            self.state._last_analyzed_bar1_close is not None and
            abs(self.state.first_bar.close - self.state._last_analyzed_bar1_close) < 0.001):
            logger.debug("First bar already analyzed with same close price - skipping duplicate analysis")
            return True
        elif self.state._first_bar_analyzed:
            logger.info(f"🔄 First bar data changed: {self.state._last_analyzed_bar1_close:.2f} → {self.state.first_bar.close:.2f} - re-analyzing")
            
        if not self.state.first_bar or not self.state.span1:
            logger.error("No first bar or SPAN1 data for analysis")
            return False
        
        first_bar = self.state.first_bar
        span1 = self.state.span1
        
        # Calculate close percentage within SPAN1
        close_pct = first_bar.get_close_percentage_in_span(span1.low, span1.high)
        
        # Track bias for iron butterfly strike selection (using SPAN1)
        bias = 0  # Default neutral
        if close_pct >= self.config.strategy.iron_butterfly_upper_threshold and close_pct < self.config.strategy.bull_threshold_upper:  # >=70% and <88%
            bias = +1  # bias up
            bias_reason = f"First bar close ≥{self.config.strategy.iron_butterfly_upper_threshold}% and <{self.config.strategy.bull_threshold_upper}% in SPAN1 → bias=+1 (bias up)"
        elif close_pct <= self.config.strategy.iron_butterfly_lower_threshold and close_pct > self.config.strategy.bear_threshold_lower:  # <=30% and >12%
            bias = -1  # bias down  
            bias_reason = f"First bar close ≤{self.config.strategy.iron_butterfly_lower_threshold}% and >{self.config.strategy.bear_threshold_lower}% in SPAN1 → bias=-1 (bias down)"
        else:
            bias_reason = f"First bar close at {close_pct:.1f}% in SPAN1 → no bias change"
        
        # Store bias in state
        self.state.current_bias = bias
        logger.info(f"📊 BIAS TRACKING (First Bar): {bias_reason}")
        
        # Make decision based on new algorithm
        if self.config.strategy.is_bear_signal_first_bar(close_pct):
            decision = DecisionType.BEAR
            reason = f"First bar close ≤ {self.config.strategy.bear_threshold_lower}% in SPAN1 (at {close_pct:.1f}%)"
        elif self.config.strategy.is_bull_signal_first_bar(close_pct):
            decision = DecisionType.BULL
            reason = f"First bar close ≥ {self.config.strategy.bull_threshold_upper}% in SPAN1 (at {close_pct:.1f}%)"
        else:
            # Stay as IRON_BUTTERFLY - BF conversion will happen in third bar if no breakout occurs
            decision = DecisionType.IRON_BUTTERFLY
            reason = f"Iron Butterfly: First bar close {close_pct:.1f}% in neutral range (BF conversion pending)"
        
        # Create decision analysis
        analysis = DecisionAnalysis(
            decision_type=decision,
            reason=reason,
            close_percentage=close_pct,
            bar_data=first_bar,
            span_data=span1
        )
        
        self.state.set_decision(decision, analysis)
        logger.info(f"🎯 First bar decision: {decision.value} - {reason}")
        
        # Log to trade report
        self.report_generator.log_bar_analysis(1, first_bar, span1, decision, reason, close_pct)
        
        # Mark as analyzed and store close price to detect future changes
        self.state._first_bar_analyzed = True
        self.state._last_analyzed_bar1_close = first_bar.close
        
        return True
    
    def _analyze_second_bar(self) -> bool:
        """t = 15:51:00 - Analyze second bar for reversals/breakouts using SPAN2"""
        
        # Guard against duplicate analysis only if bar data hasn't changed
        if (self.state._second_bar_analyzed and 
            self.state._last_analyzed_bar2_close is not None and
            abs(self.state.second_bar.close - self.state._last_analyzed_bar2_close) < 0.001):
            logger.debug("Second bar already analyzed with same close price - skipping duplicate analysis")
            return True
        elif self.state._second_bar_analyzed:
            logger.info(f"🔄 Second bar data changed: {self.state._last_analyzed_bar2_close:.2f} → {self.state.second_bar.close:.2f} - re-analyzing")
            
        if not self.state.second_bar or not self.state.span2:
            logger.error("No second bar or SPAN2 data for analysis")
            return False

        first_bar = self.state.first_bar
        second_bar = self.state.second_bar
        span2 = self.state.span2
        current_decision = self.state.current_decision
        
        # Check for breakout conditions using DIRECT PRICE COMPARISONS
        # REGARDLESS of first bar decision, if we break out and SPAN2 > 4
        
        bear_threshold_price = span2.low + (self.config.strategy.bear_threshold_lower / 100.0 * span2.span_points)  # Config bear threshold
        bull_threshold_price = span2.low + (self.config.strategy.bull_threshold_upper / 100.0 * span2.span_points)  # Config bull threshold
        
        # Bear breakout: 2nd bar's low below first bar's low & close < bear_threshold of SPAN2
        if (second_bar.low < first_bar.low and 
            second_bar.close < bear_threshold_price and
            span2.span_points > 4.0):
            
            reason = f"Breakout: 2nd bar low < 1st bar low AND 2nd bar close < {self.config.strategy.bear_threshold_lower}% of SPAN2 AND SPAN2 > 4 ({second_bar.close:.2f} < {bear_threshold_price:.2f}, SPAN2={span2.span_points:.1f})"
            self.state.update_decision(DecisionType.BEAR, reason, span2)
            logger.info("🔄 BREAKOUT: → Bear")
            
            # Log breakout to trade report
            self.report_generator.log_reversal(current_decision, DecisionType.BEAR, 2, reason)
            return True
        
        # Bull breakout: 2nd bar's high above first bar's high & close > bull_threshold of SPAN2  
        elif (second_bar.high > first_bar.high and 
              second_bar.close > bull_threshold_price and
              span2.span_points > 4.0):
            
            reason = f"Breakout: 2nd bar high > 1st bar high AND 2nd bar close > {self.config.strategy.bull_threshold_upper}% of SPAN2 AND SPAN2 > 4 ({second_bar.close:.2f} > {bull_threshold_price:.2f}, SPAN2={span2.span_points:.1f})"
            self.state.update_decision(DecisionType.BULL, reason, span2)
            logger.info("🔄 BREAKOUT: → Bull")
            
            # Log breakout to trade report
            self.report_generator.log_reversal(current_decision, DecisionType.BULL, 2, reason)
            return True
        
        logger.info(f"📊 Second bar analysis: No breakout conditions met (SPAN2={span2.span_points:.1f}, close at {((second_bar.close - span2.low) / span2.span_points * 100):.1f}% of SPAN2)")
        
        # Track bias for iron butterfly strike selection (using SPAN2)
        close_pct_span2 = ((second_bar.close - span2.low) / span2.span_points * 100) if span2.span_points > 0 else 50.0
        
        if close_pct_span2 >= self.config.strategy.iron_butterfly_upper_threshold and close_pct_span2 < self.config.strategy.bull_threshold_upper:  # >=70% and <88%
            self.state.current_bias = +1  # Override previous bias
            bias_reason = f"Second bar close ≥{self.config.strategy.iron_butterfly_upper_threshold}% and <{self.config.strategy.bull_threshold_upper}% in SPAN2 → bias=+1 (bias up)"
            logger.info(f"📊 BIAS TRACKING (Second Bar): {bias_reason}")
        elif close_pct_span2 <= self.config.strategy.iron_butterfly_lower_threshold and close_pct_span2 > self.config.strategy.bear_threshold_lower:  # <=30% and >12%
            self.state.current_bias = -1  # Override previous bias
            bias_reason = f"Second bar close ≤{self.config.strategy.iron_butterfly_lower_threshold}% and >{self.config.strategy.bear_threshold_lower}% in SPAN2 → bias=-1 (bias down)"
            logger.info(f"📊 BIAS TRACKING (Second Bar): {bias_reason}")
        else:
            bias_reason = f"Second bar close at {close_pct_span2:.1f}% in SPAN2 → no bias change (keeping bias={self.state.current_bias})"
            logger.info(f"📊 BIAS TRACKING (Second Bar): {bias_reason}")
        
        # Mark as analyzed and store close price to detect future changes
        self.state._second_bar_analyzed = True
        self.state._last_analyzed_bar2_close = second_bar.close
        
        return True
    
    def _analyze_third_bar(self) -> bool:
        """t = 15:51:30 - Analyze third bar for final reversals and narrow conversions using SPAN3"""
        
        # Guard against duplicate analysis only if bar data hasn't changed
        if (self.state._third_bar_analyzed and 
            self.state._last_analyzed_bar3_close is not None and
            abs(self.state.third_bar.close - self.state._last_analyzed_bar3_close) < 0.001):
            logger.debug("Third bar already analyzed with same close price - skipping duplicate analysis")
            return True
        elif self.state._third_bar_analyzed:
            logger.info(f"🔄 Third bar data changed: {self.state._last_analyzed_bar3_close:.2f} → {self.state.third_bar.close:.2f} - re-analyzing")
            
        if not self.state.third_bar or not self.state.span3:
            logger.error("No third bar or SPAN3 data for analysis")
            return False
        
        # Verify bar continuity now that we have all three bars
        self.verify_bar_continuity()
        
        first_bar = self.state.first_bar
        second_bar = self.state.second_bar
        third_bar = self.state.third_bar
        span3 = self.state.span3
        
        # Check for final breakout conditions using DIRECT PRICE COMPARISONS
        # REGARDLESS of current decision, if we break out and SPAN3 > 4
        current_decision = self.state.current_decision
        
        # Calculate first 2-bar's low and high
        two_bar_low = min(first_bar.low, second_bar.low)
        two_bar_high = max(first_bar.high, second_bar.high)
        
        bear_threshold_price = span3.low + (self.config.strategy.bear_threshold_lower / 100.0 * span3.span_points)  # Config bear threshold
        bull_threshold_price = span3.low + (self.config.strategy.bull_threshold_upper / 100.0 * span3.span_points)  # Config bull threshold

        # Bear breakout: 3rd bar's low below first 2-bar's low & close < bear_threshold of SPAN3
        if (third_bar.low < two_bar_low and 
            third_bar.close < bear_threshold_price and
            span3.span_points > 4.0):
            
            reason = f"Final breakout: 3rd bar low < first 2-bar's low AND 3rd bar close < {self.config.strategy.bear_threshold_lower}% of SPAN3 AND SPAN3 > 4 ({third_bar.close:.2f} < {bear_threshold_price:.2f}, SPAN3={span3.span_points:.1f})"
            self.state.update_decision(DecisionType.BEAR, reason, span3)
            logger.info("🔄 FINAL BREAKOUT: → Bear")
            
            # Log final breakout to trade report
            self.report_generator.log_reversal(current_decision, DecisionType.BEAR, 3, reason)

        # Bull breakout: 3rd bar's high above first 2-bar's high & close > bull_threshold of SPAN3  
        elif (third_bar.high > two_bar_high and 
              third_bar.close > bull_threshold_price and
              span3.span_points > 4.0):
            
            reason = f"Final breakout: 3rd bar high > first 2-bar's high AND 3rd bar close > {self.config.strategy.bull_threshold_upper}% of SPAN3 AND SPAN3 > 4 ({third_bar.close:.2f} > {bull_threshold_price:.2f}, SPAN3={span3.span_points:.1f})"
            self.state.update_decision(DecisionType.BULL, reason, span3)
            logger.info("🔄 FINAL BREAKOUT: → Bull")
            
            # Log final breakout to trade report
            self.report_generator.log_reversal(current_decision, DecisionType.BULL, 3, reason)
        
        # If still IRON_BUTTERFLY and no breakout occurred, convert to BF-Bull/BF-Bear based on first bar
        if self.state.current_decision == DecisionType.IRON_BUTTERFLY:
            first_bar_close_pct = first_bar.get_close_percentage_in_span(self.state.span1.low, self.state.span1.high)
            
            if first_bar_close_pct > 66.0:
                reason = f"Iron Butterfly → BF-Bull: First bar close {first_bar_close_pct:.1f}% > 66% in SPAN1 (no breakout in bars 2-3)"
                self.state.update_decision(DecisionType.BF_BULL, reason, span3)
                logger.info("🔄 BF CONVERSION: → BF-Bull")
                
                # Log BF conversion to trade report
                self.report_generator.log_reversal(current_decision, DecisionType.BF_BULL, 3, reason)
            else:
                reason = f"Iron Butterfly → BF-Bear: First bar close {first_bar_close_pct:.1f}% ≤ 66% in SPAN1 (no breakout in bars 2-3)"
                self.state.update_decision(DecisionType.BF_BEAR, reason, span3)
                logger.info("🔄 BF CONVERSION: → BF-Bear")
                
                # Log BF conversion to trade report
                self.report_generator.log_reversal(current_decision, DecisionType.BF_BEAR, 3, reason)
        
        # Check SPAN3 < 4 for narrow conversions + IBF dual trade
        if self.config.strategy.is_small_range(span3.span_points):
            logger.info(f"📏 SPAN3 < {self.config.strategy.narrow_range_threshold}: Converting to narrow versions")
            
            original_decision = self.state.current_decision
            
            # Get current SPX price for IBF strike calculation
            current_price = self._get_current_spx_price()
            if current_price:
                from combo_orders import StrikeCalculator
                ibf_strike = StrikeCalculator.round_to_nearest_strike(current_price)
            else:
                # Fallback to third bar close if no current price
                from combo_orders import StrikeCalculator
                ibf_strike = StrikeCalculator.round_to_nearest_strike(third_bar.close)
            
            # Convert all bull/bear variants to narrow + log IBF dual
            if original_decision in [DecisionType.BULL, DecisionType.BF_BULL]:
                self.state.update_decision(DecisionType.NARROW_BULL, f"SPAN3 < {self.config.strategy.narrow_range_threshold}", span3)
                
                # Set dual trade intention
                self.state.dual_trade_intended = True
                self.state.dual_trade_type = "iron_butterfly"
                self.state.dual_trade_strike = ibf_strike
                
                # Log dual trade intention
                logger.info(f"🔄 {original_decision.value} → Narrow Bull + would execute Iron Butterfly dual at strike {ibf_strike}")
                
                # Log narrow conversion to trade report
                self.report_generator.log_narrow_conversion(original_decision, DecisionType.NARROW_BULL, span3.span_points)
                
                # Log dual trade intention to trade report
                self.report_generator.log_dual_trade_intention(DecisionType.NARROW_BULL, ibf_strike, "one_better_narrow_bull")
                
            elif original_decision in [DecisionType.BEAR, DecisionType.BF_BEAR]:
                self.state.update_decision(DecisionType.NARROW_BEAR, f"SPAN3 < {self.config.strategy.narrow_range_threshold}", span3)
                
                # Set dual trade intention
                self.state.dual_trade_intended = True
                self.state.dual_trade_type = "iron_butterfly"
                self.state.dual_trade_strike = ibf_strike
                
                # Log dual trade intention
                logger.info(f"🔄 {original_decision.value} → Narrow Bear + would execute Iron Butterfly dual at strike {ibf_strike}")
                
                # Log narrow conversion to trade report
                self.report_generator.log_narrow_conversion(original_decision, DecisionType.NARROW_BEAR, span3.span_points)
                
                # Log dual trade intention to trade report  
                self.report_generator.log_dual_trade_intention(DecisionType.NARROW_BEAR, ibf_strike, "one_better_narrow_bear")
                
            # IRON_BUTTERFLY remains the same (no conversion needed)
        
        # Iron butterfly strike selection based on THIRD bar close vs SPAN3 thresholds
        if self.state.current_decision == DecisionType.IRON_BUTTERFLY:
            self._set_iron_butterfly_strike_direction_with_bias(third_bar.close, span3)

        # Track bias for iron butterfly strike selection (using SPAN3 - FINAL DECISION)
        close_pct_span3 = ((third_bar.close - span3.low) / span3.span_points * 100) if span3.span_points > 0 else 50.0
        
        if close_pct_span3 >= self.config.strategy.iron_butterfly_upper_threshold and close_pct_span3 < self.config.strategy.bull_threshold_upper:  # >=70% and <88%
            self.state.current_bias = +1  # Override previous bias
            bias_reason = f"Third bar close ≥{self.config.strategy.iron_butterfly_upper_threshold}% and <{self.config.strategy.bull_threshold_upper}% in SPAN3 → bias=+1 (bias up) - FINAL"
            logger.info(f"📊 BIAS TRACKING (Third Bar - FINAL): {bias_reason}")
        elif close_pct_span3 <= self.config.strategy.iron_butterfly_lower_threshold and close_pct_span3 > self.config.strategy.bear_threshold_lower:  # <=30% and >12%
            self.state.current_bias = -1  # Override previous bias
            bias_reason = f"Third bar close ≤{self.config.strategy.iron_butterfly_lower_threshold}% and >{self.config.strategy.bear_threshold_lower}% in SPAN3 → bias=-1 (bias down) - FINAL"
            logger.info(f"📊 BIAS TRACKING (Third Bar - FINAL): {bias_reason}")
        else:
            bias_reason = f"Third bar close at {close_pct_span3:.1f}% in SPAN3 → no bias change (final bias={self.state.current_bias})"
            logger.info(f"📊 BIAS TRACKING (Third Bar - FINAL): {bias_reason}")

        logger.info(f"📊 Third bar analysis complete. Final decision: {self.state.current_decision.value}")
        
        # Mark as analyzed and store close price to detect future changes
        self.state._third_bar_analyzed = True
        self.state._last_analyzed_bar3_close = third_bar.close
        
        return True
    
    def _set_iron_butterfly_strike_direction_with_bias(self, close_price: float, span3: SpanData):
        """Set iron butterfly strike direction with bias tracking based on SPEC requirements"""
        
        # Calculate percentage within SPAN3
        close_pct = ((close_price - span3.low) / span3.span_points * 100) if span3.span_points > 0 else 50.0
        
        # Use the tracked bias from state (accumulated through all bars)
        bias = getattr(self.state, 'current_bias', 0)  # Get tracked bias or default to 0
        
        # Determine strike direction based on tracked bias
        if bias == +1:  # bias up
            direction = "round_up"
            bias_reason = f"Using tracked bias=+1 (bias up)"
            strike_reason = f"bias up → round up to next strike above SPX"
        elif bias == -1:  # bias down
            direction = "round_down"
            bias_reason = f"Using tracked bias=-1 (bias down)"
            strike_reason = f"bias down → round down to next strike below SPX"
        else:  # neutral bias
            direction = "round_nearest"
            bias_reason = f"Using tracked bias=0 (neutral)"
            strike_reason = f"bias neutral → closest strike to SPX"
        
        # Store direction in state for potential future use
        if self.state.current_analysis:
            self.state.current_analysis.iron_butterfly_strike_direction = direction
            # Add bias tracking to analysis state
            if not hasattr(self.state.current_analysis, 'bias'):
                self.state.current_analysis.bias = bias
            else:
                self.state.current_analysis.bias = bias
        
        logger.info(f"📊 IRON BUTTERFLY STRIKE SELECTION: 3-bar close at {close_pct:.1f}% of SPAN3")
        logger.info(f"🎯 {bias_reason}")
        logger.info(f"🦋 STRIKE SELECTION: {strike_reason}")
        logger.info(f"🎲 Final iron butterfly strike direction: {direction}")
        
        # Log comprehensive bias and strike selection to trade report
        full_reason = f"{bias_reason} | {strike_reason} | Close: ${close_price:.2f} at {close_pct:.1f}% of SPAN3 ({span3.low:.2f}-{span3.high:.2f})"
        self.report_generator.log_iron_butterfly_strike_selection(close_price, span3, direction, full_reason)
        
        return direction
    
    def _execute_main_strategy(self) -> bool:
        """t = 15:51:31 - Execute main strategy based on final decision"""
        detailed_logger = logging.getLogger('trading_detailed')
        
        try:
            detailed_logger.info(f"=== STARTING MAIN STRATEGY EXECUTION ===")
            decision = self.state.current_decision
            detailed_logger.info(f"Final decision: {decision}")
            
            current_price = self._get_current_spx_price()
            detailed_logger.info(f"Current SPX price: {current_price}")
            
            if not current_price:
                detailed_logger.error("❌ Cannot execute trade - no SPX price available")
                logger.error("Cannot execute trade - no SPX price")
                return False
            
            detailed_logger.info(f"Strategy path: {decision.value}")
            logger.info(f"🚀 Executing main strategy: {decision.value} at SPX {current_price:.2f}")
            
            result = False
            
            if decision in [DecisionType.BULL, DecisionType.BEAR, DecisionType.BF_BULL, DecisionType.BF_BEAR]:
                detailed_logger.info(f"Executing {decision.value} spread with retries...")
                # Map BF decisions to their base spread type for execution
                execution_decision = DecisionType.BULL if decision in [DecisionType.BULL, DecisionType.BF_BULL] else DecisionType.BEAR
                result = self._execute_bull_bear_with_retries(execution_decision, current_price)
            elif decision in [DecisionType.NARROW_BULL, DecisionType.NARROW_BEAR]:
                detailed_logger.info(f"Executing one better narrow {decision.value}...")
                result = self._execute_one_better_narrow(decision, current_price)
            elif decision == DecisionType.IRON_BUTTERFLY:
                detailed_logger.info("Executing wide iron butterfly...")
                result = self._execute_wide_iron_butterfly(current_price)
            else:
                detailed_logger.error(f"❌ Unexpected decision type: {decision}")
                logger.error(f"Unexpected decision type for execution: {decision}")
                return False
            
            detailed_logger.info(f"Main strategy execution result: {result}")
            detailed_logger.info(f"=== MAIN STRATEGY EXECUTION COMPLETE ===")
            return result
                
        except Exception as e:
            detailed_logger.error(f"=== MAIN STRATEGY EXECUTION FAILED ===")
            detailed_logger.error(f"Exception: {str(e)}")
            detailed_logger.error(f"Exception type: {type(e).__name__}")
            import traceback
            detailed_logger.error(f"Traceback:\n{traceback.format_exc()}")
            logger.error(f"Error in main strategy execution: {str(e)}")
            return False
    
    def _execute_bull_bear_with_retries(self, decision: DecisionType, current_price: float) -> bool:
        """Execute bull/bear spreads with enhanced retry logic"""
        logger.info(f"🔄 Starting {decision.value} spread execution with enhanced retry logic")
        
        try:
            # Get current price for initial calculation
            current_price = self._get_current_spx_price()
            if current_price is None:
                logger.error("❌ Could not get current SPX price for execution")
                return False
            
            # Log execution attempt
            atm_strike = StrikeCalculator.get_atm_strike(current_price)
            strategy_details = f"{decision.value} spread: Enhanced retry with dynamic optimization"
            self.report_generator.log_execution_attempt(decision, atm_strike, self.config.pricing.spread_price_limit, strategy_details)
            
            # Use enhanced retry logic from execution engine
            if decision == DecisionType.BULL:
                result = self.execution_engine.execute_bull_spread_with_enhanced_retries(current_price)
            else:
                result = self.execution_engine.execute_bear_spread_with_enhanced_retries(current_price)
            
            if result.success:
                # Extract actual strike from result or calculate best guess
                executed_strike = result.strike_price if hasattr(result, 'strike_price') else atm_strike
                
                execution = TradeExecution(
                    decision_type=decision,
                    execution_status=ExecutionStatus.EXECUTED,
                    execution_time=result.execution_time,
                    trade_id=result.trade_id,
                    strike_price=executed_strike,
                    execution_price=result.fill_price,
                    retry_count=0  # Enhanced retry doesn't use simple retry count
                )
                self.state.set_trade_execution(execution)
                logger.info(f"✅ {decision.value.upper()} spread executed with enhanced retry logic")
                
                # Log execution success and generate report
                try:
                    self.report_generator.log_execution_result(True, result.trade_id, result.fill_price)
                    self.report_generator.generate_report()
                    logger.info("📊 SUCCESS: Trade report generated")
                except Exception as report_e:
                    logger.error(f"❌ Error generating success report: {report_e}")
                
                return True
            else:
                # Log execution failure
                logger.error(f"❌ {decision.value.upper()} spread execution failed with enhanced retry logic")
                
                execution = TradeExecution(
                    decision_type=decision,
                    execution_status=ExecutionStatus.FAILED,
                    error_message=result.error_message,
                    retry_count=0,
                    strike_price=atm_strike
                )
                self.state.set_trade_execution(execution)
                
                # Log execution failure and generate report
                try:
                    self.report_generator.log_execution_result(False, error_message=result.error_message)
                    self.report_generator.generate_report()
                    logger.info("📊 FAILURE: Trade report generated")
                except Exception as report_e:
                    logger.error(f"❌ Error generating failure report: {report_e}")
                
                return False
                
        except Exception as e:
            logger.error(f"Error in enhanced {decision.value} execution: {str(e)}")
            return False
    
    def _execute_one_better_narrow(self, decision: DecisionType, current_price: float) -> bool:
        """Execute 'one better strike' narrow spreads (Good-til-Date 15:58:59) with target price validation"""
        atm_strike = StrikeCalculator.get_atm_strike(current_price)
        
        try:
            # Log execution attempt
            strategy_details = f"One better {decision.value.lower()}: ATM-5 to ATM spread"
            self.report_generator.log_execution_attempt(decision, atm_strike, self.config.pricing.narrow_spread_limit, strategy_details)
            
            if decision == DecisionType.NARROW_BULL:
                result = self.execution_engine.execute_one_better_narrow_bull(atm_strike, execution_price=current_price)
            else:  # NARROW_BEAR
                result = self.execution_engine.execute_one_better_narrow_bear(atm_strike, execution_price=current_price)
            
            execution = TradeExecution(
                decision_type=decision,
                execution_status=ExecutionStatus.EXECUTED if result.success else ExecutionStatus.FAILED,
                execution_time=result.execution_time,
                trade_id=result.trade_id,
                strike_price=atm_strike,
                execution_price=result.fill_price,
                error_message=result.error_message
            )
            
            self.state.set_trade_execution(execution)
            
            if result.success:
                logger.info(f"✅ {decision.value.upper()} 'one better' spread executed (GTD 15:58:59)")
                logger.info("🎯 DONE FOR THE DAY")
                
                # Log execution success and generate report
                self.report_generator.log_execution_result(True, result.trade_id, result.fill_price)
                self.report_generator.generate_report()
            else:
                logger.error(f"❌ {decision.value.upper()} 'one better' spread execution failed")
                
                # Log execution failure and generate report
                self.report_generator.log_execution_result(False, error_message=result.error_message)
                self.report_generator.generate_report()
            
            return result.success
            
        except Exception as e:
            logger.error(f"Error executing one better narrow spread: {str(e)}")
            return False
    
    def _execute_wide_iron_butterfly(self, current_price: float) -> bool:
        """Execute wide iron butterfly - but only if credit is above $6.00"""
        # Get center strike based on iron butterfly direction  
        if (self.state.current_analysis and 
            self.state.current_analysis.iron_butterfly_strike_direction):
            direction = self.state.current_analysis.iron_butterfly_strike_direction
        else:
            direction = "round_nearest"  # Default
        
        # Calculate center strike based on direction
        if direction == "round_up":
            center_strike = StrikeCalculator.get_next_strike_up(current_price)
        elif direction == "round_down":
            center_strike = StrikeCalculator.get_next_strike_down(current_price)
        else:  # round_nearest
            center_strike = StrikeCalculator.get_atm_strike(current_price)
        
        # Show bias information if available
        bias_info = ""
        if (self.state.current_analysis and hasattr(self.state.current_analysis, 'bias')):
            bias = self.state.current_analysis.bias
            bias_desc = "bias up" if bias > 0 else "bias down" if bias < 0 else "bias neutral"
            bias_info = f" | {bias_desc} (bias={bias:+d})"
        
        logger.info(f"🦋 Wide Iron Butterfly center strike: {center_strike} ({direction}){bias_info}")
        
        try:
            # First check the real-time quote - only submit if credit >= $6.00
            logger.info("🔍 Getting wide iron butterfly quote...")
            from combo_orders import ComboOrderFactory
            combo_def = ComboOrderFactory.create_wide_iron_butterfly(center_strike)
            quote_result = self.quote_engine.get_combo_quote(combo_def)
            
            # Extract credit amount (bid for selling the combo)
            wide_bf_quote = 0.0
            if (quote_result.is_valid and 
                quote_result.bid is not None and 
                quote_result.bid == quote_result.bid):  # Check for NaN
                wide_bf_quote = abs(quote_result.bid)  # Take absolute value for credit
            
            logger.info(f"🔍 Quote engine returned: ${wide_bf_quote:.2f}")
            
            # Use test credit in live test mode, otherwise use fallback threshold
            if self.config.live_test.enabled:
                min_credit_threshold = self.config.pricing.wide_iron_butterfly_test_credit  # $9.20
                logger.info(f"🧪 Live test mode: Using test credit threshold ${min_credit_threshold:.2f}")
            else:
                min_credit_threshold = self.config.pricing.wide_iron_butterfly_fallback_min  # $6.00
            
            logger.info(f"💰 Wide Iron Butterfly real-time quote: ${wide_bf_quote:.2f} (threshold: ${min_credit_threshold:.2f})")
            
            # Check for invalid quote or below threshold
            if not quote_result.is_valid or wide_bf_quote < min_credit_threshold:
                logger.info(f"❌ Wide Iron Butterfly credit ${wide_bf_quote:.2f} below ${min_credit_threshold:.2f} threshold - skipping")
                logger.info("🔄 Immediately executing narrow iron butterfly (quote too low)")
                
                # Mark as attempted but skip to immediate narrow butterfly execution
                self.state.set_wide_iron_butterfly_attempted(True)
                
                # Log execution attempt for narrow fallback
                strategy_details = f"Narrow Iron Butterfly (immediate fallback): ±5 wings"
                self.report_generator.log_execution_attempt(DecisionType.IRON_BUTTERFLY, center_strike, self.config.pricing.narrow_iron_butterfly_credit_min, strategy_details)
                
                # Execute narrow iron butterfly immediately
                result = self.execution_engine.execute_iron_butterfly(center_strike)
                
                execution = TradeExecution(
                    decision_type=DecisionType.IRON_BUTTERFLY,
                    execution_status=ExecutionStatus.EXECUTED if result.success else ExecutionStatus.FAILED,
                    execution_time=result.execution_time,
                    trade_id=result.trade_id,
                    strike_price=center_strike,
                    execution_price=result.fill_price,
                    error_message=result.error_message,
                    original_decision=DecisionType.WIDE_IRON_BUTTERFLY
                )
                
                self.state.set_trade_execution(execution)
                
                if result.success:
                    logger.info(f"✅ NARROW IRON BUTTERFLY executed for ${result.fill_price:.2f} credit (immediate fallback)")
                    logger.info("🎯 DONE FOR THE DAY")
                    
                    # Log execution success and generate report
                    self.report_generator.log_execution_result(True, result.trade_id, result.fill_price)
                    self.report_generator.generate_report()
                    return True
                else:
                    logger.error("❌ Narrow iron butterfly execution failed")
                    
                    # Log execution failure and generate report
                    self.report_generator.log_execution_result(False, error_message=result.error_message)
                    self.report_generator.generate_report()
                    return False
            
            logger.info(f"✅ Quote above threshold, placing wide iron butterfly order at $6.50 for 9 seconds")
            
            # Log execution attempt for wide iron butterfly
            strategy_details = f"Wide Iron Butterfly: ±10 wings, 9-second timeout"
            self.report_generator.log_execution_attempt(DecisionType.WIDE_IRON_BUTTERFLY, center_strike, self.config.pricing.wide_iron_butterfly_credit_min, strategy_details)
            
            # Credit is above threshold, submit the order at $6.50 with 9-second timeout
            result = self.execution_engine.execute_wide_iron_butterfly(center_strike)
            
            # Mark that we attempted wide iron butterfly
            self.state.set_wide_iron_butterfly_attempted(True)
            
            if result.success:
                self.state.set_wide_iron_butterfly_filled(True)
                execution = TradeExecution(
                    decision_type=DecisionType.WIDE_IRON_BUTTERFLY,
                    execution_status=ExecutionStatus.EXECUTED,
                    execution_time=result.execution_time,
                    trade_id=result.trade_id,
                    strike_price=center_strike,
                    execution_price=result.fill_price
                )
                self.state.set_trade_execution(execution)
                logger.info(f"✅ WIDE IRON BUTTERFLY executed for ${result.fill_price:.2f} credit")
                logger.info("🎯 DONE FOR THE DAY")
                
                # Log execution success and generate report
                self.report_generator.log_execution_result(True, result.trade_id, result.fill_price)
                self.report_generator.generate_report()
                return True
            else:
                logger.info(f"⏱️ Wide iron butterfly not filled in 9 seconds, executing narrow iron butterfly immediately")
                
                # Log execution attempt for narrow timeout fallback
                strategy_details = f"Narrow Iron Butterfly (9s timeout fallback): ±5 wings"
                self.report_generator.log_execution_attempt(DecisionType.IRON_BUTTERFLY, center_strike, self.config.pricing.narrow_iron_butterfly_credit_min, strategy_details)
                
                # Execute narrow iron butterfly immediately after timeout
                result = self.execution_engine.execute_iron_butterfly(center_strike)
                
                execution = TradeExecution(
                    decision_type=DecisionType.IRON_BUTTERFLY,
                    execution_status=ExecutionStatus.EXECUTED if result.success else ExecutionStatus.FAILED,
                    execution_time=result.execution_time,
                    trade_id=result.trade_id,
                    strike_price=center_strike,
                    execution_price=result.fill_price,
                    error_message=result.error_message,
                    original_decision=DecisionType.WIDE_IRON_BUTTERFLY
                )
                
                self.state.set_trade_execution(execution)
                
                if result.success:
                    logger.info(f"✅ NARROW IRON BUTTERFLY executed for ${result.fill_price:.2f} credit (9-second timeout fallback)")
                    logger.info("🎯 DONE FOR THE DAY")
                    
                    # Log execution success and generate report
                    self.report_generator.log_execution_result(True, result.trade_id, result.fill_price)
                    self.report_generator.generate_report()
                    return True
                else:
                    logger.error("❌ Narrow iron butterfly execution failed")
                    
                    # Log execution failure and generate report
                    self.report_generator.log_execution_result(False, error_message=result.error_message)
                    self.report_generator.generate_report()
                    return False
                
        except Exception as e:
            logger.error(f"Error executing wide iron butterfly: {str(e)}")
            return False
    
    def _check_wide_iron_butterfly_fallback(self) -> bool:
        """t = 15:51:40 - Wide iron butterfly fallback check (now unused since we execute narrow immediately)"""
        logger.info("📊 Wide iron butterfly fallback check - but narrow butterfly already executed immediately")
        return True  # Always return True since we handle fallback immediately now
    
    def _check_overshoots(self) -> bool:
        """t = 15:58:55 - Check for overshoot reversals"""
        if not self.state.is_trade_executed():
            logger.info("📊 No trades executed, skipping overshoot check")
            return False
        
        try:
            current_price = self._get_current_spx_price()
            if not current_price:
                return False
            
            executed_trade = self.state.trade_execution
            target_strike = executed_trade.strike_price
            
            # Determine overshoot type and threshold
            if executed_trade.decision_type in [DecisionType.BULL, DecisionType.BEAR, 
                                              DecisionType.NARROW_BULL, DecisionType.NARROW_BEAR]:
                strategy_type = "spread"
                threshold_strikes = self.config.strategy.spread_overshoot_strikes
                target_price = target_strike  # Short leg strike price
            elif executed_trade.decision_type in [DecisionType.IRON_BUTTERFLY, DecisionType.WIDE_IRON_BUTTERFLY]:
                strategy_type = "iron_butterfly"
                threshold_strikes = self.config.strategy.iron_butterfly_overshoot_strikes
                target_price = target_strike  # Center strike price
            else:
                return False
            
            # Calculate overshoot
            overshoot_strikes = StrikeCalculator.calculate_overshoot_strikes(current_price, target_price)
            qualifies = overshoot_strikes >= threshold_strikes
            
            analysis = OvershootAnalysis(
                strategy_type=strategy_type,
                target_strike=target_price,
                current_price=current_price,
                overshoot_strikes=overshoot_strikes,
                threshold_strikes=threshold_strikes,
                qualifies_for_reversal=qualifies
            )
            
            self.state.add_overshoot_analysis(analysis)
            logger.info(f"📊 {analysis}")
            
            if qualifies:
                return self._execute_overshoot_reversal(current_price, target_price)
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking overshoots: {str(e)}")
            return False
    
    def _execute_overshoot_reversal(self, current_price: float, target_price: float) -> bool:
        """Execute overshoot reversal spread at $1.05"""
        try:
            # Determine reversal direction
            if current_price > target_price:
                # Price overshot to the upside, execute bear reversal
                decision = DecisionType.BEAR
                result = self.execution_engine.execute_overshoot_reversal_bear(target_price)
            else:
                # Price overshot to the downside, execute bull reversal
                decision = DecisionType.BULL
                result = self.execution_engine.execute_overshoot_reversal_bull(target_price)
            
            execution = TradeExecution(
                decision_type=decision,
                execution_status=ExecutionStatus.EXECUTED if result.success else ExecutionStatus.FAILED,
                execution_time=result.execution_time,
                trade_id=result.trade_id,
                strike_price=target_price,
                execution_price=result.fill_price,
                error_message=result.error_message,
                is_overshoot_reversal=True
            )
            
            self.state.set_trade_execution(execution)
            
            if result.success:
                logger.info(f"✅ OVERSHOOT REVERSAL {decision.value.upper()} executed at ${result.fill_price:.2f}")
            else:
                logger.error(f"❌ Overshoot reversal execution failed")
            
            return result.success
            
        except Exception as e:
            logger.error(f"Error executing overshoot reversal: {str(e)}")
            return False
    
    def _cancel_narrow_orders(self) -> bool:
        """t = 15:58:59 - Cancel open narrow bull/bear orders"""
        try:
            # Check if we have narrow bull/bear orders to cancel
            if (self.state.trade_execution and 
                self.state.trade_execution.decision_type in [DecisionType.NARROW_BULL, DecisionType.NARROW_BEAR] and
                not self.state.trade_execution.is_successful()):
                
                cancelled_count = self.execution_engine.cancel_narrow_orders()
                logger.info(f"🚫 Cancelled {cancelled_count} narrow orders at session end")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error cancelling narrow orders: {str(e)}")
            return False
    
    def _get_current_spx_price(self) -> Optional[float]:
        """Get current SPX price from the latest bar data"""
        # Use the most recent bar data if available
        if self.state.third_bar:
            return self.state.third_bar.close
        elif self.state.second_bar:
            return self.state.second_bar.close
        elif self.state.first_bar:
            return self.state.first_bar.close
        else:
            # Fallback placeholder if no bar data
            return 5000.0
    
    def get_status_summary(self) -> str:
        """Get comprehensive status summary"""
        return str(self.state)
    
    def should_trade_now(self, current_time: dt.datetime) -> bool:
        """Check if we should be trading at the current time"""
        # This would implement time-based trading rules
        # For now, always return True during market hours
        return True
    
    def reset_for_new_cycle(self):
        """Reset strategy for a new trading cycle"""
        self.state.reset()
        logger.info("🔄 Strategy reset for new trading cycle")
    
    def verify_bar_continuity(self) -> bool:
        """Verify that bar open prices are properly chained and OHLC data is valid (for debugging)"""
        if not (self.state.first_bar and self.state.second_bar and self.state.third_bar):
            logger.warning("🔍 Cannot verify continuity - not all bars available")
            return False
        
        bars = [self.state.first_bar, self.state.second_bar, self.state.third_bar]
        bar_names = ["Bar 1", "Bar 2", "Bar 3"]
        
        # Check price continuity
        bar1_close = self.state.first_bar.close
        bar2_open = self.state.second_bar.open
        bar2_close = self.state.second_bar.close
        bar3_open = self.state.third_bar.open
        
        gap1 = abs(bar1_close - bar2_open)
        gap2 = abs(bar2_close - bar3_open)
        
        logger.info(f"🔍 BAR CONTINUITY & VALIDITY CHECK:")
        logger.info(f"   Bar 1 Close: ${bar1_close:.2f}")
        logger.info(f"   Bar 2 Open:  ${bar2_open:.2f} (gap: {gap1:.2f})")
        logger.info(f"   Bar 2 Close: ${bar2_close:.2f}")
        logger.info(f"   Bar 3 Open:  ${bar3_open:.2f} (gap: {gap2:.2f})")
        
        # Check OHLC validity for each bar
        all_valid = True
        for i, (bar, name) in enumerate(zip(bars, bar_names)):
            # Check that open and close are within high/low range
            open_valid = bar.low <= bar.open <= bar.high
            close_valid = bar.low <= bar.close <= bar.high
            high_low_valid = bar.low <= bar.high
            
            if not (open_valid and close_valid and high_low_valid):
                logger.error(f"❌ {name} INVALID OHLC: O:{bar.open:.2f} H:{bar.high:.2f} L:{bar.low:.2f} C:{bar.close:.2f}")
                if not open_valid:
                    logger.error(f"   Open {bar.open:.2f} outside range [{bar.low:.2f}, {bar.high:.2f}]")
                if not close_valid:
                    logger.error(f"   Close {bar.close:.2f} outside range [{bar.low:.2f}, {bar.high:.2f}]")
                if not high_low_valid:
                    logger.error(f"   High {bar.high:.2f} < Low {bar.low:.2f}")
                all_valid = False
            else:
                logger.debug(f"✅ {name} VALID: O:{bar.open:.2f} H:{bar.high:.2f} L:{bar.low:.2f} C:{bar.close:.2f}")
        
        # Perfect continuity means gaps should be exactly 0.00
        is_continuous = (gap1 < 0.01 and gap2 < 0.01)
        
        if is_continuous and all_valid:
            logger.info(f"✅ PERFECT CONTINUITY & VALID OHLC: All bars properly chained")
        elif is_continuous:
            logger.warning(f"⚠️ CONTINUITY OK BUT INVALID OHLC DATA DETECTED")
        elif all_valid:
            logger.warning(f"⚠️ VALID OHLC BUT GAPS DETECTED: Continuity may not be working")
        else:
            logger.error(f"❌ MULTIPLE ISSUES: Both continuity and OHLC validity problems")
        
        return is_continuous and all_valid 