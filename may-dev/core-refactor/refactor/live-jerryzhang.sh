#!/bin/bash

# Get today's date for log file name
TODAY=$(date +%Y-%m-%d)
LOGFILE="logging/jerryzhang-${TODAY}.log"

echo "💰 Starting <PERSON> LIVE Trading (New Algorithm)..."
echo "System: Core-Refactor with Smart Strikes"
echo "Account: <PERSON> (Main Trading Account)"
echo "Account ID: U15269296"
echo "Port: 4017"
echo "Mode: REAL MONEY TRADING"
echo "Reference Minute: 50 (XX:50:00)"
echo "Risk: 35% Bulls, 30.625% Bears"
echo "Logging to: $LOGFILE"
echo ""
echo "⚠️  WARNING: This will place real trades with real money!"
echo "⚠️  New smart strike selection"
echo "⚠️  $2.45 spread limit, $3.40/$6.50 butterfly minimums"
echo "⚠️  Risk: 35% for bulls, 30.625% for bears"
echo ""
echo "Press Ctrl+C within 5 seconds to cancel..."
echo ""

for i in {5..1}; do
    echo "Starting in $i seconds..."
    sleep 1
done

echo "🚀 LAUNCHING JERRY ZHANG LIVE TRADING..."
echo ""

python3 live_trading_app.py \
    --data-port 4017 \
    --reference-minute 50 \
    --account U15269296 \
    --bull-risk-percentage 35.0 \
    --bear-risk-percentage 30.625 \
    --live 