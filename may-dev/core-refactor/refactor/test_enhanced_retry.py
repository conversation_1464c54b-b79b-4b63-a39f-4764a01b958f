#!/usr/bin/env python3
"""
Test script for enhanced retry logic
"""

import sys
import os
import datetime as dt
import logging

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import TradingConfig
from execution_engine import OptionExecutionEngine
from combo_orders import ComboOrderFactory

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_retry_config():
    """Test the enhanced retry configuration"""
    print("=== Testing Enhanced Retry Configuration ===")
    
    config = TradingConfig()
    
    # Test randomized timing
    start_times = []
    for i in range(10):
        start_time = config.timing.get_randomized_start_time()
        start_times.append(start_time)
        print(f"Random start time {i+1}: 15:51:{start_time:02d}")
    
    # Verify all times are within range
    min_time = min(start_times)
    max_time = max(start_times)
    print(f"Range: 15:51:{min_time:02d} to 15:51:{max_time:02d}")
    
    assert min_time >= config.timing.retry_start_time_min, f"Min time {min_time} below minimum {config.timing.retry_start_time_min}"
    assert max_time <= config.timing.retry_start_time_max, f"Max time {max_time} above maximum {config.timing.retry_start_time_max}"
    
    print("✅ Enhanced retry configuration test passed!")
    print()

def test_combo_optimization():
    """Test combo optimization logic"""
    print("=== Testing Combo Optimization ===")
    
    config = TradingConfig()
    
    # Create a mock execution engine for testing
    class MockIB:
        def __init__(self):
            self.trades = lambda: []
    
    class MockQuoteEngine:
        def get_combo_quote(self, combo_def):
            class MockQuoteResult:
                def __init__(self):
                    self.is_valid = True
                    self.ask = 2.50
                    self.bid = 2.40
            return MockQuoteResult()
    
    class MockExecutionEngine:
        def __init__(self):
            self.config = config
            self.quote_engine = MockQuoteEngine()
        
        def _get_current_spx_price(self):
            return 4500.0
        
        def _get_atm_strike(self, spx_price):
            return round(spx_price / 5) * 5
        
        def _optimize_combo_for_retry(self, original_combo, current_spx_price, original_limit_price):
            # Simulate optimization logic
            atm_strike = self._get_atm_strike(current_spx_price)
            
            if hasattr(original_combo, 'center_strike'):
                strike_distance = abs(atm_strike - original_combo.center_strike)
                if strike_distance > 2.5:
                    # Create new combo with current ATM strike
                    if original_combo.combo_type.value == 'bull_spread':
                        from combo_orders import ComboOrderFactory
                        optimized_combo = ComboOrderFactory.create_bull_spread(atm_strike)
                        # For debit strategies: MID + 0.05c, but must not exceed $2.50
                        mid_price = 2.45  # Simulated mid price
                        raw_price = mid_price + 0.05  # $2.50
                        optimized_price = round(raw_price / 0.05) * 0.05  # Nickel rounding
                        return optimized_combo, optimized_price
            
            return original_combo, original_limit_price
    
    engine = MockExecutionEngine()
    
    # Test with a bull spread
    from combo_orders import ComboOrderFactory
    original_combo = ComboOrderFactory.create_bull_spread(4500)
    original_combo.center_strike = 4495  # 5 points away from ATM
    
    optimized_combo, optimized_price = engine._optimize_combo_for_retry(
        original_combo, 4500.0, 2.50
    )
    
    print(f"Original strike: {original_combo.center_strike}")
    print(f"Optimized strike: {optimized_combo.center_strike}")
    print(f"Original price: ${2.50:.2f}")
    print(f"Optimized price: ${optimized_price:.2f}")
    
    # Verify optimization occurred
    assert optimized_combo.center_strike == 4500, f"Expected strike 4500, got {optimized_combo.center_strike}"
    assert optimized_price == 2.50, f"Expected price $2.50 (MID + 0.05c), got {optimized_price}"
    assert optimized_price <= 2.50, f"Optimized price {optimized_price} exceeds $2.50 limit"
    
    print("✅ Combo optimization test passed!")
    print()

def test_timing_schedule():
    """Test the new timing schedule"""
    print("=== Testing Enhanced Timing Schedule ===")
    
    config = TradingConfig()
    schedule = config.timing.get_timing_schedule()
    
    print("Enhanced timing schedule:")
    for event, timing in schedule.items():
        print(f"  {event}: XX:{timing['minute']:02d}:{timing['second']:02d}")
    
    # Verify enhanced retry timing is included
    assert 'spread_execution' in schedule, "Spread execution timing missing"
    
    print("✅ Enhanced timing schedule test passed!")
    print()

def main():
    """Run all tests"""
    print("🧪 Testing Enhanced Retry Logic Implementation")
    print("=" * 50)
    
    try:
        test_enhanced_retry_config()
        test_combo_optimization()
        test_timing_schedule()
        
        print("🎉 All tests passed! Enhanced retry logic is ready for implementation.")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 