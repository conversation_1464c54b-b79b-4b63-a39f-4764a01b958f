#!/usr/bin/env python3
"""
Strategy State Management for SPX Trading System
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from enum import Enum
import datetime as dt
import logging

logger = logging.getLogger(__name__)


class DecisionType(Enum):
    """Strategy decision types"""
    BULL = "bull"
    BEAR = "bear"
    BF_BULL = "bf_bull"      # Butterfly converted to Bull (>66% close)
    BF_BEAR = "bf_bear"      # <PERSON> converted to Bear (≤66% close)
    NARROW_BULL = "narrow_bull"
    NARROW_BEAR = "narrow_bear"
    IRON_BUTTERFLY = "iron_butterfly"  # Default decision
    WIDE_IRON_BUTTERFLY = "wide_iron_butterfly"  # Submitted for $6.50 credit
    NONE = "none"


class ExecutionStatus(Enum):
    """Trade execution status"""
    PENDING = "pending"
    EXECUTED = "executed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRY_1 = "retry_1"  # First retry
    RETRY_2 = "retry_2"  # Second retry
    RETRY_3 = "retry_3"  # Third retry
    FALLBACK = "fallback"  # Fallback from wide to narrow iron butterfly


@dataclass
class BarData:
    """Represents a single bar of market data"""
    timestamp: dt.datetime
    open: float
    high: float
    low: float
    close: float
    volume: int = 0
    name: Optional[str] = None  # e.g., "first", "second", "third"
    
    @property
    def range_points(self) -> float:
        """Get the range in points"""
        return self.high - self.low
    
    def get_close_percentage(self) -> float:
        """Calculate where the close is as a percentage of the bar's range"""
        if self.high == self.low:
            return 50.0  # If no range, assume middle
        return ((self.close - self.low) / (self.high - self.low)) * 100
    
    def get_close_percentage_in_span(self, span_low: float, span_high: float) -> float:
        """Calculate where the close is as a percentage of a given span"""
        if span_high == span_low:
            return 50.0  # If no span, assume middle
        return ((self.close - span_low) / (span_high - span_low)) * 100
    
    def __str__(self) -> str:
        return f"Bar({self.name}): O:{self.open:.2f} H:{self.high:.2f} L:{self.low:.2f} C:{self.close:.2f} R:{self.range_points:.2f}"


@dataclass
class CombinedBarData:
    """Represents combined bar data from multiple bars"""
    bars: List[BarData]
    name: str  # e.g., "1-minute", "1.5-minute"
    
    @property
    def combined_bar(self) -> BarData:
        """Get the combined OHLC data"""
        if not self.bars:
            raise ValueError("No bars to combine")
        
        return BarData(
            timestamp=self.bars[-1].timestamp,
            open=self.bars[0].open,
            high=max(bar.high for bar in self.bars),
            low=min(bar.low for bar in self.bars),
            close=self.bars[-1].close,
            name=self.name
        )
    
    @property
    def range_points(self) -> float:
        """Get the combined range in points"""
        return self.combined_bar.range_points
    
    def get_close_percentage(self) -> float:
        """Get close percentage for combined bar"""
        return self.combined_bar.get_close_percentage()


@dataclass
class RangeAnalysis:
    """Analysis of bar ranges for decision making"""
    bar_type: str  # "2-bar", "3-bar"
    range_points: float
    threshold: float
    forces_narrow: bool
    
    def __str__(self) -> str:
        status = "forces narrow" if self.forces_narrow else "allows normal"
        return f"{self.bar_type}: {self.range_points:.2f} pts (threshold: {self.threshold}) - {status}"


@dataclass
class SpanData:
    """Represents span calculations for decision making"""
    span_type: str  # "SPAN1", "SPAN2", "SPAN3"
    high: float
    low: float
    bars_included: List[str]  # Names of bars included
    
    @property
    def span_points(self) -> float:
        """Get the span in points"""
        return self.high - self.low
    
    def get_close_percentage(self, close_price: float) -> float:
        """Calculate where a close price is as a percentage of this span"""
        if self.high == self.low:
            return 50.0
        return ((close_price - self.low) / (self.high - self.low)) * 100
    
    def __str__(self) -> str:
        return f"{self.span_type}: H:{self.high:.2f} L:{self.low:.2f} Span:{self.span_points:.2f} ({', '.join(self.bars_included)})"


@dataclass
class OvershootAnalysis:
    """Analysis of price overshoots for reversal trades"""
    strategy_type: str  # "spread" or "iron_butterfly"
    target_strike: float
    current_price: float
    overshoot_strikes: int
    threshold_strikes: int
    qualifies_for_reversal: bool
    
    def __str__(self) -> str:
        status = "qualifies" if self.qualifies_for_reversal else "no action"
        return f"Overshoot ({self.strategy_type}): {self.overshoot_strikes} strikes from {self.target_strike:.0f} (threshold: {self.threshold_strikes}) - {status}"


@dataclass
class DecisionAnalysis:
    """Analysis that led to a trading decision"""
    decision_type: DecisionType
    reason: str
    close_percentage: float
    bar_data: BarData
    span_data: Optional[SpanData] = None
    range_analysis: Optional[RangeAnalysis] = None
    reversal_info: Optional[Dict[str, Any]] = None
    breakout_info: Optional[Dict[str, Any]] = None
    iron_butterfly_strike_direction: Optional[str] = None  # "round_up", "round_down", "round_nearest"
    
    def __str__(self) -> str:
        base = f"Decision: {self.decision_type.value} - {self.reason} (close: {self.close_percentage:.1f}%)"
        if self.span_data:
            base += f" | {self.span_data}"
        return base


@dataclass
class TradeExecution:
    """Information about trade execution"""
    decision_type: DecisionType
    execution_status: ExecutionStatus
    execution_time: Optional[dt.datetime] = None
    trade_id: Optional[str] = None
    strike_price: Optional[float] = None
    execution_price: Optional[float] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    is_overshoot_reversal: bool = False
    original_decision: Optional[DecisionType] = None  # For tracking fallbacks
    
    def is_successful(self) -> bool:
        """Check if trade was successfully executed"""
        return self.execution_status == ExecutionStatus.EXECUTED
    
    def is_retry_status(self) -> bool:
        """Check if currently in retry status"""
        return self.execution_status in [ExecutionStatus.RETRY_1, ExecutionStatus.RETRY_2, ExecutionStatus.RETRY_3]
    
    def get_next_retry_status(self) -> Optional[ExecutionStatus]:
        """Get next retry status"""
        if self.execution_status == ExecutionStatus.PENDING:
            return ExecutionStatus.RETRY_1
        elif self.execution_status == ExecutionStatus.RETRY_1:
            return ExecutionStatus.RETRY_2
        elif self.execution_status == ExecutionStatus.RETRY_2:
            return ExecutionStatus.RETRY_3
        else:
            return None
    
    def __str__(self) -> str:
        if self.execution_status == ExecutionStatus.EXECUTED:
            base = f"Executed {self.decision_type.value} @ {self.execution_time.strftime('%H:%M:%S') if self.execution_time else 'unknown'}"
            if self.execution_price:
                base += f" for ${self.execution_price:.2f}"
            if self.is_overshoot_reversal:
                base += " (overshoot reversal)"
            return base
        else:
            return f"{self.execution_status.value.title()} {self.decision_type.value}"


class StrategyState:
    """Manages the complete state of the trading strategy"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """Reset all strategy state for a new trading cycle"""
        # Bar data
        self.first_bar: Optional[BarData] = None
        self.second_bar: Optional[BarData] = None
        self.third_bar: Optional[BarData] = None
        
        # Combined bar data (legacy support)
        self.one_minute_bar: Optional[CombinedBarData] = None
        self.one_half_minute_bar: Optional[CombinedBarData] = None
        
        # Span calculations for new algorithm
        self.span1: Optional[SpanData] = None  # First bar span
        self.span2: Optional[SpanData] = None  # Two bar span
        self.span3: Optional[SpanData] = None  # Three bar span
        
        # Analysis results
        self.range_analyses: List[RangeAnalysis] = []
        self.decision_history: List[DecisionAnalysis] = []
        self.overshoot_analyses: List[OvershootAnalysis] = []
        
        # Current state - Default to IRON_BUTTERFLY per new algorithm
        self.current_decision: DecisionType = DecisionType.IRON_BUTTERFLY
        self.current_analysis: Optional[DecisionAnalysis] = None
        
        # Bias tracking for iron butterfly strike selection
        self.current_bias: int = 0  # -1=down, 0=neutral, +1=up
        
        # Execution state
        self.trade_execution: Optional[TradeExecution] = None
        self.overshoot_executions: List[TradeExecution] = []  # Track overshoot reversals
        
        # Wide iron butterfly fallback tracking
        self.wide_iron_butterfly_attempted: bool = False
        self.wide_iron_butterfly_filled: bool = False
        
        # Dual trade tracking
        self.dual_trade_intended: bool = False
        self.dual_trade_type: Optional[str] = None  # "iron_butterfly"
        self.dual_trade_strike: Optional[float] = None
        
        # Analysis tracking flags (prevent duplicate analysis on same data)
        self._first_bar_analyzed: bool = False
        self._second_bar_analyzed: bool = False
        self._third_bar_analyzed: bool = False
        
        # Track last analyzed bar close prices to detect changes
        self._last_analyzed_bar1_close: Optional[float] = None
        self._last_analyzed_bar2_close: Optional[float] = None
        self._last_analyzed_bar3_close: Optional[float] = None
        
        # Session info
        self.session_start_time: Optional[dt.datetime] = None
        self.session_end_time: Optional[dt.datetime] = None
        self.session_start_price: Optional[float] = None
    
    def start_session(self, start_time: dt.datetime = None, start_price: Optional[float] = None):
        """Start a new trading session"""
        start_time = start_time or dt.datetime.now()
        self.reset()
        self.session_start_time = start_time
        self.session_start_price = start_price  # Store the SPX price when session starts
        if start_price:
            logger.info(f"📈 Session started at ${start_price:.2f}")
    
    def end_session(self, end_time: dt.datetime = None):
        """End the current trading session"""
        self.session_end_time = end_time or dt.datetime.now()
    
    def set_bar_data(self, bar_number: int, bar_data: BarData, previous_close: Optional[float] = None):
        """Set bar data (1=first, 2=second, 3=third) and calculate spans
        
        Note: For all bars, the open price is adjusted to match the previous bar's close
        to ensure continuity and prevent gaps between bars. High and low are also
        adjusted if needed to maintain valid OHLC data.
        
        Args:
            bar_number: Which bar this is (1, 2, or 3)
            bar_data: The raw bar data
            previous_close: For bar 1, the close price from before the session started
        """
        # Debug logging to show original vs adjusted data
        original_open = bar_data.open
        
        if bar_number == 1:
            # Adjust bar 1's open to match previous close (session start price)
            if previous_close is not None:
                # Adjust high and low if new open is outside the original range
                adjusted_high = max(bar_data.high, previous_close)
                adjusted_low = min(bar_data.low, previous_close)
                
                adjusted_bar_data = BarData(
                    timestamp=bar_data.timestamp,
                    open=previous_close,  # Use previous close as open
                    high=adjusted_high,   # Expand high if needed
                    low=adjusted_low,     # Expand low if needed
                    close=bar_data.close,
                    volume=bar_data.volume,
                    name=bar_data.name
                )
                self.first_bar = adjusted_bar_data
                
                # Debug logging with high/low adjustments
                high_adj = adjusted_high != bar_data.high
                low_adj = adjusted_low != bar_data.low
                logger.info(f"🔧 Bar 1 Open Adjustment: ${original_open:.2f} → ${previous_close:.2f} (gap: {abs(original_open - previous_close):.2f})")
                if high_adj:
                    logger.info(f"📈 Bar 1 High Adjusted: ${bar_data.high:.2f} → ${adjusted_high:.2f}")
                if low_adj:
                    logger.info(f"📉 Bar 1 Low Adjusted: ${bar_data.low:.2f} → ${adjusted_low:.2f}")
            else:
                # Fallback to original data if no previous close provided
                self.first_bar = bar_data
                logger.info(f"ℹ️ Bar 1: No adjustment (no previous close available)")
                
            # Calculate SPAN1
            self.span1 = SpanData(
                span_type="SPAN1",
                high=self.first_bar.high,
                low=self.first_bar.low,
                bars_included=["first"]
            )
        elif bar_number == 2:
            # Adjust bar 2's open to match bar 1's close
            if self.first_bar:
                # Adjust high and low if new open is outside the original range
                adjusted_high = max(bar_data.high, self.first_bar.close)
                adjusted_low = min(bar_data.low, self.first_bar.close)
                
                adjusted_bar_data = BarData(
                    timestamp=bar_data.timestamp,
                    open=self.first_bar.close,  # Use previous bar's close as open
                    high=adjusted_high,         # Expand high if needed
                    low=adjusted_low,           # Expand low if needed
                    close=bar_data.close,
                    volume=bar_data.volume,
                    name=bar_data.name
                )
                self.second_bar = adjusted_bar_data
                
                # Debug logging with high/low adjustments
                high_adj = adjusted_high != bar_data.high
                low_adj = adjusted_low != bar_data.low
                logger.info(f"🔧 Bar 2 Open Adjustment: ${original_open:.2f} → ${self.first_bar.close:.2f} (gap: {abs(original_open - self.first_bar.close):.2f})")
                if high_adj:
                    logger.info(f"📈 Bar 2 High Adjusted: ${bar_data.high:.2f} → ${adjusted_high:.2f}")
                if low_adj:
                    logger.info(f"📉 Bar 2 Low Adjusted: ${bar_data.low:.2f} → ${adjusted_low:.2f}")
            else:
                # Fallback if first bar doesn't exist
                self.second_bar = bar_data
                logger.info(f"⚠️ Bar 2: No adjustment (no first bar available)")
                
            # Calculate SPAN2
            if self.first_bar:
                self.span2 = SpanData(
                    span_type="SPAN2",
                    high=max(self.first_bar.high, self.second_bar.high),
                    low=min(self.first_bar.low, self.second_bar.low),
                    bars_included=["first", "second"]
                )
                # Create 1-minute combined bar for legacy support
                self.one_minute_bar = CombinedBarData(
                    bars=[self.first_bar, self.second_bar],
                    name="1-minute"
                )
        elif bar_number == 3:
            # Adjust bar 3's open to match bar 2's close
            if self.second_bar:
                # Adjust high and low if new open is outside the original range
                adjusted_high = max(bar_data.high, self.second_bar.close)
                adjusted_low = min(bar_data.low, self.second_bar.close)
                
                adjusted_bar_data = BarData(
                    timestamp=bar_data.timestamp,
                    open=self.second_bar.close,  # Use previous bar's close as open
                    high=adjusted_high,          # Expand high if needed
                    low=adjusted_low,            # Expand low if needed
                    close=bar_data.close,
                    volume=bar_data.volume,
                    name=bar_data.name
                )
                self.third_bar = adjusted_bar_data
                
                # Debug logging with high/low adjustments
                high_adj = adjusted_high != bar_data.high
                low_adj = adjusted_low != bar_data.low
                logger.info(f"🔧 Bar 3 Open Adjustment: ${original_open:.2f} → ${self.second_bar.close:.2f} (gap: {abs(original_open - self.second_bar.close):.2f})")
                if high_adj:
                    logger.info(f"📈 Bar 3 High Adjusted: ${bar_data.high:.2f} → ${adjusted_high:.2f}")
                if low_adj:
                    logger.info(f"📉 Bar 3 Low Adjusted: ${bar_data.low:.2f} → ${adjusted_low:.2f}")
            else:
                # Fallback if second bar doesn't exist
                self.third_bar = bar_data
                logger.info(f"⚠️ Bar 3: No adjustment (no second bar available)")
                
            # Calculate SPAN3
            if self.first_bar and self.second_bar:
                self.span3 = SpanData(
                    span_type="SPAN3",
                    high=max(self.first_bar.high, self.second_bar.high, self.third_bar.high),
                    low=min(self.first_bar.low, self.second_bar.low, self.third_bar.low),
                    bars_included=["first", "second", "third"]
                )
                # Create 1.5-minute combined bar for legacy support
                self.one_half_minute_bar = CombinedBarData(
                    bars=[self.first_bar, self.second_bar, self.third_bar],
                    name="1.5-minute"
                )
    
    def add_range_analysis(self, analysis: RangeAnalysis):
        """Add range analysis result"""
        self.range_analyses.append(analysis)
    
    def add_overshoot_analysis(self, analysis: OvershootAnalysis):
        """Add overshoot analysis result"""
        self.overshoot_analyses.append(analysis)
    
    def set_decision(self, decision: DecisionType, analysis: DecisionAnalysis):
        """Set the current decision with analysis"""
        self.current_decision = decision
        self.current_analysis = analysis
        self.decision_history.append(analysis)
    
    def update_decision(self, new_decision: DecisionType, reason: str, span_data: Optional[SpanData] = None):
        """Update the current decision (for reversals/breakouts)"""
        old_decision = self.current_decision
        self.current_decision = new_decision
        
        # Create new analysis for the decision change
        analysis = DecisionAnalysis(
            decision_type=new_decision,
            reason=f"Changed from {old_decision.value}: {reason}",
            close_percentage=0.0,  # Will be updated if needed
            bar_data=self.get_latest_bar() or BarData(dt.datetime.now(), 0, 0, 0, 0),
            span_data=span_data
        )
        self.decision_history.append(analysis)
    
    def set_trade_execution(self, execution: TradeExecution):
        """Set trade execution information"""
        if execution.is_overshoot_reversal:
            self.overshoot_executions.append(execution)
        else:
            self.trade_execution = execution
    
    def update_trade_execution_status(self, new_status: ExecutionStatus, error_message: Optional[str] = None):
        """Update the current trade execution status"""
        if self.trade_execution:
            self.trade_execution.execution_status = new_status
            if error_message:
                self.trade_execution.error_message = error_message
            if new_status in [ExecutionStatus.RETRY_1, ExecutionStatus.RETRY_2, ExecutionStatus.RETRY_3]:
                self.trade_execution.retry_count += 1
    
    def set_wide_iron_butterfly_attempted(self, attempted: bool = True):
        """Mark that wide iron butterfly was attempted"""
        self.wide_iron_butterfly_attempted = attempted
    
    def set_wide_iron_butterfly_filled(self, filled: bool = True):
        """Mark that wide iron butterfly was filled"""
        self.wide_iron_butterfly_filled = filled
    
    def get_latest_bar(self) -> Optional[BarData]:
        """Get the most recent bar data"""
        if self.third_bar:
            return self.third_bar
        elif self.second_bar:
            return self.second_bar
        elif self.first_bar:
            return self.first_bar
        return None
    
    def get_latest_combined_bar(self) -> Optional[CombinedBarData]:
        """Get the most recent combined bar data"""
        if self.one_half_minute_bar:
            return self.one_half_minute_bar
        elif self.one_minute_bar:
            return self.one_minute_bar
        return None
    
    def get_latest_span(self) -> Optional[SpanData]:
        """Get the most recent span data"""
        if self.span3:
            return self.span3
        elif self.span2:
            return self.span2
        elif self.span1:
            return self.span1
        return None
    
    def has_bar_data(self, bar_number: int) -> bool:
        """Check if we have specific bar data"""
        if bar_number == 1:
            return self.first_bar is not None
        elif bar_number == 2:
            return self.second_bar is not None
        elif bar_number == 3:
            return self.third_bar is not None
        return False
    
    def is_trade_executed(self) -> bool:
        """Check if a trade has been executed"""
        return (self.trade_execution is not None and 
                self.trade_execution.is_successful())
    
    def get_decision_summary(self) -> str:
        """Get a summary of the current decision state"""
        summary = f"Current Decision: {self.current_decision.value}"
        if self.current_analysis:
            summary += f" ({self.current_analysis.reason})"
        
        if self.is_trade_executed():
            summary += f" - EXECUTED"
        else:
            summary += f" - PENDING"
        
        return summary
    
    def get_bar_summary(self) -> str:
        """Get a summary of bar data"""
        bars = []
        if self.first_bar:
            bars.append(f"1st: {self.first_bar}")
        if self.second_bar:
            bars.append(f"2nd: {self.second_bar}")
        if self.third_bar:
            bars.append(f"3rd: {self.third_bar}")
        
        if self.one_minute_bar:
            bars.append(f"Combined 1-min: {self.one_minute_bar.combined_bar}")
        if self.one_half_minute_bar:
            bars.append(f"Combined 1.5-min: {self.one_half_minute_bar.combined_bar}")
        
        return "\n".join(bars)
    
    def get_range_summary(self) -> str:
        """Get a summary of range analyses"""
        if not self.range_analyses:
            return "No range analyses"
        
        return "\n".join(str(analysis) for analysis in self.range_analyses)
    
    def get_session_duration(self) -> Optional[dt.timedelta]:
        """Get the duration of the current session"""
        if self.session_start_time and self.session_end_time:
            return self.session_end_time - self.session_start_time
        elif self.session_start_time:
            return dt.datetime.now() - self.session_start_time
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert strategy state to dictionary for serialization"""
        return {
            'current_decision': self.current_decision.value,
            'trade_executed': self.is_trade_executed(),
            'execution_time': self.trade_execution.execution_time.isoformat() if (
                self.trade_execution and self.trade_execution.execution_time
            ) else None,
            'session_start': self.session_start_time.isoformat() if self.session_start_time else None,
            'session_end': self.session_end_time.isoformat() if self.session_end_time else None,
            'bars_collected': {
                'first': self.first_bar is not None,
                'second': self.second_bar is not None,
                'third': self.third_bar is not None
            },
            'decision_history': [
                {
                    'decision': analysis.decision_type.value,
                    'reason': analysis.reason,
                    'close_pct': analysis.close_percentage
                }
                for analysis in self.decision_history
            ],
            'range_analyses': [
                {
                    'type': analysis.bar_type,
                    'range': analysis.range_points,
                    'threshold': analysis.threshold,
                    'forces_narrow': analysis.forces_narrow
                }
                for analysis in self.range_analyses
            ]
        }
    
    def __str__(self) -> str:
        """String representation of strategy state"""
        lines = [
            f"Strategy State:",
            f"  {self.get_decision_summary()}",
            f"  Session: {self.session_start_time.strftime('%H:%M:%S') if self.session_start_time else 'Not started'}",
        ]
        
        if self.get_latest_bar():
            lines.append(f"  Latest Bar: {self.get_latest_bar()}")
        
        if self.range_analyses:
            lines.append(f"  Range Analysis: {self.range_analyses[-1]}")
        
        return "\n".join(lines) 