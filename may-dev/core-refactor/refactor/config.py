#!/usr/bin/env python3
"""
Centralized Configuration Management for SPX Trading System
"""

from dataclasses import dataclass, field
from typing import Dict, Optional
import datetime as dt


@dataclass
class TimingConfig:
    """Strategy timing configuration"""
    reference_minute: int = 50  # XX:50:00 for first bar
    first_bar_second: int = 30  # XX:50:30 for first bar analysis
    second_bar_minute_offset: int = 1  # XX:51:00 for second bar
    third_bar_second: int = 30  # XX:51:30 for third bar analysis
    spread_execution_second: int = 31  # XX:51:31 for spread execution
    iron_butterfly_execution_second: int = 31  # XX:51:31 for iron butterfly execution
    wide_iron_butterfly_fallback_second: int = 40  # XX:51:40 for fallback check
    overshoot_check_minute: int = 8  # XX:58:55
    overshoot_check_second: int = 55
    cancel_narrow_minute: int = 8  # XX:58:59
    cancel_narrow_second: int = 59
    
    # Enhanced retry timing configuration
    # Initial start time (fixed at 15:51:36)
    retry_initial_start_second: int = 36  # 15:51:36 for initial start
    
    # Randomized start time range (seconds) - for variation
    retry_start_time_min: int = 31  # 15:51:31
    retry_start_time_max: int = 36  # 15:51:36
    
    # Dynamic retry configuration
    retry_interval_seconds: int = 3  # Wait 3 seconds between retries
    max_retry_attempts: int = 3  # Maximum 3 retry attempts
    final_wait_seconds: int = 3  # Wait 3 seconds after last submission before fallback
    
    # Latest possible execution time
    retry_latest_execution_second: int = 45  # 15:51:45 latest execution
    
    # Legacy retry timings (kept for backward compatibility)
    spread_retry_1: int = 2
    spread_retry_2: int = 5
    spread_retry_3: int = 8
    
    def get_timing_schedule(self) -> Dict[str, Dict[str, int]]:
        """Get complete timing schedule"""
        return {
            'first_bar_analysis': {
                'minute': self.reference_minute,
                'second': self.first_bar_second
            },
            'second_bar_analysis': {
                'minute': (self.reference_minute + self.second_bar_minute_offset) % 60,
                'second': 0
            },
            'third_bar_analysis': {
                'minute': (self.reference_minute + self.second_bar_minute_offset) % 60,
                'second': self.third_bar_second
            },
            'spread_execution': {
                'minute': (self.reference_minute + self.second_bar_minute_offset) % 60,
                'second': self.spread_execution_second
            },
            'iron_butterfly_execution': {
                'minute': (self.reference_minute + self.second_bar_minute_offset) % 60,
                'second': self.iron_butterfly_execution_second
            },
            'wide_iron_butterfly_fallback': {
                'minute': (self.reference_minute + self.second_bar_minute_offset) % 60,
                'second': self.wide_iron_butterfly_fallback_second
            },
            'overshoot_check': {
                'minute': self.overshoot_check_minute,
                'second': self.overshoot_check_second
            },
            'cancel_narrow': {
                'minute': self.cancel_narrow_minute,
                'second': self.cancel_narrow_second
            }
        }
    
    def get_randomized_start_time(self) -> int:
        """Get a randomized start time between min and max seconds"""
        import random
        return random.randint(self.retry_start_time_min, self.retry_start_time_max)


@dataclass
class StrategyConfig:
    """Strategy decision parameters"""
    # Updated to match new algorithm percentages
    bear_threshold_lower: float = 12.0 # Bear if close < 12% * span
    bull_threshold_upper: float = 88.0  # Bull if close > 88% * span
    
    # Reversal thresholds for second bar
    reversal_bear_threshold: float = bear_threshold_lower  # 12% for bear reversal
    reversal_bull_threshold: float = bull_threshold_upper  # 88% for bull reversal
    
    # Final reversal thresholds for third bar (same as second)
    final_reversal_bear_threshold: float = bear_threshold_lower
    final_reversal_bull_threshold: float = bull_threshold_upper
    
    # Range threshold for narrow conversions
    narrow_range_threshold: float = 4.0  # SPAN3 < 4 forces narrow
    
    # Iron butterfly strike selection thresholds
    iron_butterfly_upper_threshold: float = 70.0  # Round up if > 70%
    iron_butterfly_lower_threshold: float = 30.0  # Round down if < 30%
    
    # Overshoot thresholds
    spread_overshoot_strikes: int = 1  # 1+ strikes for spread overshoot
    iron_butterfly_overshoot_strikes: int = 2  # 2+ strikes for iron butterfly overshoot
    
    def is_bear_signal_first_bar(self, close_percentage: float) -> bool:
        """Check if first bar close percentage indicates bear signal"""
        return close_percentage < self.bear_threshold_lower
    
    def is_bull_signal_first_bar(self, close_percentage: float) -> bool:
        """Check if first bar close percentage indicates bull signal"""
        return close_percentage > self.bull_threshold_upper
    
    def is_bear_reversal(self, close_percentage: float) -> bool:
        """Check for bear reversal condition"""
        return close_percentage < self.reversal_bear_threshold
    
    def is_bull_reversal(self, close_percentage: float) -> bool:
        """Check for bull reversal condition"""
        return close_percentage > self.reversal_bull_threshold
    
    def is_small_range(self, span_points: float) -> bool:
        """Check if span is below threshold (forces narrow versions)"""
        return span_points < self.narrow_range_threshold
    
    def get_iron_butterfly_strike_direction(self, close_percentage: float) -> str:
        """Get iron butterfly strike direction based on close percentage"""
        if close_percentage > self.iron_butterfly_upper_threshold:
            return "round_up"
        elif close_percentage < self.iron_butterfly_lower_threshold:
            return "round_down"
        else:
            return "round_nearest"
    
    def get_bias_from_percentage(self, close_percentage: float) -> int:
        """Get bias value (-1, 0, +1) based on close percentage in SPAN"""
        if close_percentage >= self.iron_butterfly_upper_threshold:
            return +1  # bias up
        elif close_percentage <= self.iron_butterfly_lower_threshold:
            return -1  # bias down
        else:
            return 0   # bias neutral
    
    def is_bias_up(self, close_percentage: float) -> bool:
        """Check if close percentage indicates bias up (>=70%)"""
        return close_percentage >= self.iron_butterfly_upper_threshold
    
    def is_bias_down(self, close_percentage: float) -> bool:
        """Check if close percentage indicates bias down (<=30%)"""
        return close_percentage <= self.iron_butterfly_lower_threshold
    
    def is_bias_neutral(self, close_percentage: float) -> bool:
        """Check if close percentage indicates bias neutral (30% < close < 70%)"""
        return (close_percentage > self.iron_butterfly_lower_threshold and 
                close_percentage < self.iron_butterfly_upper_threshold)


@dataclass
class PricingConfig:
    """Pricing and execution limits"""
    # Updated to match new standing rules - ALL PRICES MUST BE NICKEL MULTIPLES
    spread_price_limit: float = 2.50  # Max debit for spreads (already nickel multiple)
    narrow_spread_limit: float = 2.05  # Max debit for narrow spreads (already nickel multiple)
    narrow_iron_butterfly_credit_min: float = 3.40  # Min credit for narrow iron butterfly (already nickel multiple)
    wide_iron_butterfly_credit_min: float = 6.50  # Min credit for wide iron butterfly (already nickel multiple)
    
    # Overshoot reversal pricing - FIXED to nickel multiple
    overshoot_reversal_limit: float = 1.05  # Max debit for overshoot reversals (already nickel multiple)
    
    # Live test mode pricing - FIXED to nickel multiples
    narrow_iron_butterfly_test_credit: float = 4.90  # Test credit for narrow (already nickel multiple)
    wide_iron_butterfly_test_credit: float = 9.50   # Test credit for wide (already nickel multiple)
    
    # Wide iron butterfly fallback threshold
    wide_iron_butterfly_fallback_min: float = 6.00  # Check for $6.00+ before fallback (already nickel multiple)
    
    # Risk management for position sizing - NOW CONFIGURABLE VIA COMMAND LINE
    order_size_risk_percentage: float = 7.0  # Default percentage of wallet balance to risk per trade
    order_size_risk_percentage_bull: float = 8.0  # Higher risk percentage for bull strategies
    order_size_risk_percentage_bear: float = 7.0  # Risk percentage for bear strategies
    order_size_risk_percentage_butterfly: float = 7.0  # Risk percentage for butterfly strategies
    order_size_risk_percentage_other: float = 7.0  # Risk percentage for other strategies (fallback)
    
    def get_strategy_risk_percentage(self, combo_type: str) -> float:
        """Get appropriate risk percentage based on strategy type
        
        Args:
            combo_type: The combo type from ComboType enum
            
        Returns:
            Risk percentage based on strategy type
        """
        # Bull strategies get higher risk percentage
        bull_strategies = [
            'bull_spread', 'narrow_bull_spread', 
            'one_better_narrow_bull', 'overshoot_reversal_bull'
        ]
        
        # Bear strategies
        bear_strategies = [
            'bear_spread', 'narrow_bear_spread',
            'one_better_narrow_bear', 'overshoot_reversal_bear'
        ]
        
        # Butterfly strategies
        butterfly_strategies = [
            'iron_butterfly', 'narrow_iron_butterfly', 'wide_iron_butterfly'
        ]
        
        if combo_type in bull_strategies:
            return self.order_size_risk_percentage_bull
        elif combo_type in bear_strategies:
            return self.order_size_risk_percentage_bear
        elif combo_type in butterfly_strategies:
            return self.order_size_risk_percentage_butterfly
        else:
            return self.order_size_risk_percentage_other


@dataclass
class LiveTestConfig:
    """Live test mode configuration"""
    enabled: bool = False
    test_order_price: float = 0.25  # Price to use for test orders (nickel multiple)
    test_cancel_seconds: int = 15  # Seconds before canceling test orders
    
    def is_live_test_mode(self) -> bool:
        """Check if live test mode is enabled"""
        return self.enabled


@dataclass
class OrderConfig:
    """Order execution configuration"""
    default_quantity: int = 1
    order_timeout_seconds: int = 30  # Default order timeout
    iron_butterfly_timeout: int = 9  # Specific timeout for iron butterfly
    combo_order_exchange: str = "SMART"
    option_symbol: str = "SPX"
    option_trading_class: str = "SPXW"


@dataclass
class ConnectionConfig:
    """IB Gateway connection configuration"""
    data_host: str = "127.0.0.1"
    data_port: int = 4015
    trading_host: str = "127.0.0.1"
    trading_port: int = 4015
    client_id_market_data: int = 10
    client_id_trading: int = 11
    request_timeout: float = 5.0


@dataclass
class TradingConfig:
    """Complete trading system configuration"""
    timing: TimingConfig = field(default_factory=TimingConfig)
    strategy: StrategyConfig = field(default_factory=StrategyConfig)
    pricing: PricingConfig = field(default_factory=PricingConfig)
    live_test: LiveTestConfig = field(default_factory=LiveTestConfig)
    orders: OrderConfig = field(default_factory=OrderConfig)
    connection: ConnectionConfig = field(default_factory=ConnectionConfig)
    
    # Account settings
    account_id: Optional[str] = None
    is_paper_trading: bool = False
    
    @classmethod
    def create_from_args(cls, args) -> 'TradingConfig':
        """Create configuration from command line arguments"""
        config = cls()
        
        # Update timing if provided
        if hasattr(args, 'reference_minute') and args.reference_minute is not None:
            config.timing.reference_minute = args.reference_minute
        
        # Update strategy thresholds
        if hasattr(args, 'bear_threshold') and args.bear_threshold is not None:
            config.strategy.bear_threshold_lower = args.bear_threshold
        if hasattr(args, 'bull_threshold') and args.bull_threshold is not None:
            config.strategy.bull_threshold_upper = args.bull_threshold
        
        # Update pricing
        if hasattr(args, 'spread_limit') and args.spread_limit is not None:
            config.pricing.spread_price_limit = args.spread_limit
        if hasattr(args, 'risk_percentage') and args.risk_percentage is not None:
            config.pricing.order_size_risk_percentage = args.risk_percentage
        
        # Update risk percentages for different strategy types
        if hasattr(args, 'bull_risk_percentage') and args.bull_risk_percentage is not None:
            config.pricing.order_size_risk_percentage_bull = args.bull_risk_percentage
        if hasattr(args, 'bear_risk_percentage') and args.bear_risk_percentage is not None:
            config.pricing.order_size_risk_percentage_bear = args.bear_risk_percentage
        if hasattr(args, 'butterfly_risk_percentage') and args.butterfly_risk_percentage is not None:
            config.pricing.order_size_risk_percentage_butterfly = args.butterfly_risk_percentage
        if hasattr(args, 'other_risk_percentage') and args.other_risk_percentage is not None:
            config.pricing.order_size_risk_percentage_other = args.other_risk_percentage
        
        # Update live test configuration
        if hasattr(args, 'live_test') and args.live_test:
            config.live_test.enabled = True
            if hasattr(args, 'test_price'):
                config.live_test.test_order_price = args.test_price
            if hasattr(args, 'test_cancel_seconds'):
                config.live_test.test_cancel_seconds = args.test_cancel_seconds
        
        # Update connection settings
        if hasattr(args, 'data_port') and args.data_port is not None:
            config.connection.data_port = args.data_port
            config.connection.trading_port = args.data_port
        
        # Update account settings
        if hasattr(args, 'account') and args.account is not None:
            config.account_id = args.account
        if hasattr(args, 'live') and args.live:
            config.is_paper_trading = False
        
        return config
    
    def get_effective_butterfly_credit(self, strategy_type: str) -> float:
        """Get effective credit requirement based on mode and type"""
        if self.live_test.enabled:
            if strategy_type in ['narrow', 'narrow_iron_butterfly']:
                return self.pricing.narrow_iron_butterfly_test_credit
            elif strategy_type in ['wide', 'iron_butterfly']:
                return self.pricing.wide_iron_butterfly_test_credit
        
        if strategy_type in ['narrow', 'narrow_iron_butterfly']:
            return self.pricing.narrow_iron_butterfly_credit_min
        elif strategy_type in ['wide', 'iron_butterfly']:
            return self.pricing.wide_iron_butterfly_credit_min
        else:
            return self.pricing.narrow_iron_butterfly_credit_min
    
    def get_effective_order_price(self, calculated_price: float, strategy_type: str = None) -> float:
        """Get effective order price (test price if in live test mode)"""
        if self.live_test.enabled:
            # For butterfly strategies, use the appropriate test credit instead of generic test price
            if strategy_type in ['narrow_iron_butterfly', 'iron_butterfly']:
                return self.pricing.narrow_iron_butterfly_test_credit  # $4.90
            elif strategy_type in ['wide_iron_butterfly']:
                return self.pricing.wide_iron_butterfly_test_credit  # $9.20
            else:
                # For debit strategies (spreads), use the low test price to prevent fills
                return self.live_test.test_order_price  # $0.50
        return calculated_price
    
    def should_cancel_after_timeout(self) -> bool:
        """Check if orders should be cancelled after timeout in live test mode"""
        return self.live_test.enabled
    
    def validate(self) -> bool:
        """Validate configuration parameters"""
        # Validate strategy thresholds
        if self.strategy.bear_threshold_lower >= self.strategy.bull_threshold_upper:
            raise ValueError("Bear threshold must be less than bull threshold")
        
        if not (0 <= self.strategy.bear_threshold_lower <= 100):
            raise ValueError("Bear threshold must be between 0 and 100")
        
        if not (0 <= self.strategy.bull_threshold_upper <= 100):
            raise ValueError("Bull threshold must be between 0 and 100")
        
        # Validate timing
        if not (0 <= self.timing.reference_minute <= 59):
            raise ValueError("Reference minute must be between 0 and 59")
        
        # Validate pricing
        if self.pricing.spread_price_limit <= 0:
            raise ValueError("Spread price limit must be positive")
        
        # Validate risk percentages
        risk_percentages = [
            self.pricing.order_size_risk_percentage,
            self.pricing.order_size_risk_percentage_bull,
            self.pricing.order_size_risk_percentage_bear,
            self.pricing.order_size_risk_percentage_butterfly,
            self.pricing.order_size_risk_percentage_other
        ]
        
        for i, risk_pct in enumerate(risk_percentages):
            if risk_pct <= 0:
                raise ValueError(f"Risk percentage {i} must be positive, got {risk_pct}")
            if risk_pct > 100:
                raise ValueError(f"Risk percentage {i} cannot exceed 100%, got {risk_pct}")
        
        return True


# Global configuration instance
_global_config: Optional[TradingConfig] = None


def set_global_config(config: TradingConfig) -> None:
    """Set the global configuration instance"""
    global _global_config
    config.validate()
    _global_config = config


def get_global_config() -> TradingConfig:
    """Get the global configuration instance"""
    global _global_config
    if _global_config is None:
        _global_config = TradingConfig()
    return _global_config


def create_config_from_args(args) -> TradingConfig:
    """Create and set global configuration from command line arguments"""
    config = TradingConfig.create_from_args(args)
    set_global_config(config)
    return config 