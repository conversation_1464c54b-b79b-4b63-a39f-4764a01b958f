#!/usr/bin/env python3
"""
Generate Balance Report from Multiple Trading Accounts
"""

import datetime as dt
import sys
import os
from typing import Dict, Optional
import pytz

# Add parent directory (core-refactor) to path for imports
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from ib_insync import IB
import logging

# Set up logging
logging.basicConfig(level=logging.WARNING)  # Suppress most IB logs


class BalanceReporter:
    """Generate balance reports from multiple trading accounts"""
    
    def __init__(self):
        # July 1st baseline balances (actual starting balances)
        self.accounts = {
            'PhaedrusTrader': {'port': 4013, 'balance': None, 'error': None, 'display_name': '<PERSON>', 'starting_balance': 5740.35},
            'CoupledLogic': {'port': 4015, 'balance': None, 'error': None, 'display_name': 'Jack', 'starting_balance': 5773.53}
        }
    
    def get_account_balance(self, port: int, account_name: str) -> Optional[float]:
        """Get account balance from IB Gateway"""
        ib = IB()
        try:
            print(f"📡 Connecting to {account_name} on port {port}...")
            ib.connect('127.0.0.1', port, clientId=99)  # Use unique client ID
            
            # Get account summary
            account_summary = ib.accountSummary()
            
            # Find the net liquidation value
            for item in account_summary:
                if item.tag == 'NetLiquidation' and item.currency == 'USD':
                    balance = float(item.value)
                    print(f"✅ {account_name}: ${balance:,.2f}")
                    return balance
            
            print(f"❌ {account_name}: Could not find USD NetLiquidation value")
            return None
            
        except Exception as e:
            print(f"❌ {account_name}: Connection failed - {str(e)}")
            return None
        finally:
            try:
                ib.disconnect()
            except:
                pass
    
    def collect_balances(self):
        """Collect balances from all accounts"""
        print("🏦 Collecting account balances...\n")
        
        for account_name, info in self.accounts.items():
            try:
                balance = self.get_account_balance(info['port'], account_name)
                if balance is not None:
                    info['balance'] = balance
                else:
                    info['error'] = "Could not retrieve balance"
            except Exception as e:
                info['error'] = str(e)
        
        print()  # Empty line for spacing
    
    def calculate_performance(self, current_balance: float, starting_balance: float) -> Dict[str, float]:
        """Calculate performance metrics"""
        if current_balance is None:
            return {'gain_loss': 0.0, 'percentage': 0.0}
        
        gain_loss = current_balance - starting_balance
        percentage = (gain_loss / starting_balance) * 100
        
        return {
            'gain_loss': gain_loss,
            'percentage': percentage
        }
    
    def generate_report(self) -> str:
        """Generate plain text balance report"""
        # Get Eastern time
        eastern = pytz.timezone('US/Eastern')
        timestamp = dt.datetime.now(eastern)
        report_date = timestamp.strftime("%Y%m%d_%H%M%S")
        
        # Calculate totals
        total_current = 0.0
        total_starting = 0.0
        successful_accounts = 0
        
        report_lines = [
            f"Trading Account Balance Report - Generated: {timestamp.strftime('%B %d, %Y at %I:%M:%S %p')} ET",
            f"",
            f"Combined Performance Summary",
            f""
        ]
        
        # Collect data for accounts
        account_data = {}
        for account_name, info in self.accounts.items():
            if info['balance'] is not None:
                performance = self.calculate_performance(info['balance'], info['starting_balance'])
                total_current += info['balance']
                total_starting += info['starting_balance']
                successful_accounts += 1
                
                account_data[account_name] = {
                    'balance': info['balance'],
                    'performance': performance,
                    'display_name': info['display_name']
                }
        
        # Add combined summary
        if successful_accounts > 0:
            combined_gain_loss = total_current - total_starting
            combined_percentage = (combined_gain_loss / total_starting) * 100
            
            gain_loss_str = f"+${combined_gain_loss:,.2f}" if combined_gain_loss >= 0 else f"-${abs(combined_gain_loss):,.2f}"
            percentage_str = f"+{combined_percentage:.2f}%" if combined_percentage >= 0 else f"{combined_percentage:.2f}%"
            
            report_lines.extend([
                f"Total Starting Balance: ${total_starting:,.2f}",
                f"Total Current Balance: ${total_current:,.2f}",
                f"Combined Gain/Loss: {gain_loss_str}",
                f"Combined Percentage: {percentage_str}",
                f""
            ])
        
        # Add individual account details
        for account_name, info in self.accounts.items():
            if info['balance'] is not None:
                performance = self.calculate_performance(info['balance'], info['starting_balance'])
                display_name = info['display_name']
                
                gain_loss_str = f"$+{performance['gain_loss']:,.2f}" if performance['gain_loss'] >= 0 else f"$-{abs(performance['gain_loss']):,.2f}"
                percentage_str = f"+{performance['percentage']:.2f}%" if performance['percentage'] >= 0 else f"{performance['percentage']:.2f}%"
                
                report_lines.extend([
                    f"{display_name}",
                    f"- Current Balance: ${info['balance']:,.2f}",
                    f"- Gain/Loss: {gain_loss_str}",
                    f"- Performance: {percentage_str}",
                    f""
                ])
        
        return "\n".join(report_lines)
    
    def save_report(self, report_content: str) -> str:
        """Save report to text file in balance_reports directory"""
        eastern = pytz.timezone('US/Eastern')
        timestamp = dt.datetime.now(eastern)
        filename = f"{timestamp.strftime('%Y%m%d_%H%M%S')}_balance_report.txt"
        
        # Save in the same directory as this script (balance_reports)
        script_dir = os.path.dirname(__file__)
        filepath = os.path.join(script_dir, filename)
        
        with open(filepath, 'w') as f:
            f.write(report_content)
        
        return filename
    
    def run(self):
        """Execute the balance report generation"""
        print("🏦 Trading Account Balance Reporter")
        print("=" * 50)
        print()
        
        # Collect balances
        self.collect_balances()
        
        # Generate report
        print("📊 Generating balance report...")
        report_content = self.generate_report()
        
        # Save report
        filename = self.save_report(report_content)
        
        print(f"✅ Balance report saved: balance_reports/{filename}")
        print()
        
        # Show summary
        print("📈 SUMMARY:")
        for account_name, info in self.accounts.items():
            display_name = info['display_name']
            if info['balance'] is not None:
                performance = self.calculate_performance(info['balance'], info['starting_balance'])
                print(f"   {display_name}: ${info['balance']:,.2f} ({performance['percentage']:+.2f}%)")
            else:
                print(f"   {display_name}: ❌ {info.get('error', 'Failed')}")
        
        # Calculate and show combined
        successful_balances = [(info['balance'], info['starting_balance']) for info in self.accounts.values() if info['balance'] is not None]
        if len(successful_balances) > 0:
            total_current = sum(balance for balance, _ in successful_balances)
            total_starting = sum(starting for _, starting in successful_balances)
            combined_percentage = ((total_current - total_starting) / total_starting) * 100
            print(f"   COMBINED: ${total_current:,.2f} ({combined_percentage:+.2f}%)")
        
        return filename


def main():
    """Main entry point"""
    try:
        reporter = BalanceReporter()
        filename = reporter.run()
        print(f"\n🎉 Report complete! Open balance_reports/{filename} to view detailed results.")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Report generation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error generating report: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main() 