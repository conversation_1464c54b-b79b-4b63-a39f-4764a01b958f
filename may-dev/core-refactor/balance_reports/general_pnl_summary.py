#!/usr/bin/env python3
"""
Generate General PnL Summary from Multiple Trading Accounts
Shows wins/losses and their performance as % of money risked
"""

import datetime as dt
import sys
import os
from typing import Dict, List, Optional
from dataclasses import dataclass
from collections import defaultdict

# Add parent directory (core-refactor) to path for imports
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from ib_insync import IB, Fill
import logging

# Set up logging
logging.basicConfig(level=logging.WARNING)


@dataclass
class TradeResult:
    """Represents a completed trade result"""
    symbol: str
    open_date: dt.datetime
    close_date: dt.datetime
    quantity: float
    entry_price: float
    exit_price: float
    pnl: float
    commission: float
    net_pnl: float
    risk_amount: float
    pnl_percentage: float
    is_win: bool


class PnLAnalyzer:
    """Analyze PnL and trade performance from multiple trading accounts"""
    
    def __init__(self):
        self.accounts = {
            'PhaedrusTrader': {'port': 4013, 'trades': [], 'error': None},
            'CoupledLogic': {'port': 4015, 'trades': [], 'error': None}
        }
        self.all_trades: List[TradeResult] = []
    
    def connect_and_get_data(self, port: int, account_name: str) -> List[TradeResult]:
        """Connect to IB and retrieve trade data"""
        ib = IB()
        trades = []
        
        try:
            print(f"📡 Connecting to {account_name} on port {port}...")
            ib.connect('127.0.0.1', port, clientId=98)
            
            # Get fills (completed trades)
            fills = ib.fills()
            print(f"📊 Found {len(fills)} fills for {account_name}")
            
            # Process fills to create trade results
            trades = self.process_fills_to_trades(fills, account_name)
            
            return trades
            
        except Exception as e:
            print(f"❌ {account_name}: Connection failed - {str(e)}")
            return []
        finally:
            try:
                ib.disconnect()
            except:
                pass
    
    def process_fills_to_trades(self, fills: List[Fill], account_name: str) -> List[TradeResult]:
        """Process IB fills into trade results"""
        trades = []
        
        # Group fills by symbol
        symbol_fills = defaultdict(list)
        
        for fill in fills:
            symbol = fill.contract.symbol
            symbol_fills[symbol].append(fill)
        
        # Process each symbol's fills
        for symbol, symbol_fill_list in symbol_fills.items():
            # Sort by time
            symbol_fill_list.sort(key=lambda x: x.time)
            
            # Track running position and match opening/closing trades
            position = 0
            open_trades = []  # [(quantity, price, time, commission)]
            
            for fill in symbol_fill_list:
                fill_qty = fill.execution.shares
                fill_price = fill.execution.price
                fill_time = fill.time
                commission = fill.commissionReport.commission if fill.commissionReport else 0
                
                if position == 0:
                    # Opening a new position
                    position = fill_qty
                    open_trades.append((fill_qty, fill_price, fill_time, commission))
                elif (position > 0 and fill_qty < 0) or (position < 0 and fill_qty > 0):
                    # Closing position (opposite direction)
                    remaining_to_close = abs(fill_qty)
                    
                    while remaining_to_close > 0 and open_trades:
                        open_qty, open_price, open_time, open_commission = open_trades.pop(0)
                        close_qty = min(abs(open_qty), remaining_to_close)
                        
                        # Calculate PnL for this portion
                        if position > 0:  # Long position closing
                            pnl = (fill_price - open_price) * close_qty
                        else:  # Short position closing
                            pnl = (open_price - fill_price) * close_qty
                        
                        # Calculate risk amount (money at risk)
                        risk_amount = abs(open_price * close_qty)
                        pnl_percentage = (pnl / risk_amount * 100) if risk_amount > 0 else 0
                        
                        # Create trade result
                        trade_result = TradeResult(
                            symbol=symbol,
                            open_date=open_time,
                            close_date=fill_time,
                            quantity=close_qty,
                            entry_price=open_price,
                            exit_price=fill_price,
                            pnl=pnl,
                            commission=open_commission + (commission * close_qty / abs(fill_qty)),
                            net_pnl=pnl - (open_commission + (commission * close_qty / abs(fill_qty))),
                            risk_amount=risk_amount,
                            pnl_percentage=pnl_percentage,
                            is_win=pnl > 0
                        )
                        
                        trades.append(trade_result)
                        remaining_to_close -= close_qty
                    
                    # Update position
                    position += fill_qty
                else:
                    # Adding to existing position
                    position += fill_qty
                    open_trades.append((fill_qty, fill_price, fill_time, commission))
        
        print(f"✅ Processed {len(trades)} completed trades for {account_name}")
        return trades
    
    def collect_all_trades(self):
        """Collect trades from all accounts"""
        print("📊 Collecting trade data from all accounts...\n")
        
        for account_name, info in self.accounts.items():
            try:
                trades = self.connect_and_get_data(info['port'], account_name)
                info['trades'] = trades
                self.all_trades.extend(trades)
            except Exception as e:
                info['error'] = str(e)
                print(f"❌ {account_name}: {str(e)}")
        
        print(f"\n📈 Total completed trades collected: {len(self.all_trades)}")
    
    def analyze_performance(self) -> Dict:
        """Analyze overall performance metrics"""
        if not self.all_trades:
            return {}
        
        wins = [t for t in self.all_trades if t.is_win]
        losses = [t for t in self.all_trades if not t.is_win]
        
        # Basic stats
        total_trades = len(self.all_trades)
        win_count = len(wins)
        loss_count = len(losses)
        win_rate = (win_count / total_trades * 100) if total_trades > 0 else 0
        
        # PnL stats
        total_pnl = sum(t.net_pnl for t in self.all_trades)
        avg_win = sum(t.net_pnl for t in wins) / len(wins) if wins else 0
        avg_loss = sum(t.net_pnl for t in losses) / len(losses) if losses else 0
        
        # Percentage stats (% of risk)
        avg_win_pct = sum(t.pnl_percentage for t in wins) / len(wins) if wins else 0
        avg_loss_pct = sum(t.pnl_percentage for t in losses) / len(losses) if losses else 0
        
        # Risk metrics
        total_risk = sum(t.risk_amount for t in self.all_trades)
        return_on_risk = (total_pnl / total_risk * 100) if total_risk > 0 else 0
        
        # Best/worst trades
        biggest_win = max(wins, key=lambda x: x.net_pnl) if wins else None
        biggest_loss = min(losses, key=lambda x: x.net_pnl) if losses else None
        
        return {
            'total_trades': total_trades,
            'win_count': win_count,
            'loss_count': loss_count,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'avg_win_pct': avg_win_pct,
            'avg_loss_pct': avg_loss_pct,
            'total_risk': total_risk,
            'return_on_risk': return_on_risk,
            'biggest_win': biggest_win,
            'biggest_loss': biggest_loss
        }
    
    def generate_report(self) -> str:
        """Generate comprehensive PnL summary report"""
        timestamp = dt.datetime.now()
        report_date = timestamp.strftime("%Y%m%d_%H%M%S")
        
        analysis = self.analyze_performance()
        
        if not analysis:
            return "# No Trade Data Available\n\nNo completed trades found in any account."
        
        report_lines = [
            f"# General PnL Summary Report",
            f"",
            f"**Generated:** {timestamp.strftime('%B %d, %Y at %I:%M:%S %p')}",
            f"**Report ID:** {report_date}",
            f"**Analysis Period:** All available trade history",
            f"",
            f"## Executive Summary",
            f"",
            f"| Metric | Value |",
            f"|--------|-------|",
            f"| **Total Trades** | {analysis['total_trades']:,} |",
            f"| **Wins** | {analysis['win_count']:,} ({analysis['win_rate']:.1f}%) |",
            f"| **Losses** | {analysis['loss_count']:,} ({100 - analysis['win_rate']:.1f}%) |",
            f"| **Total P&L** | ${analysis['total_pnl']:,.2f} |",
            f"| **Return on Risk** | {analysis['return_on_risk']:+.2f}% |",
            f"",
            f"## Performance Analysis",
            f"",
            f"### Wins vs Losses (as % of Money Risked)",
            f"",
            f"| Trade Type | Count | Avg P&L | Avg % of Risk | Rate |",
            f"|------------|-------|---------|---------------|------|",
            f"| **🟢 Wins** | {analysis['win_count']:,} | ${analysis['avg_win']:,.2f} | **{analysis['avg_win_pct']:+.2f}%** | {analysis['win_rate']:.1f}% |",
            f"| **🔴 Losses** | {analysis['loss_count']:,} | ${analysis['avg_loss']:,.2f} | **{analysis['avg_loss_pct']:.2f}%** | {100 - analysis['win_rate']:.1f}% |",
            f"",
        ]
        
        # Add best/worst trades
        if analysis['biggest_win']:
            bw = analysis['biggest_win']
            report_lines.extend([
                f"### 🏆 Best Trade",
                f"- **Symbol:** {bw.symbol}",
                f"- **Date:** {bw.close_date.strftime('%Y-%m-%d')}",
                f"- **P&L:** ${bw.net_pnl:,.2f}",
                f"- **% of Risk:** {bw.pnl_percentage:+.2f}%",
                f"- **Risk Amount:** ${bw.risk_amount:,.2f}",
                f"",
            ])
        
        if analysis['biggest_loss']:
            bl = analysis['biggest_loss']
            report_lines.extend([
                f"### 💸 Worst Trade",
                f"- **Symbol:** {bl.symbol}",
                f"- **Date:** {bl.close_date.strftime('%Y-%m-%d')}",
                f"- **P&L:** ${bl.net_pnl:,.2f}",
                f"- **% of Risk:** {bl.pnl_percentage:.2f}%",
                f"- **Risk Amount:** ${bl.risk_amount:,.2f}",
                f"",
            ])
        
        # Account breakdown
        report_lines.extend([
            f"## Account Performance",
            f""
        ])
        
        for account_name, info in self.accounts.items():
            if info['trades']:
                account_trades = info['trades']
                account_wins = [t for t in account_trades if t.is_win]
                account_losses = [t for t in account_trades if not t.is_win]
                
                account_pnl = sum(t.net_pnl for t in account_trades)
                account_win_rate = len(account_wins) / len(account_trades) * 100
                account_avg_win_pct = sum(t.pnl_percentage for t in account_wins) / len(account_wins) if account_wins else 0
                account_avg_loss_pct = sum(t.pnl_percentage for t in account_losses) / len(account_losses) if account_losses else 0
                
                report_lines.extend([
                    f"### {account_name}",
                    f"- **Total Trades:** {len(account_trades)} | Win Rate: {account_win_rate:.1f}%",
                    f"- **Total P&L:** ${account_pnl:,.2f}",
                    f"- **Avg Win % of Risk:** {account_avg_win_pct:+.2f}%" if account_wins else "- **Avg Win % of Risk:** N/A",
                    f"- **Avg Loss % of Risk:** {account_avg_loss_pct:.2f}%" if account_losses else "- **Avg Loss % of Risk:** N/A",
                    f""
                ])
            else:
                error_msg = info.get('error', 'No trades found')
                report_lines.extend([
                    f"### {account_name}",
                    f"- **Status:** ❌ {error_msg}",
                    f""
                ])
        
        # Recent trades
        if self.all_trades:
            recent_trades = sorted(self.all_trades, key=lambda x: x.close_date, reverse=True)[:15]
            
            report_lines.extend([
                f"## Recent Trades",
                f"",
                f"| Date | Symbol | P&L | % of Risk | Result |",
                f"|------|--------|-----|-----------|--------|"
            ])
            
            for trade in recent_trades:
                result = "🟢 WIN" if trade.is_win else "🔴 LOSS"
                report_lines.append(
                    f"| {trade.close_date.strftime('%m/%d/%Y')} | {trade.symbol} | "
                    f"${trade.net_pnl:+,.2f} | {trade.pnl_percentage:+.2f}% | {result} |"
                )
        
        # Footer
        report_lines.extend([
            f"",
            f"---",
            f"",
            f"*Generated by General PnL Summary Analyzer*  ",
            f"*Percentages show P&L as a percentage of capital at risk per trade*"
        ])
        
        return "\n".join(report_lines)
    
    def save_report(self, report_content: str) -> str:
        """Save report to markdown file"""
        timestamp = dt.datetime.now()
        filename = f"{timestamp.strftime('%Y%m%d_%H%M%S')}_pnl_summary.md"
        
        script_dir = os.path.dirname(__file__)
        filepath = os.path.join(script_dir, filename)
        
        with open(filepath, 'w') as f:
            f.write(report_content)
        
        return filename
    
    def run(self):
        """Execute the PnL analysis"""
        print("📊 General PnL Summary Analyzer")
        print("=" * 50)
        print()
        
        # Collect trade data
        self.collect_all_trades()
        
        if not self.all_trades:
            print("❌ No trade data found. Make sure accounts have completed trades.")
            return None
        
        # Generate report
        print("\n📋 Generating PnL summary report...")
        report_content = self.generate_report()
        
        # Save report
        filename = self.save_report(report_content)
        print(f"✅ PnL summary saved: balance_reports/{filename}")
        
        # Show quick summary
        analysis = self.analyze_performance()
        print(f"\n📈 QUICK SUMMARY:")
        print(f"   Total Trades: {analysis['total_trades']}")
        print(f"   Win Rate: {analysis['win_rate']:.1f}%")
        print(f"   Avg Win % of Risk: {analysis['avg_win_pct']:+.2f}%")
        print(f"   Avg Loss % of Risk: {analysis['avg_loss_pct']:.2f}%")
        print(f"   Total P&L: ${analysis['total_pnl']:+,.2f}")
        
        return filename


def main():
    """Main entry point"""
    try:
        analyzer = PnLAnalyzer()
        filename = analyzer.run()
        
        if filename:
            print(f"\n🎉 Analysis complete! Open balance_reports/{filename} to view detailed results.")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Analysis cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error during analysis: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main() 