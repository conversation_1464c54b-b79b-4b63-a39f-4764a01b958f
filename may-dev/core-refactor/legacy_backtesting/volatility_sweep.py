#!/usr/bin/env python3
"""
Volatility Parametric Sweep for SPX Backtesting
Runs backtest with volatility from 10% to 90% in 5% increments
"""

import os
import sys
import subprocess
import logging
from datetime import datetime
import json

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_volatility_sweep(start_date='2025-01-01', end_date='2025-06-18', 
                        vol_min=0.20, vol_max=0.50, vol_step=0.10):
    """
    Run volatility sweep from vol_min to vol_max in vol_step increments
    
    Args:
        start_date: Start date for backtest
        end_date: End date for backtest  
        vol_min: Minimum volatility (default 0.10 = 10%)
        vol_max: Maximum volatility (default 0.90 = 90%)
        vol_step: Volatility step size (default 0.05 = 5%)
    """
    
    # Create output directory
    output_dir = "varied_volatility"
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate volatility levels
    vol_levels = []
    vol = vol_min
    while vol <= vol_max + 0.001:  # Add small epsilon for floating point comparison
        vol_levels.append(round(vol, 3))
        vol += vol_step
    
    logger.info(f"Running volatility sweep: {vol_min:.0%} to {vol_max:.0%} in {vol_step:.0%} steps")
    logger.info(f"Total runs: {len(vol_levels)}")
    logger.info(f"Output directory: {output_dir}")
    
    # Track results
    sweep_results = []
    successful_runs = 0
    failed_runs = 0
    
    for i, volatility in enumerate(vol_levels):
        logger.info(f"Running {i+1}/{len(vol_levels)}: Volatility = {volatility:.1%}")
        
        try:
            # Build command
            cmd = [
                'python3', 'historical_backtester.py',
                '--start-date', start_date,
                '--end-date', end_date,
                '--volatility', str(volatility),
                '--output-dir', output_dir
            ]
            
            # Run the backtest (we're already in backtesting directory)
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info(f"✓ Volatility {volatility:.1%} completed successfully")
                successful_runs += 1
                
                # Try to extract summary from output
                output_lines = result.stdout.split('\n')
                summary_data = extract_summary_from_output(output_lines, volatility)
                sweep_results.append(summary_data)
                
            else:
                logger.error(f"✗ Volatility {volatility:.1%} failed:")
                logger.error(f"  Return code: {result.returncode}")
                logger.error(f"  STDERR: {result.stderr}")
                failed_runs += 1
                
        except subprocess.TimeoutExpired:
            logger.error(f"✗ Volatility {volatility:.1%} timed out (>5 minutes)")
            failed_runs += 1
        except Exception as e:
            logger.error(f"✗ Volatility {volatility:.1%} error: {e}")
            failed_runs += 1
    
    logger.info(f"Volatility sweep completed:")
    logger.info(f"  Successful runs: {successful_runs}")
    logger.info(f"  Failed runs: {failed_runs}")
    
    # Save sweep summary
    if sweep_results:
        save_sweep_summary(sweep_results, output_dir)
        create_sweep_analysis(sweep_results, output_dir)
    
    return sweep_results

def extract_summary_from_output(output_lines, volatility):
    """Extract summary statistics from backtest output"""
    
    summary = {
        'volatility': volatility,
        'total_trades': 0,
        'winning_trades': 0,
        'win_rate_pct': 0.0,
        'total_pnl': 0.0,
        'average_pnl': 0.0,
        'avg_win_amount': 0.0,
        'avg_loss_amount': 0.0,
        'avg_win_pct': 0.0,
        'avg_loss_pct': 0.0,
        'decision_counts': {},
        'decision_win_rates': {}
    }
    
    try:
        for line in output_lines:
            line = line.strip()
            if 'Total Trades:' in line:
                summary['total_trades'] = int(line.split(':')[1].strip())
            elif 'Winning Trades:' in line:
                summary['winning_trades'] = int(line.split(':')[1].strip())
            elif 'Win Rate:' in line:
                rate_str = line.split(':')[1].strip().replace('%', '')
                summary['win_rate_pct'] = float(rate_str)
            elif 'Total P&L:' in line:
                pnl_str = line.split(':')[1].strip()
                summary['total_pnl'] = float(pnl_str)
            elif 'Average P&L:' in line:
                avg_str = line.split(':')[1].strip()
                summary['average_pnl'] = float(avg_str)
            elif 'Average Win:' in line:
                # Parse "Average Win: $XXX (YY.Y%)"
                win_part = line.split(':')[1].strip()
                amount_part = win_part.split('(')[0].strip().replace('$', '')
                pct_part = win_part.split('(')[1].replace(')', '').replace('%', '')
                summary['avg_win_amount'] = float(amount_part)
                summary['avg_win_pct'] = float(pct_part)
            elif 'Average Loss:' in line:
                # Parse "Average Loss: $XXX (YY.Y%)"
                loss_part = line.split(':')[1].strip()
                amount_part = loss_part.split('(')[0].strip().replace('$', '')
                pct_part = loss_part.split('(')[1].replace(')', '').replace('%', '')
                summary['avg_loss_amount'] = float(amount_part)
                summary['avg_loss_pct'] = float(pct_part)
    
    except Exception as e:
        logger.warning(f"Error parsing output for volatility {volatility:.1%}: {e}")
    
    return summary

def save_sweep_summary(sweep_results, output_dir):
    """Save comprehensive sweep summary to JSON"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{output_dir}/volatility_sweep_summary_{timestamp}.json"
    
    sweep_summary = {
        'timestamp': timestamp,
        'total_runs': len(sweep_results),
        'volatility_range': {
            'min': min(r['volatility'] for r in sweep_results),
            'max': max(r['volatility'] for r in sweep_results),
            'step': 0.05
        },
        'results': sweep_results
    }
    
    with open(filename, 'w') as f:
        json.dump(sweep_summary, f, indent=2)
    
    logger.info(f"Sweep summary saved to: {filename}")

def create_sweep_analysis(sweep_results, output_dir):
    """Create analysis report of volatility sweep"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{output_dir}/volatility_analysis_{timestamp}.txt"
    
    with open(filename, 'w') as f:
        f.write("VOLATILITY SWEEP ANALYSIS REPORT\n")
        f.write("="*60 + "\n\n")
        
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total Runs: {len(sweep_results)}\n\n")
        
        # Summary table
        f.write("VOLATILITY vs PERFORMANCE:\n")
        f.write("-" * 80 + "\n")
        f.write("Vol   | Trades | Win Rate | Total P&L | Avg P&L | Avg Win% | Avg Loss%\n")
        f.write("-" * 80 + "\n")
        
        for result in sweep_results:
            vol = result['volatility']
            trades = result['total_trades']
            win_rate = result['win_rate_pct']
            total_pnl = result['total_pnl']
            avg_pnl = result['average_pnl']
            avg_win_pct = result.get('avg_win_pct', 0)
            avg_loss_pct = result.get('avg_loss_pct', 0)
            
            f.write(f"{vol:4.0%} | {trades:6d} | {win_rate:7.1f}% | {total_pnl:8.0f} | {avg_pnl:6.0f} | {avg_win_pct:7.1f}% | {avg_loss_pct:8.1f}%\n")
        
        f.write("\n")
        
        # Find optimal volatility levels
        if sweep_results:
            best_win_rate = max(sweep_results, key=lambda x: x['win_rate_pct'])
            best_total_pnl = max(sweep_results, key=lambda x: x['total_pnl'])
            best_avg_pnl = max(sweep_results, key=lambda x: x['average_pnl'])
            most_trades = max(sweep_results, key=lambda x: x['total_trades'])
            
            f.write("OPTIMAL VOLATILITY LEVELS:\n")
            f.write("-" * 40 + "\n")
            f.write(f"Best Win Rate:     {best_win_rate['volatility']:.0%} ({best_win_rate['win_rate_pct']:.1f}%)\n")
            f.write(f"Best Total P&L:    {best_total_pnl['volatility']:.0%} (${best_total_pnl['total_pnl']:.0f})\n")
            f.write(f"Best Average P&L:  {best_avg_pnl['volatility']:.0%} (${best_avg_pnl['average_pnl']:.0f})\n")
            f.write(f"Most Trades:       {most_trades['volatility']:.0%} ({most_trades['total_trades']} trades)\n")
    
    logger.info(f"Analysis report saved to: {filename}")

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Run volatility parametric sweep')
    parser.add_argument('--start-date', default='2025-01-01', help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', default='2025-06-18', help='End date (YYYY-MM-DD)')
    parser.add_argument('--vol-min', type=float, default=0.10, help='Minimum volatility (default: 0.10)')
    parser.add_argument('--vol-max', type=float, default=0.90, help='Maximum volatility (default: 0.90)')
    parser.add_argument('--vol-step', type=float, default=0.05, help='Volatility step (default: 0.05)')
    
    args = parser.parse_args()
    
    logger.info("Starting volatility parametric sweep...")
    
    results = run_volatility_sweep(
        start_date=args.start_date,
        end_date=args.end_date,
        vol_min=args.vol_min,
        vol_max=args.vol_max,
        vol_step=args.vol_step
    )
    
    if results:
        logger.info("Volatility sweep completed successfully!")
        logger.info("Check backtesting/varied_volatility/ for detailed results")
    else:
        logger.error("Volatility sweep failed - no results generated")

if __name__ == "__main__":
    main() 