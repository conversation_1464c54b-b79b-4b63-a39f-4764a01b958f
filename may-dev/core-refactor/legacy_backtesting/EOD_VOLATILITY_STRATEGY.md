# EOD Volatility Trading Strategy Specification

**Version**: V1.0  
**Date**: 6-30-2025  
**Strategy Type**: Long SPX vol, Short NDX vol  

## Overview
End-of-day volatility strategy that executes dual trades around market close, taking opposite volatility positions on SPX (long) and NDX (short) based on VIX levels and intraday bias.

## Strategy Details

### Timing
- **Execution Time**: Around **15:58:59** (closing of 15:58:30 30-second bar)
- **Data Point**: Use closing prices from the 15:58:30 30-second bar

### Overall Strategy
**Dual Trade Structure**: Long SPX vol + Short NDX vol

#### Trade A: Short NDX Iron Butterfly (10 wide)
**Strike Selection Logic:**

1. **Low VIX Scenario** (VIX high < 17):
   - Target strike = **closest strike to NDX@15:58:59**
   - No bias adjustment

2. **High VIX Scenario** (VIX high ≥ 17):
   - Find closest strike to NDX@15:58:59
   - Apply bias adjustment: move **one strike ($10)** toward today's bull/bear bias direction
   - **Bull bias**: move target strike UP $10
   - **Bear bias**: move target strike DOWN $10

3. **Distance Limit Check**:
   - If target strike is **>$14 away** from NDX@15:58:59
   - Move target strike **$10 closer** to NDX@15:58:59

**Pricing Assumptions:**
- **Credit Target**: Better than **$7.35 credit**
- **Risk**: **$2.65 per contract** (10-wide IBF)
- **Note**: Needs validation with historical NDX IBF pricing at 15:58:59

#### Trade B: Long SPX Iron Butterfly (5 wide)
**Trigger Condition**: Only when **VIX high > 17**

**Strike Selection**:
- Target strike = **closest strike to SPX@15:58:59**

**Pricing Assumptions**:
- **Debit Target**: **$2.35 debit**

### Position Sizing
- **Risk Per Trade**: 10% to 15% of AUM (Assets Under Management)
- **Contract Calculation**: Risk Amount ÷ Risk Per Contract = Number of Contracts
- **Daily Execution**: Dual trade executed every trading day

### VIX Logic Summary
- **Low VIX (< 17)**: Bad for long vol, good for short vol
  - Execute NDX short IBF only (no SPX long IBF)
- **High VIX (≥ 17)**: Good for long vol, still good for short vol
  - Execute both NDX short IBF (with bias) + SPX long IBF

## Data Requirements

### Historical Data Sources
**AVAILABLE DATA:**
- **ThetaData API**: New API endpoints for historical options data
- **Database**: ALGO_DATA database on .170 server
  - **SPX Table**: Historical SPX price data
  - **NDX Table**: Historical NDX price data
- **Options Chains**: 
  - **NDXP**: Historical NDX options chains
  - **SPXW**: Historical SPX options chains

### Required Data for Backtesting
1. **Underlying Price Data**:
   - SPX and NDX historical data (30-second or better resolution)
   - Settlement prices corrected by Yahoo closing prices
   - Focus on 15:58:30 bar closing prices

2. **Options Pricing Data**:
   - IBF pricing at 15:59:00 for validation
   - Historical NDXP and SPXW option chains
   - Strike price availability and pricing models

3. **VIX Data**:
   - Intraday VIX high for each trading day
   - VIX levels at execution time (15:58:59)

## Implementation Requirements

### Core Functions Needed
```python
def get_vix_daily_high(trading_date: str) -> float:
    """Get VIX intraday high for the trading date"""

def get_eod_prices(trading_date: str) -> Dict[str, float]:
    """Get SPX and NDX prices at 15:58:59"""
    # Return {"spx": price, "ndx": price}

def determine_daily_bias(trading_date: str) -> str:
    """Determine bull/bear bias for the day"""
    # Return "bull" or "bear"

def calculate_ndx_target_strike(ndx_price: float, vix_high: float, 
                               daily_bias: str) -> float:
    """Calculate NDX IBF target strike based on VIX and bias"""

def validate_strike_distance(target_strike: float, current_price: float, 
                           max_distance: float = 14.0) -> float:
    """Ensure target strike is within distance limit"""

def calculate_position_size(aum: float, risk_per_contract: float, 
                          risk_percentage: float = 0.125) -> int:
    """Calculate number of contracts based on risk management"""
```

### Database Schema
```sql
CREATE TABLE eod_volatility_trades (
    id INT PRIMARY KEY AUTO_INCREMENT,
    trading_date DATE NOT NULL,
    execution_time TIME DEFAULT '15:58:59',
    
    -- Market Data
    spx_price DECIMAL(10,2) NOT NULL,
    ndx_price DECIMAL(10,2) NOT NULL,
    vix_daily_high DECIMAL(6,2) NOT NULL,
    daily_bias ENUM('bull', 'bear') NOT NULL,
    
    -- NDX Short IBF (always executed)
    ndx_target_strike DECIMAL(10,2) NOT NULL,
    ndx_final_strike DECIMAL(10,2) NOT NULL,
    ndx_ibf_credit DECIMAL(6,2),
    ndx_contracts INT NOT NULL,
    ndx_risk_amount DECIMAL(12,2) NOT NULL,
    
    -- SPX Long IBF (only when VIX > 17)
    spx_executed BOOLEAN DEFAULT FALSE,
    spx_target_strike DECIMAL(10,2),
    spx_final_strike DECIMAL(10,2),
    spx_ibf_debit DECIMAL(6,2),
    spx_contracts INT,
    spx_risk_amount DECIMAL(12,2),
    
    -- Risk Management
    aum DECIMAL(15,2) NOT NULL,
    total_risk_amount DECIMAL(12,2) NOT NULL,
    risk_percentage DECIMAL(5,3) NOT NULL,
    
    -- Trade Outcome (filled later)
    ndx_pnl DECIMAL(12,2),
    spx_pnl DECIMAL(12,2),
    total_pnl DECIMAL(12,2),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Strike Selection Logic Implementation
```python
def execute_eod_volatility_strategy(trading_date: str, aum: float):
    """Main execution function for EOD volatility strategy"""
    
    # 1. Get market data at 15:58:59
    prices = get_eod_prices(trading_date)
    vix_high = get_vix_daily_high(trading_date)
    daily_bias = determine_daily_bias(trading_date)
    
    # 2. Calculate NDX short IBF target strike
    ndx_target = calculate_ndx_target_strike(
        ndx_price=prices['ndx'],
        vix_high=vix_high,
        daily_bias=daily_bias
    )
    
    # 3. Validate distance limit
    ndx_final_strike = validate_strike_distance(ndx_target, prices['ndx'])
    
    # 4. Calculate position sizes
    ndx_contracts = calculate_position_size(aum, 2.65, 0.125)  # 12.5% risk
    
    # 5. Execute SPX long IBF if VIX > 17
    spx_executed = vix_high > 17
    if spx_executed:
        spx_target_strike = round(prices['spx'] / 5) * 5  # Closest strike
        spx_contracts = calculate_position_size(aum, 2.35, 0.125)
    
    # 6. Log trade to database
    log_eod_volatility_trade(...)
```

## Validation Requirements

### Pricing Model Validation
1. **NDX IBF Pricing**: Collect historical 10-wide IBF prices at 15:58:59
2. **SPX IBF Pricing**: Validate 5-wide IBF debit assumptions
3. **Credit/Debit Targets**: Confirm $7.35 credit and $2.35 debit assumptions

### Backtesting Validation
1. **Strike Availability**: Ensure target strikes exist in historical options chains
2. **Liquidity Check**: Validate that IBF trades were executable historically
3. **Settlement Accuracy**: Verify Yahoo price corrections for settlement

## Risk Management

### Position Sizing
- **Maximum Risk**: 15% of AUM per trading day (both trades combined)
- **Typical Risk**: 10-12.5% of AUM per trading day
- **Per Trade Risk**: ~5-7.5% of AUM per individual trade

### Risk Limits
- **NDX IBF**: $2.65 risk per contract (10-wide)
- **SPX IBF**: $2.35 risk per contract (5-wide)
- **Distance Limit**: Target strikes must be within $14 of current price

## Testing Strategy

### Unit Tests
- VIX level categorization (< 17 vs ≥ 17)
- Strike selection with bias adjustments
- Distance limit validation
- Position sizing calculations

### Integration Tests
- End-to-end strategy execution
- Database logging and retrieval
- Historical data integration
- Options pricing model integration

### Backtesting Framework
- Historical execution simulation
- P&L calculation and tracking
- Risk management validation
- Strategy performance metrics

## Success Criteria
- Accurate strike selection based on VIX and bias rules
- Proper position sizing within risk limits
- Reliable historical data integration
- Validated pricing models for both IBF types
- Complete audit trail in database
- Backtesting framework producing actionable insights 