#!/usr/bin/env python3
"""
Database Output Module for Backtesting Results
Handles saving backtest runs and trade results to SQL Server
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import pyodbc
from dataclasses import asdict
from pathlib import Path

# Try to import dotenv, fall back gracefully if not available
try:
    import dotenv
    # Load .env file from the backtesting directory
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        dotenv.load_dotenv(env_path)
        print(f"Loaded environment variables from {env_path}")
except ImportError:
    print("python-dotenv not available, using system environment variables")

logger = logging.getLogger(__name__)


class BacktestDatabaseOutput:
    """Handles saving backtesting results to SQL Server database"""
    
    def __init__(self, connection_string: str = None):
        """
        Initialize database output handler
        
        Args:
            connection_string: Optional SQL Server connection string. If None, builds from environment variables.
        """
        self.connection_string = connection_string or self._build_connection_string()
    
    def _build_connection_string(self) -> str:
        """Build connection string from environment variables (matching main system)"""
        # Use same environment variable names as main trading system
        server = os.getenv('DB_IP') or '*************'
        database = os.getenv('DB_NAME') or 'IBDB_DEV'
        username = os.getenv('DB_USER')
        password = os.getenv('DB_PASSWORD')
        
        # Always use SQL Server Authentication if we have credentials
        if username and password:
            conn_str = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={server};"
                f"DATABASE={database};"
                f"UID={username};"
                f"PWD={password};"
                f"TrustServerCertificate=yes;"
            )
            logger.info(f"Using SQL Server Authentication for server: {server}, database: {database}, user: {username}")
        else:
            # Only fall back to Windows Authentication if no credentials provided
            conn_str = f"DRIVER={{ODBC Driver 18 for SQL Server}};SERVER={server};DATABASE={database};Trusted_Connection=yes;TrustServerCertificate=yes;"
            logger.info(f"Using Windows Authentication for server: {server}, database: {database}")
        
        return conn_str
    
    def save_backtest_results(self, backtester, results: List, run_name: Optional[str] = None) -> str:
        """
        Save complete backtest results to database
        
        Args:
            backtester: HistoricalBacktester instance with parameters
            results: List of BacktestResult objects
            run_name: Optional user-friendly name for this run
            
        Returns:
            run_id (GUID) of the created backtest run
        """
        if not results:
            logger.warning("No results to save to database")
            return None
        
        try:
            with pyodbc.connect(self.connection_string) as conn:
                cursor = conn.cursor()
                
                # Generate run_id
                cursor.execute("SELECT NEWID()")
                run_id = cursor.fetchone()[0]
                
                # Insert backtest run record
                self._insert_backtest_run(cursor, run_id, backtester, results, run_name)
                
                # Insert individual trade results
                self._insert_trade_results(cursor, run_id, results)
                
                conn.commit()
                logger.info(f"Saved backtest results to database with run_id: {run_id}")
                return run_id
                
        except Exception as e:
            logger.error(f"Error saving backtest results to database: {str(e)}")
            raise
    
    def _insert_backtest_run(self, cursor, run_id: str, backtester, results: List, run_name: Optional[str]):
        """Insert backtest run parameters and summary statistics"""
        
        # Generate summary statistics
        summary = backtester.generate_summary_stats()
        
        # Map pricing modes to database-allowed values (fixed, blackscholes, simple)
        if backtester.simple_mode:
            pricing_mode = "fixed"
        elif backtester.pricing_mode == "basic":
            pricing_mode = "simple"  # Map basic linear model to 'simple' in database
        elif backtester.pricing_mode == "advanced":
            pricing_mode = "blackscholes"
        else:
            pricing_mode = "blackscholes"  # Default fallback
        
        # Add conversion strategy info to run_name instead of pricing_mode
        if backtester.alternate_strategy and run_name:
            run_name += " (Alt Strategy)"
        elif backtester.alternate_strategy and not run_name:
            run_name = "Alternate Strategy Run"
        elif backtester.butterfly_conversion_only and run_name:
            run_name += " (BF Only)"
        elif backtester.butterfly_conversion_only and not run_name:
            run_name = "Butterfly Conversion Only Run"
        elif backtester.butterfly_narrow_conversions and run_name:
            run_name += " (BF Narrow)"
        elif backtester.butterfly_narrow_conversions and not run_name:
            run_name = "Butterfly-Narrow Conversions Run"
        
        # Prepare fixed mode parameters (only used when pricing_mode = 'fixed')
        fixed_spread_cost = backtester.simple_spread_cost if backtester.simple_mode else None
        fixed_butterfly_credit = backtester.simple_butterfly_credit if backtester.simple_mode else None
        fixed_itm_threshold = backtester.simple_itm_threshold if backtester.simple_mode else None
        
        # Convert decision counts and win rates to JSON
        decision_counts_json = json.dumps(summary.get('decision_counts', {}))
        
        # Enhanced decision win rates with profit/loss statistics
        enhanced_decision_win_rates = {}
        decision_win_rates = summary.get('decision_win_rates', {})
        decision_pnl_stats = summary.get('decision_profit_loss_stats', {})
        
        for decision in decision_win_rates.keys():
            enhanced_decision_win_rates[decision] = {
                'win_rate_pct': decision_win_rates[decision],
                'avg_win_amount': decision_pnl_stats.get(decision, {}).get('avg_win_amount', 0),
                'avg_loss_amount': decision_pnl_stats.get(decision, {}).get('avg_loss_amount', 0),
                'avg_win_pct': decision_pnl_stats.get(decision, {}).get('avg_win_pct', 0),
                'avg_loss_pct': decision_pnl_stats.get(decision, {}).get('avg_loss_pct', 0),
                'total_trades': decision_pnl_stats.get(decision, {}).get('total_trades', 0),
                'winning_trades': decision_pnl_stats.get(decision, {}).get('winning_trades', 0),
                'losing_trades': decision_pnl_stats.get(decision, {}).get('losing_trades', 0)
            }
        
        decision_win_rates_json = json.dumps(enhanced_decision_win_rates)
        
        sql = """
        INSERT INTO [IBDB_DEV].[dbo].[backtest_runs] (
            run_id, run_name, start_date, end_date, pricing_mode, volatility,
            fixed_spread_cost, fixed_butterfly_credit, fixed_itm_threshold,
            starting_balance, max_risk_per_trade_pct, skip_monday,
            total_trades, winning_trades, losing_trades, win_rate_pct,
            ending_balance, total_pnl, total_return_pct, max_drawdown, max_drawdown_pct,
            avg_pnl_per_trade, avg_win_amount, avg_loss_amount, avg_win_pct, avg_loss_pct,
            max_win_streak, max_lose_streak,
            avg_contracts_per_trade, avg_risk_amount_per_trade, avg_risk_pct_per_trade,
            decision_counts, decision_win_rates
        ) VALUES (
            ?, ?, ?, ?, ?, ?,
            ?, ?, ?,
            ?, ?, ?,
            ?, ?, ?, ?,
            ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?,
            ?, ?,
            ?, ?, ?,
            ?, ?
        )
        """
        
        # Cap values to prevent database overflow
        def cap_decimal_12_2(value):
            return max(-9999999999.99, min(9999999999.99, float(value) if value is not None else 0.0))
        
        def cap_decimal_8_2(value):
            return max(-999999.99, min(999999.99, float(value) if value is not None else 0.0))
            
        def cap_decimal_5_2(value):
            return max(-999.99, min(999.99, float(value) if value is not None else 0.0))
            
        def cap_decimal_7_2(value):
            return max(-99999.99, min(99999.99, float(value) if value is not None else 0.0))
            
        def cap_decimal_6_2(value):
            return max(-9999.99, min(9999.99, float(value) if value is not None else 0.0))
        
        cursor.execute(sql, (
            run_id,
            run_name,
            backtester.start_date,
            backtester.end_date,
            pricing_mode,
            backtester.volatility,
            fixed_spread_cost,
            fixed_butterfly_credit,
            fixed_itm_threshold,
            cap_decimal_12_2(backtester.starting_balance),
            backtester.max_risk_per_trade_pc,
            bool(backtester.skip_monday),  # Convert to Python bool
            summary.get('total_trades', 0),
            summary.get('winning_trades', 0),
            summary.get('losing_trades', 0),
            cap_decimal_5_2(summary.get('win_rate_pct', 0.0)),
            cap_decimal_12_2(summary.get('ending_balance', 0.0)),
            cap_decimal_12_2(summary.get('total_pnl', 0.0)),
            cap_decimal_7_2(summary.get('total_return_pct', 0.0)),
            cap_decimal_12_2(summary.get('max_drawdown', 0.0)),
            cap_decimal_5_2(summary.get('max_drawdown_pct', 0.0)),
            cap_decimal_8_2(summary.get('average_pnl', 0.0)),
            cap_decimal_8_2(summary.get('avg_win_amount', 0.0)),
            cap_decimal_8_2(summary.get('avg_loss_amount', 0.0)),
            cap_decimal_5_2(summary.get('avg_win_pct', 0.0)),
            cap_decimal_5_2(summary.get('avg_loss_pct', 0.0)),
            summary.get('max_win_streak', 0),
            summary.get('max_lose_streak', 0),
            cap_decimal_6_2(summary.get('avg_contracts_per_trade', 0.0)),
            cap_decimal_8_2(summary.get('avg_risk_amount_per_trade', 0.0)),
            cap_decimal_5_2(summary.get('avg_risk_pct_per_trade', 0.0)),
            decision_counts_json,
            decision_win_rates_json
        ))
    
    def _insert_trade_results(self, cursor, run_id: str, results: List):
        """Insert individual trade results"""
        
        sql = """
        INSERT INTO [IBDB_DEV].[dbo].[backtest_trade_results] (
            run_id, trading_date, trade_sequence, decision, reasoning, bias,
            execution_price, closing_price, target_price,
            original_strikes, final_strikes, spread_price, strikes_moved,
            current_balance, max_risk_amount, risk_per_contract, num_contracts, actual_risk_amount,
            trade_successful, profit_loss, new_balance,
            span1, span2, span3, close_pct_bar1, close_pct_bar2, close_pct_bar3,
            bar1_ohlc, bar2_ohlc, bar3_ohlc,
            option_types, actions, processing_time_seconds, narrow_fill_ohlc, narrow_fill_consolidated_ohlc
        ) VALUES (
            ?, ?, ?, ?, ?, ?,
            ?, ?, ?,
            ?, ?, ?, ?,
            ?, ?, ?, ?, ?,
            ?, ?, ?,
            ?, ?, ?, ?, ?, ?,
            ?, ?, ?,
            ?, ?, ?, ?, ?
        )
        """
        
        # Create a mapping to handle dual trades and extra butterfly trades with unique sequence numbers
        trade_sequence_map = {}
        sequence_counter = 1
        
        # First pass: Assign sequence numbers for main trades
        for result in results:
            trading_date = result.trading_date
            if trading_date not in trade_sequence_map:
                trade_sequence_map[trading_date] = {}
            
            # For dual trades, assign different sequence numbers
            if result.is_dual_trade:
                if result.dual_trade_type == "primary":
                    trade_sequence_map[trading_date]["primary"] = sequence_counter
                    sequence_counter += 1
                elif result.dual_trade_type == "secondary":
                    trade_sequence_map[trading_date]["secondary"] = sequence_counter
                    sequence_counter += 1
            else:
                trade_sequence_map[trading_date]["single"] = sequence_counter
                sequence_counter += 1
            
            # Reserve sequence number for extra butterfly trade if it exists
            if (hasattr(result, 'extra_butterfly_filled') and 
                result.extra_butterfly_filled and 
                hasattr(result, 'extra_butterfly_result') and 
                result.extra_butterfly_result):
                trade_sequence_map[trading_date]["extra_butterfly"] = sequence_counter
                sequence_counter += 1
        
        # Second pass: Insert main trades
        for i, result in enumerate(results):
            self._insert_single_trade_result(cursor, sql, run_id, result, trade_sequence_map, is_extra_butterfly=False)
        
        # Third pass: Insert extra butterfly trades as separate rows
        for result in results:
            if (hasattr(result, 'extra_butterfly_filled') and 
                result.extra_butterfly_filled and 
                hasattr(result, 'extra_butterfly_result') and 
                result.extra_butterfly_result):
                self._insert_extra_butterfly_trade(cursor, sql, run_id, result, trade_sequence_map)
    
    def _insert_single_trade_result(self, cursor, sql: str, run_id: str, result, trade_sequence_map: dict, is_extra_butterfly: bool = False):
        """Insert a single trade result (main trade or extra butterfly)"""
        # Convert arrays to JSON strings
        if is_extra_butterfly:
            # For extra butterfly trades, use the butterfly strikes
            original_strikes_json = json.dumps(result.extra_butterfly_result.strikes)
            final_strikes_json = json.dumps(result.extra_butterfly_result.strikes)
            option_types_json = json.dumps(["put", "put", "call", "call"])  # Iron butterfly structure
            actions_json = json.dumps(["sell", "buy", "buy", "sell"])  # Iron butterfly actions
        else:
            # For main trades, use regular strikes
            original_strikes_json = json.dumps(result.original_strikes)
            final_strikes_json = json.dumps(result.final_strikes)
            option_types_json = json.dumps(result.option_types)
            actions_json = json.dumps(result.actions)
        
        # Convert OHLC tuples to JSON strings
        bar1_ohlc_json = json.dumps(list(result.bars[0]))
        bar2_ohlc_json = json.dumps(list(result.bars[1]))
        bar3_ohlc_json = json.dumps(list(result.bars[2]))
        
        # Convert narrow_fill_ohlc to JSON string (detailed bars)
        narrow_fill_ohlc_json = json.dumps([list(bar) for bar in result.narrow_fill_ohlc]) if result.narrow_fill_ohlc else None
        
        # Convert narrow_fill_consolidated_ohlc to JSON string (single consolidated bar)
        narrow_fill_consolidated_ohlc_json = json.dumps(list(result.narrow_fill_consolidated_ohlc)) if result.narrow_fill_consolidated_ohlc != (0.0, 0.0, 0.0, 0.0) else None
        
        # Get the correct trade sequence for this result
        trading_date = result.trading_date
        if is_extra_butterfly:
            trade_sequence = trade_sequence_map[trading_date]["extra_butterfly"]
        elif result.is_dual_trade:
            trade_sequence = trade_sequence_map[trading_date][result.dual_trade_type]
        else:
            trade_sequence = trade_sequence_map[trading_date]["single"]
        
        # Cap values to prevent database overflow
        # DECIMAL(8,2) max is ±999,999.99, DECIMAL(12,2) max is ±9,999,999,999.99
        def cap_decimal_8_2(value):
            return max(-999999.99, min(999999.99, float(value) if value is not None else 0.0))
        
        def cap_decimal_12_2(value):
            return max(-9999999999.99, min(9999999999.99, float(value) if value is not None else 0.0))
            
        def cap_decimal_6_2(value):
            return max(-9999.99, min(9999.99, float(value) if value is not None else 0.0))
        
        # Set values based on trade type
        if is_extra_butterfly:
            decision = "extra_iron_butterfly"
            reasoning = f"Extra butterfly trade at 15:56:30 - {result.extra_butterfly_result.case}: {result.extra_butterfly_result.reasoning}"
            bias = 0  # Iron butterflies are neutral
            spread_price = -3.00  # Fixed credit for extra butterfly
            trade_successful = result.extra_butterfly_trade_successful
            profit_loss = result.extra_butterfly_profit_loss
            target_price = result.extra_butterfly_result.new_target
            num_contracts = result.extra_butterfly_num_contracts  # Use calculated contracts
            actual_risk_amount = result.extra_butterfly_actual_risk  # Use calculated risk
        else:
            decision = result.decision
            reasoning = result.reasoning
            bias = result.bias
            spread_price = result.spread_price
            trade_successful = result.trade_successful
            profit_loss = result.profit_loss
            target_price = result.target_price
            num_contracts = result.num_contracts
            actual_risk_amount = result.actual_risk_amount
        
        cursor.execute(sql, (
            run_id,
            result.trading_date,
            trade_sequence,  # Use the calculated sequence number
            decision,
            reasoning,
            bias,
            cap_decimal_8_2(result.execution_price),
            cap_decimal_8_2(result.closing_price),
            cap_decimal_8_2(target_price),
            original_strikes_json,
            final_strikes_json,
            cap_decimal_6_2(spread_price),
            bool(result.strikes_moved) if not is_extra_butterfly else False,  # Extra butterflies don't move strikes
            cap_decimal_12_2(result.current_balance),
            cap_decimal_8_2(result.max_risk_amount),
            cap_decimal_6_2(actual_risk_amount),  # Use actual risk for extra butterfly
            num_contracts,
            cap_decimal_8_2(actual_risk_amount),
            bool(trade_successful),
            cap_decimal_8_2(profit_loss),
            cap_decimal_12_2(result.new_balance),
            cap_decimal_6_2(result.spans['span1']),
            cap_decimal_6_2(result.spans['span2']),
            cap_decimal_6_2(result.spans['span3']),
            cap_decimal_6_2(result.close_percentages[0]),
            cap_decimal_6_2(result.close_percentages[1]),
            cap_decimal_6_2(result.close_percentages[2]),
            bar1_ohlc_json,
            bar2_ohlc_json,
            bar3_ohlc_json,
            option_types_json,
            actions_json,
            float(result.processing_time) if result.processing_time is not None else 0.0,
            narrow_fill_ohlc_json,
            narrow_fill_consolidated_ohlc_json
        ))
    
    def _insert_extra_butterfly_trade(self, cursor, sql: str, run_id: str, result, trade_sequence_map: dict):
        """Insert extra butterfly trade as a separate row"""
        self._insert_single_trade_result(cursor, sql, run_id, result, trade_sequence_map, is_extra_butterfly=True)
    
    def get_backtest_runs_summary(self) -> List[Dict[str, Any]]:
        """Get summary of all backtest runs"""
        try:
            with pyodbc.connect(self.connection_string) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM [IBDB_DEV].[dbo].[v_backtest_run_summary] ORDER BY total_return_pct DESC")
                
                columns = [column[0] for column in cursor.description]
                results = []
                
                for row in cursor.fetchall():
                    results.append(dict(zip(columns, row)))
                
                return results
                
        except Exception as e:
            logger.error(f"Error retrieving backtest runs summary: {str(e)}")
            raise
    
    def get_trade_results(self, run_id: str) -> List[Dict[str, Any]]:
        """Get detailed trade results for a specific run"""
        try:
            with pyodbc.connect(self.connection_string) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM [IBDB_DEV].[dbo].[v_backtest_results_with_params] 
                    WHERE run_id = ? 
                    ORDER BY trading_date
                """, (run_id,))
                
                columns = [column[0] for column in cursor.description]
                results = []
                
                for row in cursor.fetchall():
                    result = dict(zip(columns, row))
                    
                    # Parse JSON fields back to objects
                    try:
                        result['original_strikes'] = json.loads(result['original_strikes']) if result['original_strikes'] else []
                        result['final_strikes'] = json.loads(result['final_strikes']) if result['final_strikes'] else []
                        result['option_types'] = json.loads(result['option_types']) if result['option_types'] else []
                        result['actions'] = json.loads(result['actions']) if result['actions'] else []
                        result['bar1_ohlc'] = json.loads(result['bar1_ohlc']) if result['bar1_ohlc'] else []
                        result['bar2_ohlc'] = json.loads(result['bar2_ohlc']) if result['bar2_ohlc'] else []
                        result['bar3_ohlc'] = json.loads(result['bar3_ohlc']) if result['bar3_ohlc'] else []
                    except json.JSONDecodeError as e:
                        logger.warning(f"Error parsing JSON fields for trade {result.get('trading_date', 'unknown')}: {e}")
                    
                    results.append(result)
                
                return results
                
        except Exception as e:
            logger.error(f"Error retrieving trade results for run {run_id}: {str(e)}")
            raise 
    
    def save_trade_report_to_database(self, result, report_content: str, backtester) -> Optional[str]:
        """
        Save trade report to the backtest_trade_results table by updating the existing record
        
        Args:
            result: BacktestResult object
            report_content: Markdown content of the trade report
            backtester: HistoricalBacktester instance
            
        Returns:
            result_id if successful, None if failed
        """
        try:
            with pyodbc.connect(self.connection_string) as conn:
                cursor = conn.cursor()
                
                # Find the existing trade result record
                # For dual trades, also match by trade_sequence to ensure uniqueness
                if hasattr(result, 'is_dual_trade') and result.is_dual_trade:
                    sql_find = """
                    SELECT result_id FROM [IBDB_DEV].[dbo].[backtest_trade_results] 
                    WHERE trading_date = ? AND decision = ? AND execution_price = ? AND spread_price = ?
                    ORDER BY trade_sequence, created_at DESC
                    """
                else:
                    sql_find = """
                    SELECT result_id FROM [IBDB_DEV].[dbo].[backtest_trade_results] 
                    WHERE trading_date = ? AND decision = ? AND execution_price = ? AND spread_price = ?
                    ORDER BY created_at DESC
                    """
                
                cursor.execute(sql_find, (
                    result.trading_date,
                    result.decision,
                    result.execution_price,
                    result.spread_price
                ))
                
                row = cursor.fetchone()
                if not row:
                    logger.warning(f"Could not find existing trade result for {result.trading_date} {result.decision} (dual: {getattr(result, 'is_dual_trade', False)})")
                    return None
                
                result_id = row[0]
                
                # Update the existing record with trade report data
                sql_update = """
                UPDATE [IBDB_DEV].[dbo].[backtest_trade_results] 
                SET 
                    report_content = ?,
                    report_style = ?,
                    report_generated_at = ?
                WHERE result_id = ?
                """
                
                report_generated_at = datetime.now()
                
                cursor.execute(sql_update, (
                    report_content,
                    'live_style',  # Report style 
                    report_generated_at,
                    result_id
                ))
                
                conn.commit()
                logger.info(f"Updated trade result {result_id} with trade report")
                return str(result_id)
                
        except Exception as e:
            logger.error(f"Error saving trade report to database: {str(e)}")
            return None 
    
    def _get_strategy_mode_name(self, backtester) -> str:
        """Get the strategy mode name for database storage"""
        if backtester.alternate_strategy:
            return "Alternate"
        elif backtester.butterfly_conversion_only:
            return "Butterfly-Only"
        elif backtester.butterfly_narrow_conversions:
            return "Butterfly-Narrow"
        else:
            return "Standard" 