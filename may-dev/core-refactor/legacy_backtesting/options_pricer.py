#!/usr/bin/env python3
"""
Options Pricing Module for SPX Backtesting
Implements Black-Scholes pricing for vertical spreads and iron butterflies
"""

import math
from typing import Tuple, Dict, Any
from scipy.stats import norm

def round_up_to_nickel(price: float) -> float:
    """
    Round price up to nearest 5 cents (nickel)
    
    Args:
        price: Original price
        
    Returns:
        Price rounded up to nearest $0.05
    """
    return math.ceil(price * 20) / 20

def black_scholes_price(S: float, K: float, T: float, r: float, sigma: float, option_type: str) -> float:
    """
    Black-Scholes price for European options.
    
    Args:
        S: spot price
        K: strike price
        T: time to expiry (in years)
        r: risk-free rate
        sigma: volatility (annualized, e.g. 0.2 for 20%)
        option_type: 'call' or 'put'
        
    Returns:
        Option price
    """
    if T <= 0:
        # At expiry, option is worth intrinsic value
        if option_type == 'call':
            return max(0, S - K)
        else:
            return max(0, K - S)
            
    d1 = (math.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * math.sqrt(T))
    d2 = d1 - sigma * math.sqrt(T)
    
    if option_type == 'call':
        price = S * norm.cdf(d1) - K * math.exp(-r * T) * norm.cdf(d2)
    else:
        price = K * math.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
        
    return price

def price_vertical_spread(
    S: float,
    K1: float,
    K2: float,
    option_type: str,
    position1: str,
    position2: str,
    T: float = 8.5,  # 8.5 minutes to close
    sigma: float = 0.45,  # 45% volatility (realistic for end-of-day)
    r: float = 0.05  # 5% risk-free rate
) -> float:
    """
    Price a generic vertical spread (bull or bear, call or put).
    
    Args:
        S: spot price
        K1: strike price of first leg
        K2: strike price of second leg
        option_type: 'call' or 'put'
        position1: 'long' or 'short' for first leg (K1)
        position2: 'long' or 'short' for second leg (K2)
        T: time to expiry (in minutes)
        sigma: volatility (default 20%)
        r: risk-free rate (default 2%)
        
    Returns:
        Net price of the spread (positive = debit, negative = credit)
    """
    # Convert T from minutes to years
    T_years = T / (60 * 24 * 365.25)
    
    price1 = black_scholes_price(S, K1, T_years, r, sigma, option_type)
    price2 = black_scholes_price(S, K2, T_years, r, sigma, option_type)
    
    net = 0
    net += price1 if position1 == 'long' else -price1
    net += price2 if position2 == 'long' else -price2
    
    return net

def price_bull_call_spread(S: float, long_strike: float, short_strike: float, **kwargs) -> float:
    """Price a bull call spread: Buy lower call, Sell higher call (rounded up to nearest 5 cents)"""
    raw_price = price_vertical_spread(S, long_strike, short_strike, 'call', 'long', 'short', **kwargs)
    return round_up_to_nickel(raw_price)

def price_bear_put_spread(S: float, long_strike: float, short_strike: float, **kwargs) -> float:
    """Price a bear put spread: Buy higher put, Sell lower put (rounded up to nearest 5 cents)"""
    raw_price = price_vertical_spread(S, long_strike, short_strike, 'put', 'long', 'short', **kwargs)
    return round_up_to_nickel(raw_price)

def price_iron_butterfly(
    S: float,
    center_strike: float,
    wing_width: float = 5,
    T: float = 8.5,
    sigma: float = 0.45,
    r: float = 0.05
) -> float:
    """
    Price an iron butterfly: Short ATM call/put, Long wings
    
    Args:
        S: spot price
        center_strike: ATM strike (short call and put)
        wing_width: distance to wings (default 5)
        T: time to expiry (in minutes)
        sigma: volatility
        r: risk-free rate
        
    Returns:
        Net credit received (negative = credit, positive = debit)
    """
    T_years = T / (60 * 24 * 365.25)
    
    # Short positions (collect premium)
    short_call = -black_scholes_price(S, center_strike, T_years, r, sigma, 'call')
    short_put = -black_scholes_price(S, center_strike, T_years, r, sigma, 'put')
    
    # Long positions (pay premium)
    long_call = black_scholes_price(S, center_strike + wing_width, T_years, r, sigma, 'call')
    long_put = black_scholes_price(S, center_strike - wing_width, T_years, r, sigma, 'put')
    
    # Net credit (negative means we collect money)
    return short_call + short_put + long_call + long_put

def get_next_strike_out(current_strike: float, direction: str, increment: float = 5.0) -> float:
    """
    Get the next strike price further out of the money
    
    Args:
        current_strike: Current strike price
        direction: 'up' for calls (bull), 'down' for puts (bear)
        increment: Strike increment (default 5)
        
    Returns:
        Next strike price
    """
    if direction == 'up':
        return current_strike + increment
    else:
        return current_strike - increment

def should_move_strikes(spread_price: float, max_debit: float = 2.50) -> bool:
    """
    Check if spread price exceeds maximum acceptable debit
    
    Args:
        spread_price: Current spread price (positive = debit)
        max_debit: Maximum acceptable debit (default $2.50)
        
    Returns:
        True if strikes should be moved further OTM
    """
    return spread_price > max_debit

def find_optimal_strikes(
    S: float,
    strategy: str,
    center_strike: float,
    max_debit: float = 2.50,
    max_iterations: int = 5,
    **pricing_kwargs
) -> Tuple[float, float, float]:
    """
    Find optimal strikes that keep spread price under max_debit
    
    For bears: Start with strikes that wrap around current SPX price
    - First attempt: Buy strike above SPX, Sell strike below SPX  
    - If too expensive, move both strikes down by 5
    
    For bulls: Start with strikes that wrap around current SPX price
    - First attempt: Buy strike below SPX, Sell strike at/above SPX
    - If too expensive, move both strikes up by 5
    
    Args:
        S: spot price
        strategy: 'bull' or 'bear'
        center_strike: Initial center strike (used as reference)
        max_debit: Maximum acceptable debit
        max_iterations: Maximum strikes to try
        **pricing_kwargs: Additional pricing parameters
        
    Returns:
        (long_strike, short_strike, spread_price)
    """
    
    if strategy == 'bear':
        # Bear put spread: Buy higher strike put, Sell lower strike put
        # Start with strikes that wrap around current SPX price
        strike_below = int(S // 5) * 5  # Round down to nearest 5
        strike_above = strike_below + 5
        
        # First attempt: wrap around current price
        long_strike = strike_above   # Buy put above current SPX
        short_strike = strike_below  # Sell put below current SPX
        
        for i in range(max_iterations):
            spread_price = price_bear_put_spread(S, long_strike, short_strike, **pricing_kwargs)
            
            if not should_move_strikes(spread_price, max_debit):
                return long_strike, short_strike, spread_price
                
            # Move both strikes down by 5 (to cheaper puts)
            long_strike -= 5
            short_strike -= 5
    
    else:  # bull
        # Bull call spread: Buy lower strike call, Sell higher strike call  
        # Start with strikes that wrap around current SPX price
        strike_below = int(S // 5) * 5  # Round down to nearest 5
        strike_above = strike_below + 5
        
        # First attempt: wrap around current price
        long_strike = strike_below   # Buy call below current SPX
        short_strike = strike_above  # Sell call above current SPX
        
        for i in range(max_iterations):
            spread_price = price_bull_call_spread(S, long_strike, short_strike, **pricing_kwargs)
            
            if not should_move_strikes(spread_price, max_debit):
                return long_strike, short_strike, spread_price
                
            # Move both strikes up by 5 (to cheaper calls)
            long_strike += 5
            short_strike += 5
    
    # Return final attempt even if over max_debit
    return long_strike, short_strike, spread_price

if __name__ == "__main__":
    # Test the pricing functions across a range of SPX values
    print("SPX Price | Bull 6005/6010 | Bear 6010/6005 | IB 6005±5 | Bull Target | Bear Target")
    print("-" * 80)
    
    for price_increment in range(0, 51):  # 6005.0 to 6010.0 in 0.1 increments
        S = 6005.0 + (price_increment * 0.1)
        
        # Test spreads at current price
        bull_price = price_bull_call_spread(S, 6005, 6010)
        bear_price = price_bear_put_spread(S, 6010, 6005)
        ib_price = price_iron_butterfly(S, 6005)
        
        # Find optimal strikes and targets
        bull_long, bull_short, bull_opt_price = find_optimal_strikes(S, 'bull', 6005)
        bear_long, bear_short, bear_opt_price = find_optimal_strikes(S, 'bear', 6005)
        
        print(f"{S:8.1f} | {bull_price:11.2f} | {bear_price:11.2f} | {ib_price:8.2f} | " +
              f"{bull_short:4.0f} (${bull_opt_price:.2f}) | {bear_short:4.0f} (${bear_opt_price:.2f})")
    
    print("\n=== Key Observations ===")
    # Test a specific example
    S = 6007.5
    print(f"\nAt SPX = {S}:")
    
    # Bull spread analysis
    print("\nBull Spread Analysis:")
    bull_6005_6010 = price_bull_call_spread(S, 6005, 6010)
    bull_6010_6015 = price_bull_call_spread(S, 6010, 6015)
    print(f"  6005/6010: ${bull_6005_6010:.2f} {'(TRADE)' if bull_6005_6010 <= 2.40 else '(TOO EXPENSIVE)'}")
    print(f"  6010/6015: ${bull_6010_6015:.2f} {'(TRADE)' if bull_6010_6015 <= 2.40 else '(TOO EXPENSIVE)'}")
    
    # Bear spread analysis
    print("\nBear Spread Analysis:")
    bear_6010_6005 = price_bear_put_spread(S, 6010, 6005)
    bear_6005_6000 = price_bear_put_spread(S, 6005, 6000)
    print(f"  6010/6005: ${bear_6010_6005:.2f} {'(TRADE)' if bear_6010_6005 <= 2.40 else '(TOO EXPENSIVE)'}")
    print(f"  6005/6000: ${bear_6005_6000:.2f} {'(TRADE)' if bear_6005_6000 <= 2.40 else '(TOO EXPENSIVE)'}")
    
    # Iron butterfly analysis
    print("\nIron Butterfly Analysis:")
    ib_6005 = price_iron_butterfly(S, 6005)
    ib_6010 = price_iron_butterfly(S, 6010)
    print(f"  Center 6005: ${ib_6005:.2f} {'credit' if ib_6005 < 0 else 'debit'}")
    print(f"  Center 6010: ${ib_6010:.2f} {'credit' if ib_6010 < 0 else 'debit'}") 