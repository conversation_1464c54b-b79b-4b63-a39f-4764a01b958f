#!/usr/bin/env python3
"""
Test Script: Validate Standalone Decision Engine vs Actual System
"""

import sys
import os
from typing import Tuple, Dict, Any

# Add paths to import from the main system
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Import standalone decision engine
from decision_engine import analyze_normalized_bars, print_decision_result

# Import actual system components for comparison
from strategy_state import StrategyState, BarData, DecisionType
from spx_strategy import SPXTradingStrategy
from config import TradingConfig
from combo_orders import StrikeCalculator
import datetime as dt


class MockQuoteEngine:
    """Mock quote engine for testing"""
    def __init__(self):
        pass

class MockExecutionEngine:
    """Mock execution engine for testing"""
    def __init__(self):
        pass


def create_bar_data(ohlc: Tuple[float, float, float, float], name: str) -> BarData:
    """Create BarData from OHLC tuple"""
    return BarData(
        timestamp=dt.datetime.now(),
        open=ohlc[0],
        high=ohlc[1],
        low=ohlc[2],
        close=ohlc[3],
        name=name
    )


def test_actual_system(bar1_ohlc: Tuple[float, float, float, float],
                      bar2_ohlc: Tuple[float, float, float, float], 
                      bar3_ohlc: Tuple[float, float, float, float]) -> Tuple[Dict[str, Any], Dict[str, Tuple]]:
    """
    Run the same bar data through the actual SPX trading system
    
    Returns:
        Tuple of (actual_system_results, adjusted_bars)
    """
    
    # Create config and strategy  
    config = TradingConfig()
    mock_quote_engine = MockQuoteEngine()
    mock_execution_engine = MockExecutionEngine()
    
    strategy = SPXTradingStrategy(mock_quote_engine, mock_execution_engine)
    strategy.start_session()
    
    # Create bar data objects
    bar1 = create_bar_data(bar1_ohlc, "first")
    bar2 = create_bar_data(bar2_ohlc, "second") 
    bar3 = create_bar_data(bar3_ohlc, "third")
    
    # Process bars through actual system with proper previous_close to maintain realistic spans
    # Use the first bar's open as the previous close to minimize adjustments
    previous_close = bar1_ohlc[0]  # Use bar1 open as previous close
    
    strategy.process_bar_data(bar1, 1, previous_close)
    strategy._analyze_first_bar()
    
    strategy.process_bar_data(bar2, 2)
    strategy._analyze_second_bar()
    
    strategy.process_bar_data(bar3, 3)
    strategy._analyze_third_bar()
    
    # For iron butterfly decisions, manually call the bias direction setting
    if strategy.state.current_decision.value == "iron_butterfly":
        print(f"🔍 DEBUG: Calling _set_iron_butterfly_strike_direction_with_bias")
        strategy._set_iron_butterfly_strike_direction_with_bias(bar3_ohlc[3], strategy.state.span3)
    
    # Extract results from strategy state
    state = strategy.state
    decision = state.current_decision.value
    
    # Calculate center strike and target
    current_price = bar3_ohlc[3]
    
    if decision == "iron_butterfly":
        # Use the iron butterfly strike direction from analysis
        if (state.current_analysis and 
            hasattr(state.current_analysis, 'iron_butterfly_strike_direction')):
            direction = state.current_analysis.iron_butterfly_strike_direction
            print(f"🔍 DEBUG: Actual system iron butterfly direction = '{direction}'")
            if direction == "round_up":
                center_strike = StrikeCalculator.get_next_strike_up(current_price)
            elif direction == "round_down":
                center_strike = StrikeCalculator.get_next_strike_down(current_price)
            else:
                center_strike = StrikeCalculator.get_atm_strike(current_price)
        else:
            print(f"🔍 DEBUG: No iron butterfly direction set, using ATM")
            center_strike = StrikeCalculator.get_atm_strike(current_price)
        print(f"🔍 DEBUG: current_price={current_price}, center_strike={center_strike}")
        target_price = center_strike
    else:
        center_strike = StrikeCalculator.get_atm_strike(current_price)
        target_price = center_strike
    
    # Get bias
    bias = getattr(state, 'current_bias', 0)
    
    # Get spans
    spans = {
        'span1': state.span1.span_points if state.span1 else 0.0,
        'span2': state.span2.span_points if state.span2 else 0.0,
        'span3': state.span3.span_points if state.span3 else 0.0
    }
    
    # Get reasoning from decision history
    reasoning = "Actual system analysis"
    if state.decision_history:
        reasoning = state.decision_history[-1].reason
    
    # Extract adjusted bars from the strategy state
    adjusted_bars = {}
    if state.first_bar:
        adjusted_bars['bar1'] = (state.first_bar.open, state.first_bar.high, state.first_bar.low, state.first_bar.close)
    else:
        adjusted_bars['bar1'] = bar1_ohlc
        
    if state.second_bar:
        adjusted_bars['bar2'] = (state.second_bar.open, state.second_bar.high, state.second_bar.low, state.second_bar.close)
    else:
        adjusted_bars['bar2'] = bar2_ohlc
        
    if state.third_bar:
        adjusted_bars['bar3'] = (state.third_bar.open, state.third_bar.high, state.third_bar.low, state.third_bar.close)
    else:
        adjusted_bars['bar3'] = bar3_ohlc

    actual_results = {
        'decision': decision,
        'center_strike': center_strike,
        'target_price': target_price,
        'bias': bias,
        'spans': spans,
        'reasoning': reasoning
    }
    
    return actual_results, adjusted_bars


def compare_results(standalone_result, actual_result, test_name: str):
    """Compare standalone vs actual system results"""
    
    print(f"\n{'='*60}")
    print(f"TEST: {test_name}")
    print(f"{'='*60}")
    
    print(f"\n📊 STANDALONE DECISION ENGINE:")
    print_decision_result(standalone_result)
    
    print(f"\n🏭 ACTUAL SYSTEM RESULTS:")
    print(f"Decision: {actual_result['decision'].upper()}")
    print(f"Center Strike: ${actual_result['center_strike']:.0f}")
    print(f"Target Price: ${actual_result['target_price']:.0f}")
    print(f"Bias: {actual_result['bias']} ({'up' if actual_result['bias'] > 0 else 'down' if actual_result['bias'] < 0 else 'neutral'})")
    print(f"Spans: SPAN1={actual_result['spans']['span1']:.1f}, SPAN2={actual_result['spans']['span2']:.1f}, SPAN3={actual_result['spans']['span3']:.1f}")
    print(f"Reasoning: {actual_result['reasoning']}")
    
    print(f"\n🔍 COMPARISON:")
    
    # Compare key metrics
    decision_match = standalone_result.decision == actual_result['decision']
    target_match = abs(standalone_result.target_price - actual_result['target_price']) < 0.1
    bias_match = standalone_result.bias == actual_result['bias']
    
    span1_match = abs(standalone_result.spans['span1'] - actual_result['spans']['span1']) < 0.1
    span2_match = abs(standalone_result.spans['span2'] - actual_result['spans']['span2']) < 0.1  
    span3_match = abs(standalone_result.spans['span3'] - actual_result['spans']['span3']) < 0.1
    
    print(f"✅ Decision Match: {decision_match} ({standalone_result.decision} vs {actual_result['decision']})")
    print(f"✅ Target Price Match: {target_match} (${standalone_result.target_price:.0f} vs ${actual_result['target_price']:.0f})")
    print(f"✅ Bias Match: {bias_match} ({standalone_result.bias} vs {actual_result['bias']})")
    print(f"✅ SPAN1 Match: {span1_match} ({standalone_result.spans['span1']:.1f} vs {actual_result['spans']['span1']:.1f})")
    print(f"✅ SPAN2 Match: {span2_match} ({standalone_result.spans['span2']:.1f} vs {actual_result['spans']['span2']:.1f})")
    print(f"✅ SPAN3 Match: {span3_match} ({standalone_result.spans['span3']:.1f} vs {actual_result['spans']['span3']:.1f})")
    
    all_match = decision_match and target_match and bias_match and span1_match and span2_match and span3_match
    
    if all_match:
        print(f"\n🎉 PERFECT MATCH! Standalone engine matches actual system exactly.")
    else:
        print(f"\n⚠️  DIFFERENCES DETECTED - Review implementation.")
    
    return all_match


def run_test_cases():
    """Run comprehensive test cases"""
    
    print("🧪 TESTING STANDALONE DECISION ENGINE vs ACTUAL SYSTEM")
    print("="*70)
    
    test_cases = [
        # Test Case 1: Bull breakout
        {
            'name': 'Bull Breakout (High Close)',
            'bar1': (5800.0, 5805.0, 5795.0, 5803.0),  # Close at 80% of range
            'bar2': (5803.0, 5810.0, 5800.0, 5808.0),  # Breakout higher  
            'bar3': (5808.0, 5815.0, 5805.0, 5812.0),  # Continue higher
        },
        
        # Test Case 2: Bear breakout  
        {
            'name': 'Bear Breakout (Low Close)',
            'bar1': (5800.0, 5805.0, 5795.0, 5797.0),  # Close at 20% of range
            'bar2': (5797.0, 5800.0, 5790.0, 5792.0),  # Breakout lower
            'bar3': (5792.0, 5795.0, 5785.0, 5788.0),  # Continue lower
        },
        
        # Test Case 3: Iron butterfly (neutral)
        {
            'name': 'Iron Butterfly (Neutral)',
            'bar1': (5800.0, 5804.0, 5796.0, 5800.0),  # Close at 50% of range
            'bar2': (5800.0, 5803.0, 5798.0, 5801.0),  # No breakout
            'bar3': (5801.0, 5803.0, 5799.0, 5802.0),  # No breakout
        },
        
        # Test Case 4: Narrow conversion
        {
            'name': 'Narrow Bull (Small Range)',
            'bar1': (5800.0, 5802.0, 5799.0, 5801.5),  # Close high, small range
            'bar2': (5801.5, 5803.0, 5800.0, 5802.5),  # Continue higher
            'bar3': (5802.5, 5803.5, 5801.0, 5803.0),  # Small SPAN3 < 4.0
        },
        
        # Test Case 5: Iron butterfly with bias up
        {
            'name': 'Iron Butterfly (Bias Up)',
            'bar1': (5800.0, 5810.0, 5795.0, 5807.0),  # Close at 80% - should bias up
            'bar2': (5807.0, 5812.0, 5805.0, 5810.0),  # No breakout (not > 88%)
            'bar3': (5810.0, 5815.0, 5808.0, 5812.0),  # No breakout, stay iron butterfly
        }
    ]
    
    all_passed = True
    
    for test_case in test_cases:
        try:
            # Run actual system first to get adjusted bars
            actual_result, adjusted_bars = test_actual_system(
                test_case['bar1'],
                test_case['bar2'], 
                test_case['bar3']
            )
            
            # Now run standalone engine with the SAME adjusted bars
            standalone_result = analyze_normalized_bars(
                adjusted_bars['bar1'], 
                adjusted_bars['bar2'], 
                adjusted_bars['bar3']
            )
            
            # Compare results
            test_passed = compare_results(standalone_result, actual_result, test_case['name'])
            all_passed = all_passed and test_passed
            
        except Exception as e:
            print(f"\n❌ ERROR in test '{test_case['name']}': {str(e)}")
            all_passed = False
    
    print(f"\n{'='*70}")
    if all_passed:
        print("🎉 ALL TESTS PASSED! Standalone engine is correctly implemented.")
    else:
        print("⚠️  SOME TESTS FAILED - Review implementation differences.")
    print(f"{'='*70}")
    
    return all_passed


if __name__ == "__main__":
    success = run_test_cases()
    sys.exit(0 if success else 1) 