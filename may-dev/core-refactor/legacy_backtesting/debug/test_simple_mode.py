#!/usr/bin/env python3
"""
Test Simple Mode functionality
Demonstrates the simple mode with fixed costs and next strike logic
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# Add paths to import from the main system
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from historical_backtester import HistoricalBacktester


def test_simple_mode():
    """Test simple mode vs regular mode"""
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Calculate recent date range (last 10 days)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=10)
    
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')
    
    print("="*80)
    print("SIMPLE MODE vs ADVANCED MODE COMPARISON")
    print("="*80)
    print(f"Date Range: {start_date_str} to {end_date_str}")
    print()
    
    # Test Advanced Mode
    print("🔬 Running ADVANCED MODE (Black-Scholes pricing)...")
    print("-" * 60)
    
    backtester_advanced = HistoricalBacktester(
        start_date_str, 
        end_date_str, 
        volatility=0.45, 
        simple_mode=False
    )
    results_advanced = backtester_advanced.run_backtest()
    
    if results_advanced:
        backtester_advanced.print_summary()
    else:
        print("❌ No results for advanced mode")
    
    print("\n" + "="*80)
    
    # Test Simple Mode
    print("⚡ Running SIMPLE MODE (Fixed costs)...")
    print("-" * 60)
    
    backtester_simple = HistoricalBacktester(
        start_date_str, 
        end_date_str, 
        volatility=0.45, 
        simple_mode=True
    )
    results_simple = backtester_simple.run_backtest()
    
    if results_simple:
        backtester_simple.print_summary()
    else:
        print("❌ No results for simple mode")
    
    # Compare results
    if results_advanced and results_simple:
        print("\n" + "="*80)
        print("COMPARISON SUMMARY")
        print("="*80)
        
        # Get summary stats
        advanced_stats = backtester_advanced.generate_summary_stats()
        simple_stats = backtester_simple.generate_summary_stats()
        
        print(f"Advanced Mode - Total P&L: ${advanced_stats['total_pnl']:.1f}, Win Rate: {advanced_stats['win_rate_pct']:.1f}%")
        print(f"Simple Mode   - Total P&L: ${simple_stats['total_pnl']:.1f}, Win Rate: {simple_stats['win_rate_pct']:.1f}%")
        
        pnl_diff = simple_stats['total_pnl'] - advanced_stats['total_pnl']
        if pnl_diff > 0:
            print(f"📈 Simple mode performed ${pnl_diff:.1f} better")
        elif pnl_diff < 0:
            print(f"📉 Advanced mode performed ${abs(pnl_diff):.1f} better")
        else:
            print("📊 Both modes performed equally")
        
        print("\nSimple Mode Benefits:")
        print("✅ Fixed, predictable costs ($2.20 spreads, -$3.50 butterflies)")
        print("✅ No complex option pricing calculations")
        print("✅ Faster execution (no strike optimization)")
        print("✅ Automatic strike adjustment when >$2.50 ITM")
        
        print("\nAdvanced Mode Benefits:")
        print("🔬 Real-time option pricing based on Black-Scholes")
        print("🎯 Dynamic strike optimization")
        print("📊 More realistic profit/loss calculations")
        print("⚙️ Configurable volatility assumptions")
    
    print("\n" + "="*80)
    print("✅ Test completed!")


def main():
    """Main entry point"""
    print("SPX Simple Mode Testing")
    test_simple_mode()


if __name__ == "__main__":
    main() 