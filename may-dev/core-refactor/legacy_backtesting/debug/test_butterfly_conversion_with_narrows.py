#!/usr/bin/env python3
"""
Test script for butterfly-conversion-with-narrows mode
Tests the new --butterfly-conversion-with-narrows feature
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from historical_backtester import HistoricalBacktester
from decision_engine import DecisionR<PERSON>ult

def test_butterfly_conversion_with_narrows():
    """Test the butterfly-conversion-with-narrows mode"""
    print("Testing Butterfly Conversion with Narrows Mode")
    print("=" * 50)
    
    # Create backtester with butterfly_conversion_with_narrows enabled
    backtester = HistoricalBacktester(butterfly_conversion_with_narrows=True)
    
    # Test case 1: Iron butterfly with 1st bar close >= 66% AND span3 >= 4 → bull
    print("\n1. Iron butterfly with 1st bar close >= 66% AND span3 >= 4 → bull")
    original_result = DecisionResult(
        decision="iron_butterfly",
        strikes=[5590, 5595, 5595, 5600],
        option_types=['P', 'P', 'C', 'C'],
        actions=['BUY', 'SELL', 'SELL', 'BUY'],
        target_price=5595.0,
        reasoning="Iron butterfly decision based on market conditions",
        bias=0,
        spans={'span1': 3.0, 'span2': 4.2, 'span3': 5.1},  # span3 >= 4.0
        close_percentages=[70.0, 50.0, 45.0]  # 1st bar close >= 66%
    )
    
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Original: {original_result.decision}")
    print(f"   Converted: {converted_result.decision}")
    print(f"   Reasoning: {converted_result.reasoning}")
    print(f"   Expected: bull")
    assert converted_result.decision == "bull", f"Expected bull, got {converted_result.decision}"
    assert "BF-CONV-NARROW" in converted_result.reasoning
    assert "span3 5.1 >= 4" in converted_result.reasoning
    
    # Test case 2: Iron butterfly with 1st bar close >= 66% AND span3 < 4 → narrow_bull
    print("\n2. Iron butterfly with 1st bar close >= 66% AND span3 < 4 → narrow_bull")
    original_result = DecisionResult(
        decision="iron_butterfly",
        strikes=[5590, 5595, 5595, 5600],
        option_types=['P', 'P', 'C', 'C'],
        actions=['BUY', 'SELL', 'SELL', 'BUY'],
        target_price=5595.0,
        reasoning="Iron butterfly decision based on market conditions",
        bias=0,
        spans={'span1': 2.0, 'span2': 3.2, 'span3': 3.8},  # span3 < 4.0
        close_percentages=[75.0, 50.0, 45.0]  # 1st bar close >= 66%
    )
    
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Original: {original_result.decision}")
    print(f"   Converted: {converted_result.decision}")
    print(f"   Reasoning: {converted_result.reasoning}")
    print(f"   Expected: narrow_bull")
    assert converted_result.decision == "narrow_bull", f"Expected narrow_bull, got {converted_result.decision}"
    assert "BF-CONV-NARROW" in converted_result.reasoning
    assert "span3 3.8 < 4" in converted_result.reasoning
    
    # Test case 3: Iron butterfly with 1st bar close < 66% AND span3 >= 4 → bear
    print("\n3. Iron butterfly with 1st bar close < 66% AND span3 >= 4 → bear")
    original_result = DecisionResult(
        decision="iron_butterfly",
        strikes=[5590, 5595, 5595, 5600],
        option_types=['P', 'P', 'C', 'C'],
        actions=['BUY', 'SELL', 'SELL', 'BUY'],
        target_price=5595.0,
        reasoning="Iron butterfly decision based on market conditions",
        bias=0,
        spans={'span1': 3.0, 'span2': 4.2, 'span3': 5.1},  # span3 >= 4.0
        close_percentages=[45.0, 50.0, 55.0]  # 1st bar close < 66%
    )
    
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Original: {original_result.decision}")
    print(f"   Converted: {converted_result.decision}")
    print(f"   Reasoning: {converted_result.reasoning}")
    print(f"   Expected: bear")
    assert converted_result.decision == "bear", f"Expected bear, got {converted_result.decision}"
    assert "BF-CONV-NARROW" in converted_result.reasoning
    assert "span3 5.1 >= 4" in converted_result.reasoning
    
    # Test case 4: Iron butterfly with 1st bar close < 66% AND span3 < 4 → narrow_bear
    print("\n4. Iron butterfly with 1st bar close < 66% AND span3 < 4 → narrow_bear")
    original_result = DecisionResult(
        decision="iron_butterfly",
        strikes=[5590, 5595, 5595, 5600],
        option_types=['P', 'P', 'C', 'C'],
        actions=['BUY', 'SELL', 'SELL', 'BUY'],
        target_price=5595.0,
        reasoning="Iron butterfly decision based on market conditions",
        bias=0,
        spans={'span1': 2.0, 'span2': 3.2, 'span3': 3.8},  # span3 < 4.0
        close_percentages=[45.0, 50.0, 55.0]  # 1st bar close < 66%
    )
    
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Original: {original_result.decision}")
    print(f"   Converted: {converted_result.decision}")
    print(f"   Reasoning: {converted_result.reasoning}")
    print(f"   Expected: narrow_bear")
    assert converted_result.decision == "narrow_bear", f"Expected narrow_bear, got {converted_result.decision}"
    assert "BF-CONV-NARROW" in converted_result.reasoning
    assert "span3 3.8 < 4" in converted_result.reasoning
    
    # Test case 5: Test edge cases for thresholds
    print("\n5. Test exact threshold: 1st bar close = 66% AND span3 = 4.0")
    original_result = DecisionResult(
        decision="iron_butterfly",
        strikes=[5590, 5595, 5595, 5600],
        option_types=['P', 'P', 'C', 'C'],
        actions=['BUY', 'SELL', 'SELL', 'BUY'],
        target_price=5595.0,
        reasoning="Iron butterfly decision based on market conditions",
        bias=0,
        spans={'span1': 3.0, 'span2': 4.2, 'span3': 4.0},  # span3 = 4.0
        close_percentages=[66.0, 50.0, 55.0]  # 1st bar close = 66%
    )
    
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Original: {original_result.decision}")
    print(f"   Converted: {converted_result.decision}")
    print(f"   Reasoning: {converted_result.reasoning}")
    print(f"   Expected: bull (>= 66% and >= 4.0)")
    assert converted_result.decision == "bull", f"Expected bull, got {converted_result.decision}"
    assert "BF-CONV-NARROW" in converted_result.reasoning
    
    # Test case 6: Non-butterfly strategies should remain unchanged
    print("\n6. Bull strategy should remain unchanged")
    original_result = DecisionResult(
        decision="bull",
        strikes=[5590, 5595],
        option_types=['C', 'C'],
        actions=['BUY', 'SELL'],
        target_price=5595.0,
        reasoning="Bull decision based on market conditions",
        bias=0,
        spans={'span1': 2.0, 'span2': 3.2, 'span3': 3.8},
        close_percentages=[75.0, 50.0, 45.0]
    )
    
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Original: {original_result.decision}")
    print(f"   Converted: {converted_result.decision}")
    print(f"   Expected: bull (unchanged)")
    assert converted_result.decision == "bull", f"Expected bull, got {converted_result.decision}"
    assert converted_result.reasoning == original_result.reasoning  # Should be unchanged
    
    # Test case 7: Test with butterfly_conversion_with_narrows disabled
    print("\n7. Test with butterfly_conversion_with_narrows disabled")
    backtester_normal = HistoricalBacktester(butterfly_conversion_with_narrows=False)
    
    original_result = DecisionResult(
        decision="iron_butterfly",
        strikes=[5590, 5595, 5595, 5600],
        option_types=['P', 'P', 'C', 'C'],
        actions=['BUY', 'SELL', 'SELL', 'BUY'],
        target_price=5595.0,
        reasoning="Iron butterfly decision based on market conditions",
        bias=0,
        spans={'span1': 3.0, 'span2': 4.2, 'span3': 5.1},
        close_percentages=[70.0, 50.0, 45.0]
    )
    
    converted_result = backtester_normal.apply_alternate_strategy_conversion(original_result)
    print(f"   Original: {original_result.decision}")
    print(f"   Converted: {converted_result.decision}")
    print(f"   Butterfly-conversion-with-narrows enabled: {backtester_normal.butterfly_conversion_with_narrows}")
    print(f"   Expected: iron_butterfly (unchanged)")
    assert converted_result.decision == "iron_butterfly", f"Expected iron_butterfly, got {converted_result.decision}"
    
    print("\n" + "=" * 50)
    print("✅ All tests passed!")
    print("The butterfly-conversion-with-narrows mode is working correctly.")
    print("\nTest Summary:")
    print("- Iron butterfly → bull (1st bar >= 66%, span3 >= 4)")
    print("- Iron butterfly → narrow_bull (1st bar >= 66%, span3 < 4)")
    print("- Iron butterfly → bear (1st bar < 66%, span3 >= 4)")
    print("- Iron butterfly → narrow_bear (1st bar < 66%, span3 < 4)")
    print("- Non-butterfly strategies remain unchanged")
    print("- Mode can be disabled correctly")


if __name__ == "__main__":
    test_butterfly_conversion_with_narrows() 