#!/usr/bin/env python3
"""
Test script for butterfly-narrow conversions mode
Tests the new --butterfly-narrow-conversions feature
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from historical_backtester import HistoricalBacktester
from decision_engine import DecisionR<PERSON>ult

def test_butterfly_narrow_conversions():
    """Test the butterfly-narrow conversions mode"""
    print("Testing Butterfly-Narrow Conversions Mode")
    print("=" * 50)
    
    # Create backtester with butterfly-narrow conversions enabled
    backtester = HistoricalBacktester(butterfly_narrow_conversions=True)
    
    # Test case 1: Iron butterfly with span3 >= 4.0 and 1st bar close > 66%
    print("\n1. Iron butterfly with span3 >= 4.0 and 1st bar close > 66% → bull")
    original_result = DecisionResult(
        decision="iron_butterfly",
        strikes=[5590, 5595, 5595, 5600],
        option_types=['P', 'P', 'C', 'C'],
        actions=['BUY', 'SELL', 'SELL', 'BUY'],
        target_price=5595.0,
        reasoning="Iron butterfly decision based on market conditions",
        bias=0,
        spans={'span1': 3.0, 'span2': 4.2, 'span3': 5.1},  # span3 >= 4.0
        close_percentages=[75.0, 50.0, 45.0]  # 1st bar close > 66%
    )
    
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Original: {original_result.decision}")
    print(f"   Converted: {converted_result.decision}")
    print(f"   Reasoning: {converted_result.reasoning}")
    print(f"   Expected: bull")
    assert converted_result.decision == "bull", f"Expected bull, got {converted_result.decision}"
    assert "BF-NARROW" in converted_result.reasoning
    
    # Test case 2: Iron butterfly with span3 < 4.0 and 1st bar close > 66%
    print("\n2. Iron butterfly with span3 < 4.0 and 1st bar close > 66% → narrow_bull")
    original_result = DecisionResult(
        decision="iron_butterfly",
        strikes=[5590, 5595, 5595, 5600],
        option_types=['P', 'P', 'C', 'C'],
        actions=['BUY', 'SELL', 'SELL', 'BUY'],
        target_price=5595.0,
        reasoning="Iron butterfly decision based on market conditions",
        bias=0,
        spans={'span1': 2.0, 'span2': 3.2, 'span3': 3.8},  # span3 < 4.0
        close_percentages=[75.0, 50.0, 45.0]  # 1st bar close > 66%
    )
    
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Original: {original_result.decision}")
    print(f"   Converted: {converted_result.decision}")
    print(f"   Reasoning: {converted_result.reasoning}")
    print(f"   Expected: narrow_bull")
    assert converted_result.decision == "narrow_bull", f"Expected narrow_bull, got {converted_result.decision}"
    assert "BF-NARROW" in converted_result.reasoning
    assert "span3 3.8 < 4" in converted_result.reasoning
    
    # Test case 3: Iron butterfly with span3 < 4.0 and 1st bar close < 66%
    print("\n3. Iron butterfly with span3 < 4.0 and 1st bar close < 66% → narrow_bear")
    original_result = DecisionResult(
        decision="iron_butterfly",
        strikes=[5590, 5595, 5595, 5600],
        option_types=['P', 'P', 'C', 'C'],
        actions=['BUY', 'SELL', 'SELL', 'BUY'],
        target_price=5595.0,
        reasoning="Iron butterfly decision based on market conditions",
        bias=0,
        spans={'span1': 2.0, 'span2': 3.2, 'span3': 3.8},  # span3 < 4.0
        close_percentages=[45.0, 50.0, 55.0]  # 1st bar close < 66%
    )
    
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Original: {original_result.decision}")
    print(f"   Converted: {converted_result.decision}")
    print(f"   Reasoning: {converted_result.reasoning}")
    print(f"   Expected: narrow_bear")
    assert converted_result.decision == "narrow_bear", f"Expected narrow_bear, got {converted_result.decision}"
    assert "BF-NARROW" in converted_result.reasoning
    assert "span3 3.8 < 4" in converted_result.reasoning
    
    # Test case 4: Iron butterfly with span3 >= 4.0 and 1st bar close < 66%
    print("\n4. Iron butterfly with span3 >= 4.0 and 1st bar close < 66% → bear")
    original_result = DecisionResult(
        decision="iron_butterfly",
        strikes=[5590, 5595, 5595, 5600],
        option_types=['P', 'P', 'C', 'C'],
        actions=['BUY', 'SELL', 'SELL', 'BUY'],
        target_price=5595.0,
        reasoning="Iron butterfly decision based on market conditions",
        bias=0,
        spans={'span1': 3.0, 'span2': 4.2, 'span3': 5.1},  # span3 >= 4.0
        close_percentages=[45.0, 50.0, 55.0]  # 1st bar close < 66%
    )
    
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Original: {original_result.decision}")
    print(f"   Converted: {converted_result.decision}")
    print(f"   Reasoning: {converted_result.reasoning}")
    print(f"   Expected: bear")
    assert converted_result.decision == "bear", f"Expected bear, got {converted_result.decision}"
    assert "BF-NARROW" in converted_result.reasoning
    
    # Test case 5: Narrow bull should remain unchanged
    print("\n5. Narrow bull should remain unchanged")
    original_result = DecisionResult(
        decision="narrow_bull",
        strikes=[5590, 5595],
        option_types=['C', 'C'],
        actions=['BUY', 'SELL'],
        target_price=5595.0,
        reasoning="Narrow bull decision based on market conditions",
        bias=0,
        spans={'span1': 2.0, 'span2': 3.2, 'span3': 3.8},  # span3 < 4.0
        close_percentages=[75.0, 50.0, 45.0]
    )
    
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Original: {original_result.decision}")
    print(f"   Converted: {converted_result.decision}")
    print(f"   Expected: narrow_bull (unchanged)")
    assert converted_result.decision == "narrow_bull", f"Expected narrow_bull, got {converted_result.decision}"
    assert converted_result.reasoning == original_result.reasoning  # Should be unchanged
    
    # Test case 6: Narrow bear should remain unchanged
    print("\n6. Narrow bear should remain unchanged")
    original_result = DecisionResult(
        decision="narrow_bear",
        strikes=[5590, 5595],
        option_types=['P', 'P'],
        actions=['BUY', 'SELL'],
        target_price=5595.0,
        reasoning="Narrow bear decision based on market conditions",
        bias=0,
        spans={'span1': 2.0, 'span2': 3.2, 'span3': 3.8},  # span3 < 4.0
        close_percentages=[25.0, 50.0, 45.0]
    )
    
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Original: {original_result.decision}")
    print(f"   Converted: {converted_result.decision}")
    print(f"   Expected: narrow_bear (unchanged)")
    assert converted_result.decision == "narrow_bear", f"Expected narrow_bear, got {converted_result.decision}"
    assert converted_result.reasoning == original_result.reasoning  # Should be unchanged
    
    # Test case 7: Test with butterfly-narrow conversions disabled
    print("\n7. Test with butterfly-narrow conversions disabled")
    backtester_normal = HistoricalBacktester(butterfly_narrow_conversions=False)
    
    converted_result = backtester_normal.apply_alternate_strategy_conversion(original_result)
    print(f"   Original: {original_result.decision}")
    print(f"   Converted: {converted_result.decision}")
    print(f"   Butterfly-narrow conversions enabled: {backtester_normal.butterfly_narrow_conversions}")
    print(f"   Expected: narrow_bear (unchanged)")
    assert converted_result.decision == "narrow_bear", f"Expected narrow_bear, got {converted_result.decision}"
    
    print("\n" + "=" * 50)
    print("✅ All butterfly-narrow conversions tests passed!")
    print("\nKey features verified:")
    print("• Iron butterfly → bull/bear based on 1st bar close > 66%")
    print("• Iron butterfly → narrow_bull/narrow_bear if span3 < 4 points")
    print("• Narrow strategies remain unchanged")
    print("• Conversion only happens when butterfly_narrow_conversions=True")
    print("• Proper reasoning strings with conversion details")
    print("• File naming includes '_bf_narrow' suffix")
    print("• Database integration with proper run names")

if __name__ == "__main__":
    test_butterfly_narrow_conversions() 