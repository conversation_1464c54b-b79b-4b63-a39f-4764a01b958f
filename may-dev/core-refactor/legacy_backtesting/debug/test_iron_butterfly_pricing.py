#!/usr/bin/env python3
"""
Test iron butterfly pricing logic across different SPX prices
Structure: 5995/6000/6000/6005 (put wing/atm put/atm call/call wing)
"""

from options_pricer import black_scholes_price, price_iron_butterfly

def price_iron_butterfly_manual(S, put_wing, atm_strike, call_wing, T=8.5, sigma=0.20, r=0.05):
    """
    Manually price iron butterfly: Short ATM call/put, Long wings
    Structure: Short 6000 Put + Short 6000 Call + Long 5995 Put + Long 6005 Call
    
    Args:
        S: spot price
        put_wing: long put strike (5995)
        atm_strike: short put/call strike (6000) 
        call_wing: long call strike (6005)
        T: time to expiry (in minutes)
        sigma: volatility
        r: risk-free rate
        
    Returns:
        Net credit received (negative = credit, positive = debit)
    """
    T_years = T / (60 * 24 * 365.25)
    
    # Short positions (collect premium)
    short_put_6000 = -black_scholes_price(S, atm_strike, T_years, r, sigma, 'put')
    short_call_6000 = -black_scholes_price(S, atm_strike, T_years, r, sigma, 'call')
    
    # Long positions (pay premium)  
    long_put_5995 = black_scholes_price(S, put_wing, T_years, r, sigma, 'put')
    long_call_6005 = black_scholes_price(S, call_wing, T_years, r, sigma, 'call')
    
    # Net credit (negative means we collect money)
    net_credit = short_put_6000 + short_call_6000 + long_put_5995 + long_call_6005
    
    return {
        'net_credit': net_credit,
        'short_put_6000': short_put_6000,
        'short_call_6000': short_call_6000, 
        'long_put_5995': long_put_5995,
        'long_call_6005': long_call_6005
    }

def test_iron_butterfly_pricing():
    """Test iron butterfly pricing across SPX range 5995-6005"""
    
    print("Iron Butterfly Pricing Test: 5995/6000/6000/6005")
    print("=" * 70)
    print("SPX    | Net Credit | Short Put | Short Call | Long Put | Long Call")
    print("Price  | (Collect)  |   6000    |    6000    |   5995   |   6005")
    print("-" * 70)
    
    for price_increment in range(101):  # 5995.0 to 6005.0 in 0.1 increments
        S = 5995.0 + (price_increment * 0.1)
        
        # Test our current function
        current_method = price_iron_butterfly(S, center_strike=6000, wing_width=5)
        
        # Test manual calculation
        manual_result = price_iron_butterfly_manual(S, put_wing=5995, atm_strike=6000, call_wing=6005)
        
        print(f"{S:6.1f} | {manual_result['net_credit']:9.3f} | "
              f"{manual_result['short_put_6000']:8.3f} | {manual_result['short_call_6000']:9.3f} | "
              f"{manual_result['long_put_5995']:7.3f} | {manual_result['long_call_6005']:8.3f}")
        
        # Verify our current method matches manual calculation
        if abs(current_method - manual_result['net_credit']) > 0.001:
            print(f"  *** MISMATCH: Current method={current_method:.3f}, Manual={manual_result['net_credit']:.3f}")
    
    print("\n" + "=" * 70)
    print("Key Observations:")
    print("1. Net Credit should be negative (we collect money)")
    print("2. Maximum credit should be around ATM (6000)")
    print("3. Credit should decrease as we move away from ATM")
    
    # Test specific scenarios
    print("\nSpecific Test Cases:")
    test_cases = [5998.0, 6000.0, 6002.0]
    
    for test_price in test_cases:
        result = price_iron_butterfly_manual(test_price, 5995, 6000, 6005)
        print(f"\nSPX @ {test_price}:")
        print(f"  Net Credit: ${abs(result['net_credit']):.2f}")
        print(f"  Individual legs:")
        print(f"    Short 6000 Put:  ${abs(result['short_put_6000']):.3f} (collect)")
        print(f"    Short 6000 Call: ${abs(result['short_call_6000']):.3f} (collect)")  
        print(f"    Long 5995 Put:   ${result['long_put_5995']:.3f} (pay)")
        print(f"    Long 6005 Call:  ${result['long_call_6005']:.3f} (pay)")
        
        # Check if above minimum credit threshold
        credit_amount = abs(result['net_credit'])
        print(f"  Above $3.20 minimum? {'YES' if credit_amount >= 3.20 else 'NO'}")
        print(f"  Above $3.40 minimum? {'YES' if credit_amount >= 3.40 else 'NO'}")

if __name__ == "__main__":
    test_iron_butterfly_pricing() 