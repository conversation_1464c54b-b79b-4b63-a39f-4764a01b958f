#!/usr/bin/env python3
"""
Database Setup Checker for Backtesting Framework
Helps verify and setup the database tables and views
"""

import os
import sys
import pyodbc
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

try:
    import dotenv
    # Load .env file from the backtesting directory
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        dotenv.load_dotenv(env_path)
        print(f"Loaded environment variables from {env_path}")
except ImportError:
    print("python-dotenv not available, using system environment variables")


def build_connection_string():
    """Build connection string from environment variables"""
    server = os.getenv('DB_IP') or '*************'
    database = os.getenv('DB_NAME') or 'IBDB_DEV'
    username = os.getenv('DB_USER')
    password = os.getenv('DB_PASSWORD')
    
    if username and password:
        conn_str = (
            f"DRIVER={{ODBC Driver 18 for SQL Server}};"
            f"SERVER={server};"
            f"DATABASE={database};"
            f"UID={username};"
            f"PWD={password};"
            f"TrustServerCertificate=yes;"
        )
        print(f"Using SQL Server Authentication for server: {server}, database: {database}, user: {username}")
    else:
        conn_str = f"DRIVER={{ODBC Driver 18 for SQL Server}};SERVER={server};DATABASE={database};Trusted_Connection=yes;TrustServerCertificate=yes;"
        print(f"Using Windows Authentication for server: {server}, database: {database}")
    
    return conn_str


def check_tables_exist(cursor):
    """Check if backtest tables exist"""
    tables_to_check = ['backtest_runs', 'backtest_trade_results']
    existing_tables = []
    
    for table in tables_to_check:
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'dbo' 
            AND TABLE_NAME = ?
        """, (table,))
        
        if cursor.fetchone()[0] > 0:
            existing_tables.append(table)
            print(f"✓ Table [IBDB_DEV].[dbo].[{table}] exists")
        else:
            print(f"✗ Table [IBDB_DEV].[dbo].[{table}] does not exist")
    
    return existing_tables


def check_views_exist(cursor):
    """Check if backtest views exist"""
    views_to_check = ['v_backtest_run_summary', 'v_backtest_results_with_params']
    existing_views = []
    
    for view in views_to_check:
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.VIEWS 
            WHERE TABLE_SCHEMA = 'dbo' 
            AND TABLE_NAME = ?
        """, (view,))
        
        if cursor.fetchone()[0] > 0:
            existing_views.append(view)
            print(f"✓ View [IBDB_DEV].[dbo].[{view}] exists")
        else:
            print(f"✗ View [IBDB_DEV].[dbo].[{view}] does not exist")
    
    return existing_views


def check_data_exists(cursor):
    """Check if there's any data in the tables"""
    try:
        cursor.execute("SELECT COUNT(*) FROM [IBDB_DEV].[dbo].[backtest_runs]")
        runs_count = cursor.fetchone()[0]
        print(f"Found {runs_count} backtest runs in database")
        
        cursor.execute("SELECT COUNT(*) FROM [IBDB_DEV].[dbo].[backtest_trade_results]")
        trades_count = cursor.fetchone()[0]
        print(f"Found {trades_count} trade results in database")
        
        if runs_count > 0:
            cursor.execute("SELECT TOP 5 run_name, total_return_pct, win_rate_pct FROM [IBDB_DEV].[dbo].[backtest_runs] ORDER BY created_at DESC")
            recent_runs = cursor.fetchall()
            print("\nRecent backtest runs:")
            for run in recent_runs:
                print(f"  - {run[0] or 'Unnamed'}: {run[1]:.1f}% return, {run[2]:.1f}% win rate")
                
    except Exception as e:
        print(f"Error checking data: {e}")


def create_schema():
    """Create the database schema"""
    schema_file = Path(__file__).parent / 'backtest_schema.sql'
    if not schema_file.exists():
        print(f"Error: Schema file not found at {schema_file}")
        return False
    
    try:
        with open(schema_file, 'r') as f:
            schema_sql = f.read()
        
        connection_string = build_connection_string()
        with pyodbc.connect(connection_string) as conn:
            cursor = conn.cursor()
            
            # Split by GO statements and execute each batch
            batches = schema_sql.split('GO')
            for i, batch in enumerate(batches):
                batch = batch.strip()
                if batch:
                    try:
                        cursor.execute(batch)
                        print(f"✓ Executed batch {i+1}")
                    except Exception as e:
                        print(f"✗ Error in batch {i+1}: {e}")
            
            conn.commit()
            print("Schema creation completed!")
            return True
            
    except Exception as e:
        print(f"Error creating schema: {e}")
        return False


def main():
    print("=== Backtest Database Setup Checker ===\n")
    
    try:
        connection_string = build_connection_string()
        print(f"\nConnecting to database...")
        
        with pyodbc.connect(connection_string) as conn:
            cursor = conn.cursor()
            print("✓ Database connection successful\n")
            
            # Check current database
            cursor.execute("SELECT DB_NAME()")
            current_db = cursor.fetchone()[0]
            print(f"Current database: {current_db}")
            
            # Check tables
            print("\n--- Checking Tables ---")
            existing_tables = check_tables_exist(cursor)
            
            # Check views
            print("\n--- Checking Views ---")
            existing_views = check_views_exist(cursor)
            
            # Check data
            if len(existing_tables) == 2:
                print("\n--- Checking Data ---")
                check_data_exists(cursor)
            
            # Offer to create schema if missing
            if len(existing_tables) < 2 or len(existing_views) < 2:
                print(f"\n--- Schema Setup Needed ---")
                response = input("Would you like to create the missing tables/views? (y/n): ")
                if response.lower() == 'y':
                    if create_schema():
                        print("\n✓ Schema setup completed successfully!")
                        print("You can now run backtests with --save-to-database")
                    else:
                        print("\n✗ Schema setup failed. Check the errors above.")
                else:
                    print("Schema setup skipped.")
            else:
                print(f"\n✓ Database setup is complete!")
                print("You can run backtests with --save-to-database")
    
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        print("\nTroubleshooting:")
        print("1. Check your environment variables: DB_IP, DB_NAME, DB_USER, DB_PASSWORD")
        print("2. Ensure ODBC Driver 18 for SQL Server is installed")
        print("3. Verify network connectivity to the database server")


if __name__ == "__main__":
    main() 