#!/usr/bin/env python3
"""
Debug script to analyze actual backtest results and identify the 99.2% loss issue
"""

import sys
import os
from typing import Dict, List, Tuple

# Add the current directory to path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_actual_backtest_results():
    """Analyze actual backtest results to find the 99.2% issue"""
    print("🔍 Analyzing Actual Backtest Results for 99.2% Issue")
    print("=" * 60)
    
    # Let's create a simple backtest to see what's happening
    print("\n📊 Running Simple Backtest Analysis")
    print("-" * 40)
    
    try:
        from backtesting.historical_backtester import HistoricalBacktester
        
        # Create a simple backtester with minimal data
        backtester = HistoricalBacktester(
            start_date="2024-01-01",
            end_date="2024-01-05",  # Just a few days
            butterfly_conversion_latest=True,
            pricing_mode="basic",
            max_risk_bull_pc=0.08,
            max_risk_bear_pc=0.07,
            max_risk_iron_butterfly_pc=0.07
        )
        
        # Run the backtest
        results = backtester.run_backtest()
        
        if not results:
            print("❌ No results generated")
            return
        
        print(f"✅ Generated {len(results)} results")
        
        # Analyze the results
        actual_trades = [r for r in results if r.num_contracts > 0]
        losing_trades = [r for r in actual_trades if not r.trade_successful]
        
        print(f"Total Results: {len(results)}")
        print(f"Actual Trades: {len(actual_trades)}")
        print(f"Losing Trades: {len(losing_trades)}")
        
        if losing_trades:
            print("\n📊 Analyzing Losing Trades")
            print("-" * 30)
            
            total_loss_pct = 0
            trade_details = []
            
            for i, trade in enumerate(losing_trades[:10]):  # Look at first 10 losing trades
                if trade.actual_risk_amount > 0:
                    loss_pct = abs(trade.profit_loss / trade.actual_risk_amount) * 100
                    total_loss_pct += loss_pct
                    
                    trade_details.append({
                        'date': trade.trading_date,
                        'decision': trade.decision,
                        'profit_loss': trade.profit_loss,
                        'actual_risk_amount': trade.actual_risk_amount,
                        'loss_pct': loss_pct,
                        'spread_price': trade.spread_price,
                        'num_contracts': trade.num_contracts
                    })
                    
                    print(f"Trade {i+1}: {trade.trading_date} {trade.decision}")
                    print(f"  P&L: ${trade.profit_loss:.2f}")
                    print(f"  Risk: ${trade.actual_risk_amount:.2f}")
                    print(f"  Loss %: {loss_pct:.1f}%")
                    print(f"  Spread Price: ${trade.spread_price:.2f}")
                    print(f"  Contracts: {trade.num_contracts}")
                    print()
            
            if losing_trades:
                avg_loss_pct = total_loss_pct / len(losing_trades)
                print(f"Average Loss %: {avg_loss_pct:.1f}%")
                
                # Check if we're getting the 99.2% pattern
                if 99.0 <= avg_loss_pct <= 99.5:
                    print("🔍 Found 99.2% pattern! Analyzing...")
                    
                    # Look for patterns in the individual trades
                    loss_percentages = [t['loss_pct'] for t in trade_details]
                    print(f"Individual Loss %s: {[f'{p:.1f}%' for p in loss_percentages]}")
                    
                    # Check if all losses are very close to 100%
                    near_100 = [p for p in loss_percentages if p >= 99.0 and p <= 100.1]
                    print(f"Losses near 100%: {len(near_100)}/{len(loss_percentages)}")
                    
                    if len(near_100) == len(loss_percentages):
                        print("🔍 All losses are very close to 100% - this suggests max loss scenarios")
                        print("This could be due to:")
                        print("1. All losing trades are hitting max loss")
                        print("2. There's a small calculation error in the loss percentage")
                        print("3. The actual_risk_amount is slightly inflated")
                        
                        # Check if there's a systematic difference
                        differences = []
                        for trade in trade_details:
                            expected_loss = trade['actual_risk_amount']
                            actual_loss = abs(trade['profit_loss'])
                            diff = expected_loss - actual_loss
                            differences.append(diff)
                        
                        avg_diff = sum(differences) / len(differences)
                        print(f"Average difference (expected - actual): ${avg_diff:.2f}")
                        
                        if abs(avg_diff) > 0.01:
                            print(f"🔍 Found systematic difference of ${avg_diff:.2f}")
                            print("This could explain the 99.2% average loss")
        
        # Let's also check the summary stats
        print("\n📊 Summary Statistics")
        print("-" * 25)
        
        summary = backtester.generate_summary_stats()
        
        if 'avg_loss_pct' in summary:
            print(f"Reported Average Loss %: {summary['avg_loss_pct']:.1f}%")
        
        if 'decision_profit_loss_stats' in summary:
            print("\nDecision-specific loss percentages:")
            for decision, stats in summary['decision_profit_loss_stats'].items():
                if 'avg_loss_pct' in stats:
                    print(f"  {decision}: {stats['avg_loss_pct']:.1f}%")
        
    except Exception as e:
        print(f"❌ Error running analysis: {str(e)}")
        import traceback
        traceback.print_exc()

def check_loss_calculation_code():
    """Check the actual loss calculation code for issues"""
    print("\n🔍 Checking Loss Calculation Code")
    print("-" * 35)
    
    # Let's look at the exact code that calculates the loss percentage
    print("From backtesting/historical_backtester.py generate_summary_stats():")
    print()
    print("for result in losing_results:")
    print("    if result.actual_risk_amount > 0:")
    print("        loss_pct = abs(result.profit_loss / result.actual_risk_amount) * 100")
    print("        total_loss_pct += loss_pct")
    print()
    print("avg_loss_pct = total_loss_pct / len(losing_results)")
    print()
    
    # Let's create a test case to see if there's a floating point precision issue
    print("🔍 Testing for Floating Point Precision Issues")
    print("-" * 45)
    
    # Test with exact values that might cause precision issues
    test_cases = [
        (100.0, 100.0),  # Should be 100%
        (99.2, 100.0),   # Should be 99.2%
        (99.0, 100.0),   # Should be 99.0%
        (99.5, 100.0),   # Should be 99.5%
    ]
    
    for profit_loss, actual_risk_amount in test_cases:
        loss_pct = abs(profit_loss / actual_risk_amount) * 100
        print(f"P&L: ${profit_loss:.2f}, Risk: ${actual_risk_amount:.2f} → Loss: {loss_pct:.1f}%")
    
    # Test with values that might cause the 99.2% issue
    print("\n🔍 Testing 99.2% Specific Values")
    print("-" * 35)
    
    # If we want 99.2% loss, what would the values be?
    target_loss_pct = 99.2
    actual_risk_amount = 100.0
    required_profit_loss = -(target_loss_pct / 100.0) * actual_risk_amount
    
    print(f"To get {target_loss_pct}% loss:")
    print(f"  Required P&L: ${required_profit_loss:.2f}")
    print(f"  Actual Risk: ${actual_risk_amount:.2f}")
    print(f"  Calculated Loss %: {abs(required_profit_loss / actual_risk_amount) * 100:.1f}%")
    
    # Check if this could be due to rounding in position sizing
    print("\n🔍 Checking Position Sizing Rounding")
    print("-" * 35)
    
    # Example: Balance = $10,000, risk = 7%
    balance = 10000.0
    risk_pc = 0.07
    max_risk_amount = balance * risk_pc
    
    # For different spread prices, calculate actual risk
    spread_prices = [2.20, 2.10, 2.00, 1.90]
    
    for spread_price in spread_prices:
        risk_per_contract = abs(spread_price) * 100
        num_contracts = int(max_risk_amount // risk_per_contract)
        actual_risk_amount = num_contracts * risk_per_contract
        
        # Calculate what loss % we'd get if we lost the full amount
        max_loss = -actual_risk_amount
        loss_pct = abs(max_loss / actual_risk_amount) * 100
        
        print(f"Spread: ${spread_price:.2f}, Risk/Contract: ${risk_per_contract:.2f}")
        print(f"  Contracts: {num_contracts}, Actual Risk: ${actual_risk_amount:.2f}")
        print(f"  Max Loss: ${max_loss:.2f}, Loss %: {loss_pct:.1f}%")
        print()

if __name__ == "__main__":
    analyze_actual_backtest_results()
    check_loss_calculation_code() 