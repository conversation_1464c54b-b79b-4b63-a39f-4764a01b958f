#!/usr/bin/env python3
"""
Test script to verify backtesting BF conversion logic matches live trading
"""

import sys
import os

# Add the current directory to path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backtesting.decision_engine import analyze_normalized_bars, print_decision_result

def test_backtesting_bf_logic():
    """Test the backtesting BF conversion logic"""
    print("🧪 Testing Backtesting BF-Bull/BF-Bear Conversion Logic")
    print("=" * 70)
    
    # Test Case 1: First bar close > 66% but no breakout in bars 2-3
    print("\n📊 Test Case 1: First bar close > 66%, no breakout")
    print("-" * 50)
    
    # Create test data where first bar closes at 75% of SPAN1
    # SPAN1: 4990-5010 = 20 points, close at 5007.5 = 75% of range
    bar1 = (5000.0, 5010.0, 4990.0, 5007.5)  # 75% of SPAN1
    
    # Second bar: no breakout
    bar2 = (5007.5, 5012.0, 5005.0, 5008.0)
    
    # Third bar: no breakout
    bar3 = (5008.0, 5013.0, 5006.0, 5009.0)
    
    result = analyze_normalized_bars(bar1, bar2, bar3)
    print_decision_result(result)
    
    expected_decision = "BF-bull"
    assert result.decision == expected_decision, f"Expected {expected_decision}, got {result.decision}"
    assert "BF conversion: BF-bull" in result.reasoning, "Missing BF conversion in reasoning"
    print("✅ Test Case 1 PASSED")
    
    # Test Case 2: First bar close ≤ 66% but no breakout in bars 2-3
    print("\n📊 Test Case 2: First bar close ≤ 66%, no breakout")
    print("-" * 50)
    
    # Create test data where first bar closes at 45% of SPAN1
    bar1 = (5000.0, 5010.0, 4990.0, 5001.0)  # 45% of SPAN1
    
    result = analyze_normalized_bars(bar1, bar2, bar3)
    print_decision_result(result)
    
    expected_decision = "BF-bear"
    assert result.decision == expected_decision, f"Expected {expected_decision}, got {result.decision}"
    assert "BF conversion: BF-bear" in result.reasoning, "Missing BF conversion in reasoning"
    print("✅ Test Case 2 PASSED")
    
    # Test Case 3: Breakout in second bar should override BF conversion
    print("\n📊 Test Case 3: Breakout in second bar overrides BF conversion")
    print("-" * 50)
    
    # First bar: close at 75% of SPAN1 (would trigger BF-Bull if no breakout)
    bar1 = (5000.0, 5010.0, 4990.0, 5007.5)  # 75% of SPAN1
    
    # Second bar: bull breakout (high above first bar high, close > 88% of SPAN2)
    bar2_breakout = (5007.5, 5015.0, 5005.0, 5013.0)  # Breakout
    
    result = analyze_normalized_bars(bar1, bar2_breakout, bar3)
    print_decision_result(result)
    
    expected_decision = "bull"
    assert result.decision == expected_decision, f"Expected {expected_decision}, got {result.decision}"
    assert "Bar 2 breakout: bull" in result.reasoning, "Missing breakout detection in reasoning"
    assert "BF conversion" not in result.reasoning, "BF conversion should not occur with breakout"
    print("✅ Test Case 3 PASSED")
    
    # Test Case 4: Narrow conversion for BF decisions
    print("\n📊 Test Case 4: Narrow conversion for BF decisions")
    print("-" * 50)
    
    # Create narrow range scenario (SPAN3 < 4.0)
    bar1 = (5000.0, 5002.0, 4998.0, 5001.5)  # 75% of SPAN1, narrow range
    bar2 = (5001.5, 5003.0, 4999.0, 5002.0)
    bar3 = (5002.0, 5004.0, 5000.0, 5002.5)
    
    result = analyze_normalized_bars(bar1, bar2, bar3)
    print_decision_result(result)
    
    # Should be narrow_bull (BF-bull converted to narrow due to SPAN3 < 4.0)
    expected_decision = "narrow_bull"
    assert result.decision == expected_decision, f"Expected {expected_decision}, got {result.decision}"
    assert "Narrow conversion: narrow_bull" in result.reasoning, "Missing narrow conversion in reasoning"
    print("✅ Test Case 4 PASSED")
    
    print("\n🎉 All backtesting tests passed! BF conversion logic matches live trading.")
    print("\nSummary:")
    print("- BF conversion happens in third bar analysis (not first bar)")
    print("- Breakout conditions take precedence over BF conversion")
    print("- BF-Bull/BF-Bear are treated the same as BULL/BEAR for narrow conversions")
    print("- Backtesting now matches live trading logic exactly")

if __name__ == "__main__":
    test_backtesting_bf_logic() 