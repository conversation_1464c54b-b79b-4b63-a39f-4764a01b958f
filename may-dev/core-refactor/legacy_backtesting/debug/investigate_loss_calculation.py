#!/usr/bin/env python3
"""
Investigate the 99.2% average loss issue in backtesting
"""

import sys
import os
from typing import Dict, List, Tuple

# Add the current directory to path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_risk_calculation_issue():
    """Analyze the risk calculation issue causing 99.2% average loss"""
    print("🔍 Investigating 99.2% Average Loss Issue")
    print("=" * 60)
    
    # Let's examine the actual risk calculation logic from the code
    print("\n📊 Analysis of Risk Calculation Logic")
    print("-" * 40)
    
    print("From backtesting/historical_backtester.py calculate_position_size():")
    print()
    
    print("For Bull/Bear Spreads:")
    print("  risk_per_contract = abs(spread_price) * 100")
    print("  actual_risk_amount = num_contracts * risk_per_contract")
    print()
    
    print("For Iron Butterflies:")
    print("  credit_received = abs(spread_price)  # spread_price is negative")
    print("  strike_width = 5.0")
    print("  risk_per_contract = (strike_width - credit_received) * 100")
    print("  actual_risk_amount = num_contracts * risk_per_contract")
    print()
    
    # Test specific scenarios
    print("\n📊 Testing Specific Scenarios")
    print("-" * 30)
    
    # Scenario 1: Bull spread with $2.20 debit
    print("Scenario 1: Bull Spread ($2.20 debit)")
    spread_price = 2.20
    num_contracts = 3
    risk_per_contract = abs(spread_price) * 100  # $220
    actual_risk_amount = num_contracts * risk_per_contract  # $660
    max_loss = -actual_risk_amount  # -$660
    
    print(f"  Spread Price: ${spread_price:.2f}")
    print(f"  Risk per Contract: ${risk_per_contract:.2f}")
    print(f"  Number of Contracts: {num_contracts}")
    print(f"  Actual Risk Amount: ${actual_risk_amount:.2f}")
    print(f"  Max Loss: ${max_loss:.2f}")
    print(f"  Loss % if max loss: {abs(max_loss / actual_risk_amount) * 100:.1f}%")
    print()
    
    # Scenario 2: Iron butterfly with $3.40 credit
    print("Scenario 2: Iron Butterfly ($3.40 credit)")
    spread_price = -3.40  # Negative for credit
    num_contracts = 2
    credit_received = abs(spread_price)  # $3.40
    strike_width = 5.0
    risk_per_contract = (strike_width - credit_received) * 100  # (5.0 - 3.40) * 100 = $160
    actual_risk_amount = num_contracts * risk_per_contract  # $320
    max_loss = -actual_risk_amount  # -$320
    
    print(f"  Spread Price: ${spread_price:.2f} (credit)")
    print(f"  Credit Received: ${credit_received:.2f}")
    print(f"  Strike Width: {strike_width:.1f}")
    print(f"  Risk per Contract: ${risk_per_contract:.2f}")
    print(f"  Number of Contracts: {num_contracts}")
    print(f"  Actual Risk Amount: ${actual_risk_amount:.2f}")
    print(f"  Max Loss: ${max_loss:.2f}")
    print(f"  Loss % if max loss: {abs(max_loss / actual_risk_amount) * 100:.1f}%")
    print()
    
    # Investigate the 99.2% issue
    print("\n🔍 Investigating 99.2% Issue")
    print("-" * 30)
    
    # If we're getting 99.2% instead of 100%, it means:
    # actual_loss / actual_risk_amount = 0.992
    # So actual_risk_amount = actual_loss / 0.992
    
    actual_loss = 100.0
    actual_risk_amount_99_2 = actual_loss / 0.992
    difference = actual_risk_amount_99_2 - actual_loss
    
    print(f"To get 99.2% loss:")
    print(f"  Actual Loss: ${actual_loss:.2f}")
    print(f"  Required Actual Risk Amount: ${actual_risk_amount_99_2:.2f}")
    print(f"  Difference: ${difference:.2f}")
    print(f"  This suggests actual_risk_amount is ${difference:.2f} higher than actual loss")
    print()
    
    # Let's check if this could be due to position sizing rounding
    print("🔍 Position Sizing Analysis")
    print("-" * 30)
    
    # Example: Balance = $10,000, risk = 7%
    balance = 10000.0
    risk_pc = 0.07
    max_risk_amount = balance * risk_pc  # $700
    
    print(f"Balance: ${balance:.2f}")
    print(f"Risk %: {risk_pc * 100:.1f}%")
    print(f"Max Risk Amount: ${max_risk_amount:.2f}")
    print()
    
    # For bull spread with $2.20 debit
    spread_price = 2.20
    risk_per_contract = abs(spread_price) * 100  # $220
    num_contracts = int(max_risk_amount // risk_per_contract)  # 700 // 220 = 3
    actual_risk_amount = num_contracts * risk_per_contract  # 3 * 220 = $660
    
    print(f"Bull Spread Analysis:")
    print(f"  Spread Price: ${spread_price:.2f}")
    print(f"  Risk per Contract: ${risk_per_contract:.2f}")
    print(f"  Max Contracts Possible: {int(max_risk_amount // risk_per_contract)}")
    print(f"  Actual Contracts: {num_contracts}")
    print(f"  Actual Risk Amount: ${actual_risk_amount:.2f}")
    print(f"  Unused Risk: ${max_risk_amount - actual_risk_amount:.2f}")
    print()
    
    # Check if the issue is that we're using max_risk_amount instead of actual_risk_amount
    print("🔍 Potential Issue: Using max_risk_amount vs actual_risk_amount")
    print("-" * 50)
    
    print("If the loss calculation is using max_risk_amount instead of actual_risk_amount:")
    print(f"  Max Risk Amount: ${max_risk_amount:.2f}")
    print(f"  Actual Risk Amount: ${actual_risk_amount:.2f}")
    print(f"  Difference: ${max_risk_amount - actual_risk_amount:.2f}")
    
    # Calculate what loss percentage we'd get if using max_risk_amount
    max_loss = -actual_risk_amount  # -$660
    loss_pct_with_max_risk = abs(max_loss / max_risk_amount) * 100
    loss_pct_with_actual_risk = abs(max_loss / actual_risk_amount) * 100
    
    print(f"  Loss % using max_risk_amount: {loss_pct_with_max_risk:.1f}%")
    print(f"  Loss % using actual_risk_amount: {loss_pct_with_actual_risk:.1f}%")
    print()
    
    # Check if this matches the 99.2% pattern
    print("🔍 Does this match the 99.2% pattern?")
    print("-" * 30)
    
    # 660/700 = 0.943, which is 94.3% - not 99.2%
    # Let's find what ratio would give us 99.2%
    ratio_for_99_2 = 0.992
    print(f"Ratio for 99.2% loss: {ratio_for_99_2}")
    print(f"Our ratio (660/700): {actual_risk_amount/max_risk_amount:.3f}")
    print(f"This doesn't match the 99.2% pattern")
    print()
    
    # Let's check if there's an issue with the P&L calculation itself
    print("🔍 P&L Calculation Analysis")
    print("-" * 30)
    
    print("From determine_trade_outcome() for bull spreads:")
    print("  debit_paid_per_contract = spread_price")
    print("  total_debit_paid = debit_paid_per_contract * 100 * num_contracts")
    print("  if closing_price <= long_strike:")
    print("    profit_loss = -abs(total_debit_paid)")
    print()
    
    # Let's verify this calculation
    spread_price = 2.20
    num_contracts = 3
    debit_paid_per_contract = spread_price
    total_debit_paid = debit_paid_per_contract * 100 * num_contracts
    profit_loss = -abs(total_debit_paid)
    
    print(f"Verification:")
    print(f"  Debit per Contract: ${debit_paid_per_contract:.2f}")
    print(f"  Total Debit Paid: ${total_debit_paid:.2f}")
    print(f"  Profit/Loss: ${profit_loss:.2f}")
    print(f"  Expected: -${spread_price * 100 * num_contracts:.2f}")
    print()
    
    # The issue might be in how actual_risk_amount is calculated vs how profit_loss is calculated
    print("🔍 Comparing actual_risk_amount vs profit_loss calculation")
    print("-" * 50)
    
    print("actual_risk_amount calculation:")
    print("  risk_per_contract = abs(spread_price) * 100")
    print("  actual_risk_amount = num_contracts * risk_per_contract")
    print()
    
    print("profit_loss calculation:")
    print("  total_debit_paid = spread_price * 100 * num_contracts")
    print("  profit_loss = -abs(total_debit_paid)")
    print()
    
    # These should be the same! Let's verify
    risk_per_contract = abs(spread_price) * 100
    actual_risk_amount = num_contracts * risk_per_contract
    total_debit_paid = spread_price * 100 * num_contracts
    profit_loss = -abs(total_debit_paid)
    
    print(f"Comparison:")
    print(f"  actual_risk_amount: ${actual_risk_amount:.2f}")
    print(f"  abs(profit_loss): ${abs(profit_loss):.2f}")
    print(f"  Are they equal? {'✅' if abs(actual_risk_amount - abs(profit_loss)) < 0.01 else '❌'}")
    print()
    
    # If they're equal, then the issue must be elsewhere
    # Let me check if there's a subtle difference in the calculation
    print("🔍 Looking for subtle differences")
    print("-" * 30)
    
    # Maybe the issue is with iron butterflies specifically
    print("Iron Butterfly Analysis:")
    spread_price = -3.40
    num_contracts = 2
    
    # actual_risk_amount calculation
    credit_received = abs(spread_price)
    strike_width = 5.0
    risk_per_contract = (strike_width - credit_received) * 100
    actual_risk_amount = num_contracts * risk_per_contract
    
    # profit_loss calculation (max loss)
    credit_per_contract = abs(spread_price)
    total_credit_received = credit_per_contract * 100 * num_contracts
    max_loss_per_contract = 500.0 - (credit_per_contract * 100)
    total_max_loss = max_loss_per_contract * num_contracts
    profit_loss = -total_max_loss
    
    print(f"  Credit per Contract: ${credit_per_contract:.2f}")
    print(f"  Strike Width: {strike_width:.1f}")
    print(f"  Risk per Contract: ${risk_per_contract:.2f}")
    print(f"  Actual Risk Amount: ${actual_risk_amount:.2f}")
    print(f"  Max Loss per Contract: ${max_loss_per_contract:.2f}")
    print(f"  Total Max Loss: ${total_max_loss:.2f}")
    print(f"  Profit/Loss: ${profit_loss:.2f}")
    print(f"  Are they equal? {'✅' if abs(actual_risk_amount - abs(profit_loss)) < 0.01 else '❌'}")
    
    if abs(actual_risk_amount - abs(profit_loss)) > 0.01:
        print(f"  Difference: ${abs(actual_risk_amount - abs(profit_loss)):.2f}")
        print(f"  This could be the source of the 99.2% issue!")

if __name__ == "__main__":
    analyze_risk_calculation_issue() 