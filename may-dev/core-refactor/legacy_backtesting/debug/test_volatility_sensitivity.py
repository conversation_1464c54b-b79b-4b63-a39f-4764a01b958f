#!/usr/bin/env python3
"""
Test how different volatility inputs affect iron butterfly pricing
Compare VIX-based vs. intraday volatility assumptions
"""

from options_pricer import price_iron_butterfly, black_scholes_price
import math

def test_volatility_sensitivity():
    """Test iron butterfly pricing across different volatility assumptions"""
    
    # Test parameters
    S = 6000.0  # SPX price
    center_strike = 6000
    T_minutes = 8.5  # Time to expiry
    
    # Different volatility scenarios
    volatility_scenarios = {
        'Current (20%)': 0.20,
        'VIX Low (15%)': 0.15,
        'VIX High (25%)': 0.25,
        'Intraday Low (30%)': 0.30,
        'Intraday High (40%)': 0.40,
        'End-of-Day (50%)': 0.50,
        'Panic (75%)': 0.75,
        'Extreme (100%)': 1.00
    }
    
    print("Iron Butterfly Volatility Sensitivity Analysis")
    print("=" * 80)
    print("Structure: 5995/6000/6000/6005 at SPX = 6000")
    print(f"Time to expiry: {T_minutes} minutes")
    print("-" * 80)
    print("Volatility Scenario    | Credit  | Put 6000 | Call 6000| Put 5995 | Call 6005")
    print("-" * 80)
    
    for scenario_name, vol in volatility_scenarios.items():
        # Calculate iron butterfly credit
        credit = price_iron_butterfly(S=S, center_strike=center_strike, T=T_minutes, sigma=vol)
        
        # Break down individual option prices
        T_years = T_minutes / (60 * 24 * 365.25)
        put_6000 = black_scholes_price(S, 6000, T_years, 0.05, vol, 'put')
        call_6000 = black_scholes_price(S, 6000, T_years, 0.05, vol, 'call')
        put_5995 = black_scholes_price(S, 5995, T_years, 0.05, vol, 'put')
        call_6005 = black_scholes_price(S, 6005, T_years, 0.05, vol, 'call')
        
        print(f"{scenario_name:18} | ${abs(credit):6.2f} | "
              f"${put_6000:7.3f} | ${call_6000:7.3f} | ${put_5995:7.3f} | ${call_6005:7.3f}")
    
    print("\n" + "=" * 80)
    print("Key Insights:")
    print("1. Higher volatility = Higher option prices = Higher credit collected")
    print("2. For 8.5-minute options, intraday vol (30-50%) may be more realistic")
    print("3. End-of-day volatility spikes could justify 50%+ volatility")
    
    # Test across different SPX prices with realistic volatility
    print("\n" + "=" * 80)
    print("Realistic Volatility Test (40% intraday vol)")
    print("-" * 80)
    print("SPX Price | Credit | Above $3.40? | Above $4.00? | Above $5.00?")
    print("-" * 80)
    
    realistic_vol = 0.40
    for spx_price in [5995, 5997, 5999, 6000, 6001, 6003, 6005]:
        center = round(spx_price / 5) * 5
        credit = price_iron_butterfly(S=spx_price, center_strike=center, T=T_minutes, sigma=realistic_vol)
        credit_amount = abs(credit)
        
        print(f"{spx_price:8.0f} | ${credit_amount:5.2f} | "
              f"{'YES' if credit_amount >= 3.40 else 'NO':11} | "
              f"{'YES' if credit_amount >= 4.00 else 'NO':11} | "
              f"{'YES' if credit_amount >= 5.00 else 'NO':9}")

def calculate_historical_volatility_from_spx_data():
    """
    Placeholder for calculating actual volatility from SPX intraday data
    This would read your database and calculate realized volatility
    """
    print("\n" + "=" * 80)
    print("Historical Volatility Calculation (Placeholder)")
    print("-" * 80)
    print("To implement:")
    print("1. Pull SPX 10-second data for last 30 trading days")
    print("2. Calculate 10-second returns: (price[t] - price[t-1]) / price[t-1]")
    print("3. Annualize: vol = std(returns) * sqrt(252 * 6.5 * 60 * 6)")
    print("4. Focus on 15:50-16:00 time window for end-of-day volatility")
    print("5. Compare to VIX levels for calibration")

def test_time_decay_impact():
    """Test how time to expiry affects pricing"""
    print("\n" + "=" * 80)
    print("Time Decay Impact on Iron Butterfly Credit")
    print("-" * 80)
    print("Time to Expiry | Credit @ 30% Vol | Credit @ 50% Vol")
    print("-" * 80)
    
    S = 6000
    center = 6000
    
    time_scenarios = [
        ("30 minutes", 30),
        ("15 minutes", 15),
        ("8.5 minutes", 8.5),
        ("5 minutes", 5),
        ("2 minutes", 2),
        ("1 minute", 1),
        ("30 seconds", 0.5)
    ]
    
    for time_name, minutes in time_scenarios:
        credit_30 = abs(price_iron_butterfly(S=S, center_strike=center, T=minutes, sigma=0.30))
        credit_50 = abs(price_iron_butterfly(S=S, center_strike=center, T=minutes, sigma=0.50))
        
        print(f"{time_name:14} | ${credit_30:14.3f} | ${credit_50:14.3f}")

if __name__ == "__main__":
    test_volatility_sensitivity()
    test_time_decay_impact()
    calculate_historical_volatility_from_spx_data() 