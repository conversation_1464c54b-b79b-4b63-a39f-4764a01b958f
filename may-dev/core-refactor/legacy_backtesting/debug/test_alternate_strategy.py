#!/usr/bin/env python3
"""
Test script for alternate strategy conversions
Verifies that the strategy conversions are working as expected
"""

import sys
import os
import logging

# Add paths to import from the main system
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from decision_engine import analyze_normalized_bars, DecisionResult
from historical_backtester import HistoricalBacktester

def test_alternate_strategy_conversions():
    """Test the alternate strategy conversion logic"""
    
    # Create a backtester with alternate strategy enabled
    backtester = HistoricalBacktester(alternate_strategy=True)
    
    print("🧪 Testing Alternate Strategy Conversions")
    print("="*60)
    
    # Test case 1: narrow_bull should convert to bull
    print("\n1. Testing narrow_bull → bull conversion:")
    
    # Create sample bars that would produce narrow_bull (SPAN3 < 4.0 and bull conditions)
    bar1 = (5800.0, 5802.0, 5798.0, 5801.5)  # Close at ~87% (high in range)
    bar2 = (5801.5, 5804.0, 5800.0, 5803.0)  # Breakout higher  
    bar3 = (5803.0, 5805.0, 5802.0, 5804.0)  # Continue higher, but narrow span
    
    original_result = analyze_normalized_bars(bar1, bar2, bar3)
    print(f"   Original decision: {original_result.decision}")
    print(f"   SPAN3: {original_result.spans['span3']:.2f}")
    
    # Apply conversion
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Converted decision: {converted_result.decision}")
    print(f"   Reasoning: {converted_result.reasoning}")
    
    # Test case 2: narrow_bear should convert to bear
    print("\n2. Testing narrow_bear → bear conversion:")
    
    # Create sample bars that would produce narrow_bear (SPAN3 < 4.0 and bear conditions)
    bar1 = (5800.0, 5802.0, 5797.0, 5798.0)  # Close at ~25% (low in range)
    bar2 = (5798.0, 5799.0, 5795.0, 5796.0)  # Breakout lower
    bar3 = (5796.0, 5798.0, 5794.0, 5795.0)  # Continue lower, but narrow span
    
    original_result = analyze_normalized_bars(bar1, bar2, bar3)
    print(f"   Original decision: {original_result.decision}")
    print(f"   SPAN3: {original_result.spans['span3']:.2f}")
    
    # Apply conversion
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Converted decision: {converted_result.decision}")
    print(f"   Reasoning: {converted_result.reasoning}")
    
    # Test case 3: iron_butterfly with 1st bar close > 66% should convert to bull
    print("\n3. Testing iron_butterfly → bull conversion (1st bar close > 66%):")
    
    # Create sample bars that would produce iron_butterfly with high 1st bar close
    bar1 = (5800.0, 5803.0, 5798.0, 5802.0)  # Close at ~80% (high in range)
    bar2 = (5802.0, 5804.0, 5800.0, 5801.0)  # Stay in range
    bar3 = (5801.0, 5803.0, 5799.0, 5801.5)  # Stay in range
    
    original_result = analyze_normalized_bars(bar1, bar2, bar3)
    print(f"   Original decision: {original_result.decision}")
    print(f"   1st bar close %: {original_result.close_percentages[0]:.1f}%")
    
    # Apply conversion
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Converted decision: {converted_result.decision}")
    print(f"   Reasoning: {converted_result.reasoning}")
    
    # Test case 4: iron_butterfly with 1st bar close < 66% should convert to bear
    print("\n4. Testing iron_butterfly → bear conversion (1st bar close < 66%):")
    
    # Create sample bars that would produce iron_butterfly with low 1st bar close
    bar1 = (5800.0, 5803.0, 5797.0, 5798.5)  # Close at ~25% (low in range)
    bar2 = (5798.5, 5801.0, 5796.0, 5799.0)  # Stay in range
    bar3 = (5799.0, 5801.0, 5796.0, 5798.0)  # Stay in range
    
    original_result = analyze_normalized_bars(bar1, bar2, bar3)
    print(f"   Original decision: {original_result.decision}")
    print(f"   1st bar close %: {original_result.close_percentages[0]:.1f}%")
    
    # Apply conversion
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Converted decision: {converted_result.decision}")
    print(f"   Reasoning: {converted_result.reasoning}")
    
    # Test case 5: Normal bull should remain unchanged
    print("\n5. Testing bull decision (should remain unchanged):")
    
    # Create sample bars that would produce normal bull
    bar1 = (5800.0, 5805.0, 5795.0, 5804.0)  # Close at ~90% (very high)
    bar2 = (5804.0, 5810.0, 5800.0, 5808.0)  # Breakout higher
    bar3 = (5808.0, 5815.0, 5805.0, 5812.0)  # Continue higher
    
    original_result = analyze_normalized_bars(bar1, bar2, bar3)
    print(f"   Original decision: {original_result.decision}")
    print(f"   SPAN3: {original_result.spans['span3']:.2f}")
    
    # Apply conversion
    converted_result = backtester.apply_alternate_strategy_conversion(original_result)
    print(f"   Converted decision: {converted_result.decision}")
    print(f"   Should be same as original: {converted_result.decision == original_result.decision}")
    
    # Test case 6: Test with alternate strategy disabled
    print("\n6. Testing with alternate strategy disabled:")
    backtester_normal = HistoricalBacktester(alternate_strategy=False)
    
    # Use the narrow_bull case from test 1
    original_result = analyze_normalized_bars(bar1, bar2, bar3)
    converted_result = backtester_normal.apply_alternate_strategy_conversion(original_result)
    
    print(f"   Alternate strategy enabled: {backtester_normal.alternate_strategy}")
    print(f"   Original decision: {original_result.decision}")
    print(f"   After 'conversion': {converted_result.decision}")
    print(f"   Should be unchanged: {converted_result.decision == original_result.decision}")
    
    print(f"\n{'='*60}")
    print("✅ Alternate strategy conversion tests completed!")

def test_command_line_integration():
    """Test running the backtester with alternate strategy from command line"""
    print(f"\n🔧 Command Line Integration Test")
    print("="*60)
    
    print("To test the alternate strategy from command line, use:")
    print("python historical_backtester.py --alternate-strategy --start-date 2025-01-01 --end-date 2025-01-05")
    print("")
    print("This will:")
    print("• Convert narrow_bull decisions to bull (identical strike logic)")
    print("• Convert narrow_bear decisions to bear (identical strike logic)")
    print("• Convert iron_butterfly to bull if 1st bar close > 66%")
    print("• Convert iron_butterfly to bear if 1st bar close < 66%")
    print("• Save results with '_alt' suffix in filename")
    print("• Show 'Alternate Strategy: ENABLED' in summary")

if __name__ == "__main__":
    # Set up basic logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    # Run tests
    test_alternate_strategy_conversions()
    test_command_line_integration() 