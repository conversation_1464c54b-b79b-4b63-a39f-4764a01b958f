#!/usr/bin/env python3
"""
Test Script for Historical Backtesting System
Validates the complete end-to-end process with a small date range
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# Add paths to import from the main system
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Import our components
from database_connector import DatabaseConnector
from bar_processor import BarProcessor
from historical_backtester import HistoricalBacktester


def test_database_connection():
    """Test database connection and data retrieval"""
    print("\n1. Testing Database Connection...")
    
    try:
        with DatabaseConnector() as db:
            # Test getting trading dates
            dates = db.get_trading_dates(start_date='2025-01-01', end_date='2025-01-05')
            print(f"✅ Found {len(dates)} trading dates: {dates}")
            
            if dates:
                # Test getting daily data
                daily_data = db.get_daily_end_of_day_data(dates[0])
                if daily_data:
                    print(f"✅ Successfully retrieved data for {dates[0]}")
                    print(f"   Pre-bar close: {daily_data['pre_bar']['close']:.2f}")
                    print(f"   Number of 10-sec bars: {len(daily_data['bars_10sec'])}")
                    print(f"   Closing price: {daily_data['closing_price']:.2f}")
                    return daily_data
                else:
                    print(f"❌ Failed to retrieve daily data for {dates[0]}")
                    return None
            else:
                print("❌ No trading dates found")
                return None
                
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        return None


def test_bar_processing(sample_data):
    """Test bar processing logic"""
    print("\n2. Testing Bar Processing...")
    
    try:
        if not sample_data:
            print("❌ No sample data to process")
            return None
            
        adjusted_bars, closing_price = BarProcessor.process_daily_data(sample_data)
        
        print(f"✅ Successfully processed bars")
        print(f"   Number of adjusted bars: {len(adjusted_bars)}")
        print(f"   Bar 1 OHLC: {adjusted_bars[0]}")
        print(f"   Bar 2 OHLC: {adjusted_bars[1]}")
        print(f"   Bar 3 OHLC: {adjusted_bars[2]}")
        print(f"   Closing price: {closing_price:.2f}")
        
        return adjusted_bars, closing_price
        
    except Exception as e:
        print(f"❌ Bar processing failed: {str(e)}")
        return None


def test_decision_engine(bars_data):
    """Test decision engine"""
    print("\n3. Testing Decision Engine...")
    
    try:
        if not bars_data:
            print("❌ No bar data to analyze")
            return None
            
        adjusted_bars, closing_price = bars_data
        
        # Import and test decision engine
        from decision_engine import analyze_normalized_bars
        
        decision_result = analyze_normalized_bars(
            adjusted_bars[0], 
            adjusted_bars[1], 
            adjusted_bars[2]
        )
        
        print(f"✅ Decision engine analysis completed")
        print(f"   Decision: {decision_result.decision}")
        print(f"   Target Price: ${decision_result.target_price:.0f}")
        print(f"   Bias: {decision_result.bias}")
        print(f"   Reasoning: {decision_result.reasoning}")
        print(f"   Spans: {decision_result.spans}")
        
        return decision_result
        
    except Exception as e:
        print(f"❌ Decision engine failed: {str(e)}")
        return None


def test_full_backtest():
    """Test complete backtesting process"""
    print("\n4. Testing Complete Backtest (Limited Range)...")
    
    try:
        # Test with just a few days
        backtester = HistoricalBacktester(
            start_date='2025-01-01', 
            end_date='2025-01-03'
        )
        
        results = backtester.run_backtest()
        
        if results:
            print(f"✅ Backtest completed successfully")
            print(f"   Processed {len(results)} days")
            
            # Print summary
            backtester.print_summary()
            
            # Save results (optional)
            # backtester.save_results_to_csv()
            # backtester.save_summary_to_json()
            
            return True
        else:
            print("❌ No backtest results generated")
            return False
            
    except Exception as e:
        print(f"❌ Complete backtest failed: {str(e)}")
        return False


def main():
    """Run all tests"""
    print("🧪 TESTING HISTORICAL BACKTESTING SYSTEM")
    print("="*60)
    
    # Set up logging for debugging
    logging.basicConfig(
        level=logging.WARNING,  # Reduce noise
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Test each component
    sample_data = test_database_connection()
    bars_data = test_bar_processing(sample_data)
    decision_result = test_decision_engine(bars_data)
    backtest_success = test_full_backtest()
    
    print("\n" + "="*60)
    print("TEST SUMMARY:")
    print(f"Database Connection: {'✅' if sample_data else '❌'}")
    print(f"Bar Processing: {'✅' if bars_data else '❌'}")
    print(f"Decision Engine: {'✅' if decision_result else '❌'}")
    print(f"Complete Backtest: {'✅' if backtest_success else '❌'}")
    
    if all([sample_data, bars_data, decision_result, backtest_success]):
        print("\n🎉 ALL TESTS PASSED! System is ready for full backtesting.")
        print("\nTo run full backtest:")
        print("python backtesting/historical_backtester.py --start-date 2025-01-01 --end-date 2025-01-31")
    else:
        print("\n⚠️  SOME TESTS FAILED - Review configuration and database connection.")
    
    print("="*60)


if __name__ == "__main__":
    main() 