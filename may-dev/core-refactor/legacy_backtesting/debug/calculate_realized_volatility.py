#!/usr/bin/env python3
"""
Calculate realized volatility from SPX 10-second data
Focus on end-of-day period (15:50-16:00) to calibrate our options pricing model
"""

import pandas as pd
import numpy as np
from database_connector import DatabaseConnector
from datetime import datetime, timedelta
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def calculate_intraday_volatility(start_date='2025-01-01', end_date='2025-06-18', time_window='15:50-16:00'):
    """
    Calculate realized volatility from SPX 10-second data
    
    Args:
        start_date: Start date for analysis
        end_date: End date for analysis
        time_window: Time window to focus on (default: end-of-day)
    
    Returns:
        Dict with volatility statistics
    """
    
    db = DatabaseConnector()
    
    try:
        with db:
            # Get all trading dates in our range
            trading_dates = db.get_trading_dates(start_date, end_date)
            logger.info(f"Analyzing {len(trading_dates)} trading days")
            
            all_returns = []
            daily_volatilities = []
            
            for i, date in enumerate(trading_dates):
                try:
                    # Get 10-second data for this day
                    daily_data = db.get_daily_end_of_day_data(date)
                    
                    if not daily_data:
                        logger.warning(f"No data for {date}")
                        continue
                    
                    # Convert to DataFrame for easier analysis
                    df = pd.DataFrame(daily_data, columns=['id', 'datetime', 'open', 'high', 'low', 'close'])
                    df['datetime'] = pd.to_datetime(df['datetime'])
                    df = df.sort_values('datetime')
                    
                    # Focus on end-of-day period (15:50:00 to 16:00:00)
                    df['time_str'] = df['datetime'].dt.strftime('%H:%M:%S')
                    eod_data = df[(df['time_str'] >= '15:50:00') & (df['time_str'] <= '16:00:00')].copy()
                    
                    if len(eod_data) < 2:
                        logger.warning(f"Insufficient end-of-day data for {date}")
                        continue
                    
                    # Calculate 10-second returns
                    eod_data['returns'] = eod_data['close'].pct_change()
                    returns = eod_data['returns'].dropna()
                    
                    if len(returns) < 5:  # Need at least 5 returns for meaningful calculation
                        continue
                    
                    all_returns.extend(returns.tolist())
                    
                    # Calculate daily volatility for this day
                    daily_vol = returns.std()
                    daily_volatilities.append(daily_vol)
                    
                    if (i + 1) % 50 == 0:
                        logger.info(f"Processed {i+1}/{len(trading_dates)} days")
                        
                except Exception as e:
                    logger.error(f"Error processing {date}: {e}")
                    continue
            
            if not all_returns:
                logger.error("No valid returns calculated")
                return None
            
            # Convert to numpy arrays for calculations
            all_returns = np.array(all_returns)
            daily_volatilities = np.array(daily_volatilities)
            
            # Calculate volatility statistics
            # Annualize the volatility:
            # - 10-second intervals
            # - 6.5 hours per trading day = 390 minutes = 2340 ten-second intervals
            # - 252 trading days per year
            intervals_per_day = 6.5 * 60 * 6  # 2340 ten-second intervals per day
            intervals_per_year = intervals_per_day * 252
            
            # Standard volatility calculation
            return_std = np.std(all_returns)
            annualized_vol = return_std * np.sqrt(intervals_per_year)
            
            # Alternative: Average daily volatility approach
            avg_daily_vol = np.mean(daily_volatilities)
            annualized_daily_vol = avg_daily_vol * np.sqrt(intervals_per_day) * np.sqrt(252)
            
            # End-of-day specific volatility (last 10 minutes)
            eod_intervals_per_day = 10 * 60 / 10  # 60 ten-second intervals in 10 minutes
            eod_annualized_vol = return_std * np.sqrt(eod_intervals_per_day * 252)
            
            results = {
                'total_returns': len(all_returns),
                'trading_days': len(daily_volatilities),
                'date_range': f"{start_date} to {end_date}",
                'time_window': time_window,
                
                # Volatility measures
                'annualized_vol_full': annualized_vol,
                'annualized_vol_daily': annualized_daily_vol,
                'eod_specific_vol': eod_annualized_vol,
                
                # Statistics
                'mean_return': np.mean(all_returns),
                'std_return': return_std,
                'min_return': np.min(all_returns),
                'max_return': np.max(all_returns),
                
                # Daily volatility stats
                'avg_daily_vol': avg_daily_vol,
                'min_daily_vol': np.min(daily_volatilities),
                'max_daily_vol': np.max(daily_volatilities),
                'median_daily_vol': np.median(daily_volatilities),
                
                # Percentiles for different scenarios
                'vol_25th_percentile': np.percentile(daily_volatilities, 25) * np.sqrt(intervals_per_day) * np.sqrt(252),
                'vol_50th_percentile': np.percentile(daily_volatilities, 50) * np.sqrt(intervals_per_day) * np.sqrt(252),
                'vol_75th_percentile': np.percentile(daily_volatilities, 75) * np.sqrt(intervals_per_day) * np.sqrt(252),
                'vol_90th_percentile': np.percentile(daily_volatilities, 90) * np.sqrt(intervals_per_day) * np.sqrt(252),
            }
            
            return results
            
    except Exception as e:
        logger.error(f"Error in volatility calculation: {e}")
        return None

def print_volatility_analysis(results):
    """Print formatted volatility analysis results"""
    
    if not results:
        print("No volatility results to display")
        return
    
    print("SPX REALIZED VOLATILITY ANALYSIS")
    print("=" * 80)
    print(f"Date Range: {results['date_range']}")
    print(f"Time Window: {results['time_window']}")
    print(f"Total 10-second returns analyzed: {results['total_returns']:,}")
    print(f"Trading days: {results['trading_days']}")
    print()
    
    print("ANNUALIZED VOLATILITY ESTIMATES:")
    print("-" * 40)
    print(f"Full day volatility:      {results['annualized_vol_full']:.1%}")
    print(f"Daily average method:     {results['annualized_vol_daily']:.1%}")
    print(f"End-of-day specific:      {results['eod_specific_vol']:.1%}")
    print()
    
    print("VOLATILITY PERCENTILES (for different market conditions):")
    print("-" * 40)
    print(f"25th percentile (calm):   {results['vol_25th_percentile']:.1%}")
    print(f"50th percentile (normal): {results['vol_50th_percentile']:.1%}")
    print(f"75th percentile (active): {results['vol_75th_percentile']:.1%}")
    print(f"90th percentile (stress): {results['vol_90th_percentile']:.1%}")
    print()
    
    print("RETURN STATISTICS:")
    print("-" * 40)
    print(f"Mean 10-second return:    {results['mean_return']:.6f}")
    print(f"Std 10-second return:     {results['std_return']:.6f}")
    print(f"Min return:               {results['min_return']:.4f}")
    print(f"Max return:               {results['max_return']:.4f}")
    print()
    
    print("RECOMMENDED VOLATILITY INPUTS FOR OPTIONS PRICING:")
    print("-" * 40)
    # Convert to decimal format for options pricing
    normal_vol = results['vol_50th_percentile']
    active_vol = results['vol_75th_percentile']
    stress_vol = results['vol_90th_percentile']
    
    print(f"Normal market conditions: {normal_vol:.3f} ({normal_vol:.1%})")
    print(f"Active market conditions: {active_vol:.3f} ({active_vol:.1%})")
    print(f"Stress market conditions: {stress_vol:.3f} ({stress_vol:.1%})")
    print()
    
    print("COMPARISON TO CURRENT MODEL:")
    print("-" * 40)
    current_vol = 0.20
    print(f"Current model uses:       {current_vol:.3f} ({current_vol:.1%})")
    print(f"Recommended normal:       {normal_vol:.3f} ({normal_vol:.1%})")
    print(f"Difference:               {(normal_vol - current_vol):.3f} ({(normal_vol/current_vol - 1):.1%})")

def test_volatility_impact_on_pricing(volatility_results):
    """Test how the calculated volatility affects iron butterfly pricing"""
    
    if not volatility_results:
        return
    
    print("\n" + "=" * 80)
    print("IMPACT ON IRON BUTTERFLY PRICING")
    print("=" * 80)
    
    from options_pricer import price_iron_butterfly
    
    # Test scenarios
    vol_scenarios = {
        'Current Model (20%)': 0.20,
        'Realized Normal': volatility_results['vol_50th_percentile'],
        'Realized Active': volatility_results['vol_75th_percentile'],
        'Realized Stress': volatility_results['vol_90th_percentile']
    }
    
    print("SPX @ 6000, Center Strike 6000, 8.5 minutes to expiry")
    print("-" * 50)
    print("Scenario                | Volatility | Credit  | Above $3.40?")
    print("-" * 50)
    
    for scenario, vol in vol_scenarios.items():
        credit = abs(price_iron_butterfly(S=6000, center_strike=6000, T=8.5, sigma=vol))
        meets_min = credit >= 3.40
        
        print(f"{scenario:22} | {vol:9.1%} | ${credit:6.2f} | {'YES' if meets_min else 'NO'}")

if __name__ == "__main__":
    print("Calculating realized volatility from SPX database...")
    
    # Calculate volatility for the full backtest period
    results = calculate_intraday_volatility(
        start_date='2025-01-01',
        end_date='2025-06-18',
        time_window='15:50-16:00'
    )
    
    if results:
        print_volatility_analysis(results)
        test_volatility_impact_on_pricing(results)
    else:
        print("Failed to calculate volatility") 