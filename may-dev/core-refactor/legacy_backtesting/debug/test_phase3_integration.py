#!/usr/bin/env python3
"""
Test script for Phase 3: Extra Butterfly Integration
Tests the integration of extra butterfly mode into the historical backtester
"""

import os
import sys
import logging
from datetime import datetime

# Add the backtesting directory to the path
sys.path.append('backtesting')

from historical_backtester import HistoricalBacktester

def test_phase3_integration():
    """Test the integration of extra butterfly mode"""
    print("🧪 Testing Phase 3: Extra Butterfly Integration")
    print("=" * 60)
    
    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    try:
        # Test 1: Create backtester with extra butterfly enabled
        print("\n1️⃣ Testing backtester creation with extra butterfly mode...")
        backtester = HistoricalBacktester(
            start_date="2025-01-02",
            end_date="2025-01-02",
            extra_butterfly=True
        )
        
        print("✅ Backtester created successfully with extra butterfly mode")
        print(f"   Extra butterfly enabled: {backtester.extra_butterfly}")
        print(f"   Extra butterfly engine: {backtester.extra_butterfly_engine is not None}")
        
        # Test 2: Check that extra butterfly fields are added to BacktestResult
        print("\n2️⃣ Testing BacktestResult extra butterfly fields...")
        from historical_backtester import BacktestResult
        
        # Create a sample result to check fields
        sample_result = BacktestResult(
            trading_date="2025-01-02",
            decision="bull",
            target_price=5870.0,
            closing_price=5875.0,
            reasoning="Test",
            bias=1,
            spans={},
            strikes=[5870, 5875],
            option_types=["call", "call"],
            actions=["buy", "sell"],
            close_percentages=[0.5, 0.5],
            bars=[],
            execution_price=5870.0,
            original_strikes=[5870, 5875],
            final_strikes=[5870, 5875],
            spread_price=2.20,
            strikes_moved=False,
            current_balance=10000.0,
            max_risk_amount=700.0,
            risk_per_contract=2.20,
            num_contracts=1,
            actual_risk_amount=2.20,
            trade_successful=True,
            profit_loss=100.0,
            new_balance=10100.0,
            narrow_fill_ohlc=[],
            narrow_fill_consolidated_ohlc=(0.0, 0.0, 0.0, 0.0)
        )
        
        print("✅ BacktestResult created successfully")
        print(f"   Extra butterfly result: {sample_result.extra_butterfly_result}")
        print(f"   Extra butterfly trade successful: {sample_result.extra_butterfly_trade_successful}")
        print(f"   Extra butterfly profit/loss: {sample_result.extra_butterfly_profit_loss}")
        print(f"   Extra butterfly filled: {sample_result.extra_butterfly_filled}")
        
        # Test 3: Test CLI argument parsing (simulate)
        print("\n3️⃣ Testing CLI argument parsing...")
        import argparse
        
        parser = argparse.ArgumentParser()
        parser.add_argument('--extra-butterfly', action='store_true', help='Enable extra butterfly mode')
        
        # Test with extra butterfly enabled
        test_args = parser.parse_args(['--extra-butterfly'])
        print(f"✅ CLI argument parsing: extra_butterfly={test_args.extra_butterfly}")
        
        # Test 4: Test database method availability
        print("\n4️⃣ Testing database method availability...")
        if hasattr(backtester.db_connector, 'get_spx_price_at_time'):
            print("✅ Database method get_spx_price_at_time is available")
        else:
            print("❌ Database method get_spx_price_at_time is missing")
            return False
        
        print("\n🎉 Phase 3 Integration Test Completed Successfully!")
        print("\n📋 Summary:")
        print("   ✅ Extra butterfly mode can be enabled")
        print("   ✅ Extra butterfly engine is initialized")
        print("   ✅ BacktestResult includes extra butterfly fields")
        print("   ✅ CLI argument parsing works")
        print("   ✅ Database method is available")
        
        return True
        
    except Exception as e:
        print(f"❌ Phase 3 Integration Test Failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_extra_butterfly_logic():
    """Test the extra butterfly logic with sample data"""
    print("\n🧪 Testing Extra Butterfly Logic with Sample Data")
    print("=" * 60)
    
    try:
        # Create backtester with extra butterfly enabled
        backtester = HistoricalBacktester(
            start_date="2025-01-02",
            end_date="2025-01-02",
            extra_butterfly=True
        )
        
        # Test the extra butterfly engine directly
        if backtester.extra_butterfly_engine:
            print("✅ Extra butterfly engine is available")
            
            # Test with sample data
            result = backtester.extra_butterfly_engine.process_extra_butterfly(
                original_target=5870.0,
                spx_1530=5870.0,
                spx_1556=5875.0,
                strategy_type="bull"
            )
            
            if result:
                print(f"✅ Extra butterfly processing successful")
                print(f"   Case: {result.case}")
                print(f"   Should trade: {result.should_trade}")
                print(f"   Strategy type: {result.strategy_type}")
                print(f"   Strikes: {result.strikes}")
                print(f"   New target: {result.new_target}")
            else:
                print("❌ Extra butterfly processing returned None")
                return False
        else:
            print("❌ Extra butterfly engine is not available")
            return False
        
        print("\n🎉 Extra Butterfly Logic Test Completed Successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Extra Butterfly Logic Test Failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Phase 3 Integration Tests")
    print("=" * 60)
    
    # Test 1: Basic integration
    test1_passed = test_phase3_integration()
    
    # Test 2: Extra butterfly logic
    test2_passed = test_extra_butterfly_logic()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"Phase 3 Integration Test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Extra Butterfly Logic Test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED! Phase 3 integration is ready.")
        print("\n📝 Next steps:")
        print("   1. Run a full backtest with --extra-butterfly flag")
        print("   2. Verify extra butterfly trades are being processed")
        print("   3. Check that P&L calculations include extra butterfly results")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        sys.exit(1) 