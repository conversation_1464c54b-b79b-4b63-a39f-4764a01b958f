#!/usr/bin/env python3
"""
Test script to verify target price validation logic for narrow decisions
"""

import sys
import os

# Add the current directory to path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from combo_orders import ComboOrderFactory

def test_target_price_validation():
    """Test the target price validation logic"""
    print("🧪 Testing Target Price Validation Logic")
    print("=" * 50)
    
    # Test Case 1: narrow_bull with target ≤ execution (should move strikes up)
    print("\n📊 Test Case 1: narrow_bull with target ≤ execution")
    print("-" * 45)
    
    atm_strike = 5000.0
    execution_price = 5002.0  # Execution price above ATM
    
    # Without validation (old behavior)
    combo_old = ComboOrderFactory.create_one_better_narrow_bull(atm_strike)
    buy_strike_old = combo_old.legs[0].strike  # Buy strike
    sell_strike_old = combo_old.legs[1].strike  # Sell strike (target)
    
    print(f"Old behavior:")
    print(f"  Buy strike: {buy_strike_old}")
    print(f"  Sell strike (target): {sell_strike_old}")
    print(f"  Execution price: {execution_price}")
    print(f"  Target ≤ execution? {sell_strike_old <= execution_price} ❌")
    
    # With validation (new behavior)
    combo_new = ComboOrderFactory.create_one_better_narrow_bull(atm_strike, execution_price)
    buy_strike_new = combo_new.legs[0].strike  # Buy strike
    sell_strike_new = combo_new.legs[1].strike  # Sell strike (target)
    
    print(f"\nNew behavior (with validation):")
    print(f"  Buy strike: {buy_strike_new}")
    print(f"  Sell strike (target): {sell_strike_new}")
    print(f"  Execution price: {execution_price}")
    print(f"  Target > execution? {sell_strike_new > execution_price} ✅")
    
    # Verify strikes were adjusted
    assert sell_strike_new > execution_price, f"Target {sell_strike_new} should be > execution {execution_price}"
    assert buy_strike_new > buy_strike_old, f"Buy strike should have moved up: {buy_strike_new} > {buy_strike_old}"
    print("✅ PASSED: Strikes adjusted correctly for narrow_bull")
    
    # Test Case 2: narrow_bear with target ≥ execution (should move strikes down)
    print("\n📊 Test Case 2: narrow_bear with target ≥ execution")
    print("-" * 45)
    
    atm_strike = 5000.0
    execution_price = 4998.0  # Execution price below ATM
    
    # Without validation (old behavior)
    combo_old = ComboOrderFactory.create_one_better_narrow_bear(atm_strike)
    buy_strike_old = combo_old.legs[0].strike  # Buy strike
    sell_strike_old = combo_old.legs[1].strike  # Sell strike (target)
    
    print(f"Old behavior:")
    print(f"  Buy strike: {buy_strike_old}")
    print(f"  Sell strike (target): {sell_strike_old}")
    print(f"  Execution price: {execution_price}")
    print(f"  Target ≥ execution? {sell_strike_old >= execution_price} ❌")
    
    # With validation (new behavior)
    combo_new = ComboOrderFactory.create_one_better_narrow_bear(atm_strike, execution_price)
    buy_strike_new = combo_new.legs[0].strike  # Buy strike
    sell_strike_new = combo_new.legs[1].strike  # Sell strike (target)
    
    print(f"\nNew behavior (with validation):")
    print(f"  Buy strike: {buy_strike_new}")
    print(f"  Sell strike (target): {sell_strike_new}")
    print(f"  Execution price: {execution_price}")
    print(f"  Target < execution? {sell_strike_new < execution_price} ✅")
    
    # Verify strikes were adjusted
    assert sell_strike_new < execution_price, f"Target {sell_strike_new} should be < execution {execution_price}"
    assert buy_strike_new < buy_strike_old, f"Buy strike should have moved down: {buy_strike_new} < {buy_strike_old}"
    print("✅ PASSED: Strikes adjusted correctly for narrow_bear")
    
    # Test Case 3: narrow_bull with good target (no adjustment needed)
    print("\n📊 Test Case 3: narrow_bull with good target (no adjustment)")
    print("-" * 50)
    
    atm_strike = 5000.0
    execution_price = 4998.0  # Execution price below ATM (good for bull)
    
    combo = ComboOrderFactory.create_one_better_narrow_bull(atm_strike, execution_price)
    buy_strike = combo.legs[0].strike
    sell_strike = combo.legs[1].strike
    
    print(f"  Buy strike: {buy_strike}")
    print(f"  Sell strike (target): {sell_strike}")
    print(f"  Execution price: {execution_price}")
    print(f"  Target > execution? {sell_strike > execution_price} ✅")
    
    # Should use original strikes (no adjustment needed)
    expected_buy = atm_strike - 5  # ATM - 5
    expected_sell = atm_strike     # ATM
    assert buy_strike == expected_buy, f"Expected buy strike {expected_buy}, got {buy_strike}"
    assert sell_strike == expected_sell, f"Expected sell strike {expected_sell}, got {sell_strike}"
    print("✅ PASSED: No adjustment needed for good narrow_bull target")
    
    # Test Case 4: narrow_bear with good target (no adjustment needed)
    print("\n📊 Test Case 4: narrow_bear with good target (no adjustment)")
    print("-" * 50)
    
    atm_strike = 5000.0
    execution_price = 5002.0  # Execution price above ATM (good for bear)
    
    combo = ComboOrderFactory.create_one_better_narrow_bear(atm_strike, execution_price)
    buy_strike = combo.legs[0].strike
    sell_strike = combo.legs[1].strike
    
    print(f"  Buy strike: {buy_strike}")
    print(f"  Sell strike (target): {sell_strike}")
    print(f"  Execution price: {execution_price}")
    print(f"  Target < execution? {sell_strike < execution_price} ✅")
    
    # Should use original strikes (no adjustment needed)
    expected_buy = atm_strike + 5  # ATM + 5
    expected_sell = atm_strike     # ATM
    assert buy_strike == expected_buy, f"Expected buy strike {expected_buy}, got {buy_strike}"
    assert sell_strike == expected_sell, f"Expected sell strike {expected_sell}, got {sell_strike}"
    print("✅ PASSED: No adjustment needed for good narrow_bear target")
    
    # Test Case 5: Real example from user (6/17 narrow_bear)
    print("\n📊 Test Case 5: Real example - 6/17 narrow_bear")
    print("-" * 40)
    
    # From user's example: narrow_bear, execution_price=5988, target_price=5990 (higher in bear decision)
    atm_strike = 5990.0  # Target was 5990 (sell strike)
    execution_price = 5988.0  # Execution price
    
    combo = ComboOrderFactory.create_one_better_narrow_bear(atm_strike, execution_price)
    buy_strike = combo.legs[0].strike
    sell_strike = combo.legs[1].strike
    
    print(f"  Original ATM strike: {atm_strike}")
    print(f"  Execution price: {execution_price}")
    print(f"  Adjusted buy strike: {buy_strike}")
    print(f"  Adjusted sell strike (target): {sell_strike}")
    print(f"  Target < execution? {sell_strike < execution_price} ✅")
    
    # Verify the adjustment
    assert sell_strike < execution_price, f"Target {sell_strike} should be < execution {execution_price}"
    assert buy_strike < atm_strike + 5, f"Buy strike should have moved down from {atm_strike + 5} to {buy_strike}"
    print("✅ PASSED: Real example handled correctly")
    
    print("\n🎉 All target price validation tests passed!")
    print("\nSummary:")
    print("- narrow_bull: target must be > execution_price, move strikes up if needed")
    print("- narrow_bear: target must be < execution_price, move strikes down if needed")
    print("- Live trading now handles target price validation correctly")

if __name__ == "__main__":
    test_target_price_validation() 