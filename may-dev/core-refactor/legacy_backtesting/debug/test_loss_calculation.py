#!/usr/bin/env python3
"""
Test script to verify loss calculation logic and identify the 99.2% average loss issue
"""

def test_loss_calculation():
    """Test the loss calculation logic"""
    print("🧪 Testing Loss Calculation Logic")
    print("=" * 50)
    
    # Test Case 1: Bull spread loss
    print("\n📊 Test Case 1: Bull Spread Loss")
    print("-" * 30)
    
    # Scenario: Bull spread that loses completely
    spread_price = 2.20  # Debit paid per contract
    num_contracts = 3
    profit_loss = -660.0  # Total loss: -$660 (3 contracts * $2.20 * 100)
    actual_risk_amount = 660.0  # 3 contracts * $2.20 * 100
    
    # Current calculation
    loss_pct = abs(profit_loss / actual_risk_amount) * 100
    print(f"Spread Price: ${spread_price:.2f}")
    print(f"Number of Contracts: {num_contracts}")
    print(f"Total P&L: ${profit_loss:.2f}")
    print(f"Actual Risk Amount: ${actual_risk_amount:.2f}")
    print(f"Loss Percentage: {loss_pct:.1f}%")
    
    # Expected: Should be 100% loss (lost entire debit paid)
    expected_loss_pct = 100.0
    print(f"Expected Loss Percentage: {expected_loss_pct:.1f}%")
    print(f"Calculation Correct: {'✅' if abs(loss_pct - expected_loss_pct) < 0.1 else '❌'}")
    
    # Test Case 2: Iron Butterfly loss
    print("\n📊 Test Case 2: Iron Butterfly Loss")
    print("-" * 30)
    
    # Scenario: Iron butterfly that loses completely
    credit_received = 3.40  # Credit received per contract
    strike_width = 5.0  # 5-point wings
    num_contracts = 2
    max_loss_per_contract = (strike_width - credit_received) * 100  # (5.0 - 3.40) * 100 = 160
    profit_loss = -320.0  # Total loss: -$320 (2 contracts * $160)
    actual_risk_amount = 320.0  # 2 contracts * $160
    
    # Current calculation
    loss_pct = abs(profit_loss / actual_risk_amount) * 100
    print(f"Credit Received: ${credit_received:.2f}")
    print(f"Strike Width: {strike_width:.1f}")
    print(f"Max Loss per Contract: ${max_loss_per_contract:.2f}")
    print(f"Number of Contracts: {num_contracts}")
    print(f"Total P&L: ${profit_loss:.2f}")
    print(f"Actual Risk Amount: ${actual_risk_amount:.2f}")
    print(f"Loss Percentage: {loss_pct:.1f}%")
    
    # Expected: Should be 100% loss (lost entire max risk)
    expected_loss_pct = 100.0
    print(f"Expected Loss Percentage: {expected_loss_pct:.1f}%")
    print(f"Calculation Correct: {'✅' if abs(loss_pct - expected_loss_pct) < 0.1 else '❌'}")
    
    # Test Case 3: Partial loss scenario
    print("\n📊 Test Case 3: Partial Loss Scenario")
    print("-" * 30)
    
    # Scenario: Bull spread with partial loss
    spread_price = 2.20  # Debit paid per contract
    num_contracts = 1
    profit_loss = -110.0  # Partial loss: -$110 (lost half the debit)
    actual_risk_amount = 220.0  # 1 contract * $2.20 * 100
    
    # Current calculation
    loss_pct = abs(profit_loss / actual_risk_amount) * 100
    print(f"Spread Price: ${spread_price:.2f}")
    print(f"Number of Contracts: {num_contracts}")
    print(f"Total P&L: ${profit_loss:.2f}")
    print(f"Actual Risk Amount: ${actual_risk_amount:.2f}")
    print(f"Loss Percentage: {loss_pct:.1f}%")
    
    # Expected: Should be 50% loss (lost half the debit)
    expected_loss_pct = 50.0
    print(f"Expected Loss Percentage: {expected_loss_pct:.1f}%")
    print(f"Calculation Correct: {'✅' if abs(loss_pct - expected_loss_pct) < 0.1 else '❌'}")
    
    # Test Case 4: Investigate the 99.2% issue
    print("\n📊 Test Case 4: Investigating 99.2% Issue")
    print("-" * 30)
    
    # Let's see what would cause a 99.2% loss
    # If actual_risk_amount is slightly higher than the actual loss, we get 99.2%
    actual_loss = 100.0
    actual_risk_amount_99_2 = actual_loss / 0.992  # To get 99.2% loss
    calculated_loss_pct = (actual_loss / actual_risk_amount_99_2) * 100
    
    print(f"Actual Loss: ${actual_loss:.2f}")
    print(f"Actual Risk Amount (for 99.2%): ${actual_risk_amount_99_2:.2f}")
    print(f"Calculated Loss Percentage: {calculated_loss_pct:.1f}%")
    
    # The difference
    difference = actual_risk_amount_99_2 - actual_loss
    print(f"Difference: ${difference:.2f}")
    print(f"This suggests the actual_risk_amount is ${difference:.2f} higher than the actual loss")
    
    print("\n🔍 Analysis:")
    print("The 99.2% average loss suggests that actual_risk_amount is consistently")
    print("slightly higher than the actual loss amount. This could be due to:")
    print("1. Rounding errors in position sizing")
    print("2. Incorrect risk_per_contract calculation")
    print("3. Fees or slippage not being accounted for")
    print("4. The actual_risk_amount being calculated as max_risk_amount instead of actual risk taken")

if __name__ == "__main__":
    test_loss_calculation() 