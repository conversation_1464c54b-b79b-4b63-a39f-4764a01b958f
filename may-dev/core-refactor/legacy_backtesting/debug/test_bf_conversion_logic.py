#!/usr/bin/env python3
"""
Test script to verify BF-Bull/BF-Bear conversion logic
"""

import sys
import os
import datetime as dt

# Add the current directory to path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_state import StrategyState, DecisionType, BarData, SpanData
from config import TradingConfig
from spx_strategy import SPXTradingStrategy
from quote_engine import OptionQuoteEngine
from execution_engine import OptionExecutionEngine

def create_mock_components():
    """Create mock components for testing"""
    class MockIB:
        def sleep(self, seconds):
            pass
    
    class MockQuoteEngine:
        def get_combo_quote(self, combo_def):
            return None
    
    class MockExecutionEngine:
        def execute_bull_spread(self, price, timeout_seconds=None):
            return None
        def execute_bear_spread(self, price, timeout_seconds=None):
            return None
    
    return MockQuoteEngine(), MockExecutionEngine()

def test_bf_conversion_logic():
    """Test the BF conversion logic"""
    print("🧪 Testing BF-Bull/BF-Bear Conversion Logic")
    print("=" * 60)
    
    # Create configuration
    config = TradingConfig()
    
    # Create mock components
    quote_engine, execution_engine = create_mock_components()
    
    # Create strategy
    strategy = SPXTradingStrategy(quote_engine, execution_engine)
    
    # Test Case 1: First bar close > 66% but no breakout in bars 2-3
    print("\n📊 Test Case 1: First bar close > 66%, no breakout")
    print("-" * 40)
    
    # Create test data
    strategy.state.reset()
    strategy.state.session_start_time = dt.datetime.now()
    
    # First bar: close at 75% of SPAN1 (should trigger BF-Bull conversion in third bar)
    first_bar = BarData(
        timestamp=dt.datetime.now(),
        open=5000.0,
        high=5010.0,
        low=4990.0,
        close=5007.5,  # 75% of range
        name="first"
    )
    span1 = SpanData("SPAN1", 5010.0, 4990.0, ["first"])
    
    # Second bar: no breakout
    second_bar = BarData(
        timestamp=dt.datetime.now(),
        open=5007.5,
        high=5012.0,
        low=5005.0,
        close=5008.0,
        name="second"
    )
    span2 = SpanData("SPAN2", 5012.0, 4990.0, ["first", "second"])
    
    # Third bar: no breakout
    third_bar = BarData(
        timestamp=dt.datetime.now(),
        open=5008.0,
        high=5013.0,
        low=5006.0,
        close=5009.0,
        name="third"
    )
    span3 = SpanData("SPAN3", 5013.0, 4990.0, ["first", "second", "third"])
    
    # Set up the data
    strategy.state.set_bar_data(1, first_bar)
    strategy.state.set_bar_data(2, second_bar)
    strategy.state.set_bar_data(3, third_bar)
    strategy.state.span1 = span1
    strategy.state.span2 = span2
    strategy.state.span3 = span3
    
    # Analyze first bar
    strategy._analyze_first_bar()
    print(f"After first bar: {strategy.state.current_decision.value}")
    print(f"Expected: iron_butterfly")
    assert strategy.state.current_decision == DecisionType.IRON_BUTTERFLY, f"Expected IRON_BUTTERFLY, got {strategy.state.current_decision.value}"
    
    # Analyze second bar
    strategy._analyze_second_bar()
    print(f"After second bar: {strategy.state.current_decision.value}")
    print(f"Expected: iron_butterfly (no breakout)")
    assert strategy.state.current_decision == DecisionType.IRON_BUTTERFLY, f"Expected IRON_BUTTERFLY, got {strategy.state.current_decision.value}"
    
    # Analyze third bar
    strategy._analyze_third_bar()
    print(f"After third bar: {strategy.state.current_decision.value}")
    print(f"Expected: bf_bull (first bar close > 66%)")
    assert strategy.state.current_decision == DecisionType.BF_BULL, f"Expected BF_BULL, got {strategy.state.current_decision.value}"
    print("✅ Test Case 1 PASSED")
    
    # Test Case 2: First bar close ≤ 66% but no breakout in bars 2-3
    print("\n📊 Test Case 2: First bar close ≤ 66%, no breakout")
    print("-" * 40)
    
    # Reset and create new test data
    strategy.state.reset()
    strategy.state.session_start_time = dt.datetime.now()
    
    # First bar: close at 45% of SPAN1 (should trigger BF-Bear conversion in third bar)
    first_bar = BarData(
        timestamp=dt.datetime.now(),
        open=5000.0,
        high=5010.0,
        low=4990.0,
        close=5001.0,  # 45% of range
        name="first"
    )
    
    # Set up the data
    strategy.state.set_bar_data(1, first_bar)
    strategy.state.set_bar_data(2, second_bar)
    strategy.state.set_bar_data(3, third_bar)
    strategy.state.span1 = span1
    strategy.state.span2 = span2
    strategy.state.span3 = span3
    
    # Analyze first bar
    strategy._analyze_first_bar()
    print(f"After first bar: {strategy.state.current_decision.value}")
    print(f"Expected: iron_butterfly")
    assert strategy.state.current_decision == DecisionType.IRON_BUTTERFLY, f"Expected IRON_BUTTERFLY, got {strategy.state.current_decision.value}"
    
    # Analyze second bar
    strategy._analyze_second_bar()
    print(f"After second bar: {strategy.state.current_decision.value}")
    print(f"Expected: iron_butterfly (no breakout)")
    assert strategy.state.current_decision == DecisionType.IRON_BUTTERFLY, f"Expected IRON_BUTTERFLY, got {strategy.state.current_decision.value}"
    
    # Analyze third bar
    strategy._analyze_third_bar()
    print(f"After third bar: {strategy.state.current_decision.value}")
    print(f"Expected: bf_bear (first bar close ≤ 66%)")
    assert strategy.state.current_decision == DecisionType.BF_BEAR, f"Expected BF_BEAR, got {strategy.state.current_decision.value}"
    print("✅ Test Case 2 PASSED")
    
    # Test Case 3: Breakout in second bar should override BF conversion
    print("\n📊 Test Case 3: Breakout in second bar overrides BF conversion")
    print("-" * 40)
    
    # Reset and create new test data
    strategy.state.reset()
    strategy.state.session_start_time = dt.datetime.now()
    
    # First bar: close at 75% of SPAN1 (would trigger BF-Bull if no breakout)
    first_bar = BarData(
        timestamp=dt.datetime.now(),
        open=5000.0,
        high=5010.0,
        low=4990.0,
        close=5007.5,  # 75% of range
        name="first"
    )
    
    # Second bar: bull breakout (high above first bar high, close > 88% of SPAN2)
    second_bar_breakout = BarData(
        timestamp=dt.datetime.now(),
        open=5007.5,
        high=5015.0,  # Above first bar high (5010)
        low=5005.0,
        close=5013.0,  # > 88% of SPAN2 range
        name="second"
    )
    span2_breakout = SpanData("SPAN2", 5015.0, 4990.0, ["first", "second"])
    
    # Set up the data
    strategy.state.set_bar_data(1, first_bar)
    strategy.state.set_bar_data(2, second_bar_breakout)
    strategy.state.set_bar_data(3, third_bar)
    strategy.state.span1 = span1
    strategy.state.span2 = span2_breakout
    strategy.state.span3 = span3
    
    # Analyze first bar
    strategy._analyze_first_bar()
    print(f"After first bar: {strategy.state.current_decision.value}")
    print(f"Expected: iron_butterfly")
    assert strategy.state.current_decision == DecisionType.IRON_BUTTERFLY, f"Expected IRON_BUTTERFLY, got {strategy.state.current_decision.value}"
    
    # Analyze second bar (should detect breakout)
    strategy._analyze_second_bar()
    print(f"After second bar: {strategy.state.current_decision.value}")
    print(f"Expected: bull (breakout detected)")
    assert strategy.state.current_decision == DecisionType.BULL, f"Expected BULL, got {strategy.state.current_decision.value}"
    
    # Analyze third bar (should remain bull, no BF conversion)
    strategy._analyze_third_bar()
    print(f"After third bar: {strategy.state.current_decision.value}")
    print(f"Expected: bull (breakout takes precedence)")
    assert strategy.state.current_decision == DecisionType.BULL, f"Expected BULL, got {strategy.state.current_decision.value}"
    print("✅ Test Case 3 PASSED")
    
    print("\n🎉 All tests passed! BF conversion logic is working correctly.")
    print("\nSummary of changes:")
    print("- BF conversion moved from first bar to third bar analysis")
    print("- Breakout conditions take precedence over BF conversion")
    print("- BF-Bull/BF-Bear are treated the same as BULL/BEAR for narrow conversions")

if __name__ == "__main__":
    test_bf_conversion_logic() 