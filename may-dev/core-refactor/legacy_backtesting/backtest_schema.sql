-- SQL Server Schema for Backtesting Results
-- Designed for frontend display and analysis


-- Table to store backtest run parameters and summary statistics
CREATE TABLE [IBDB_DEV].[dbo].[backtest_runs] (
    run_id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    run_timestamp DATETIME2 NOT NULL DEFAULT GETDATE(),
    run_name NVARCHAR(255) NULL, -- Optional user-friendly name
    
    -- Date Range
    start_date DATE NULL,
    end_date DATE NULL,
    
    -- Pricing Configuration
    pricing_mode NVARCHAR(20) NOT NULL CHECK (pricing_mode IN ('fixed', 'blackscholes', 'simple')),
    volatility DECIMAL(5,4) NOT NULL DEFAULT 0.45, -- e.g., 0.4500 for 45%
    
    -- Fixed Mode Parameters (only used when pricing_mode = 'fixed')
    fixed_spread_cost DECIMAL(4,2) NULL, -- e.g., 2.20
    fixed_butterfly_credit DECIMAL(4,2) NULL, -- e.g., 3.50
    fixed_itm_threshold DECIMAL(4,2) NULL, -- e.g., 2.50
    
    -- Risk Management
    starting_balance DECIMAL(12,2) NOT NULL DEFAULT 10000.00,
    max_risk_per_trade_pct DECIMAL(5,4) NOT NULL DEFAULT 0.07, -- e.g., 0.0700 for 7%
    
    -- Execution Options
    skip_monday BIT NOT NULL DEFAULT 0,
    
    -- Summary Statistics
    total_trades INT NOT NULL DEFAULT 0,
    winning_trades INT NOT NULL DEFAULT 0,
    losing_trades INT NOT NULL DEFAULT 0,
    win_rate_pct DECIMAL(5,2) NULL, -- e.g., 62.50 for 62.5%
    
    -- Performance Metrics
    ending_balance DECIMAL(12,2) NULL,
    total_pnl DECIMAL(12,2) NULL,
    total_return_pct DECIMAL(7,2) NULL, -- e.g., 14.50 for 14.5%
    max_drawdown DECIMAL(12,2) NULL,
    max_drawdown_pct DECIMAL(5,2) NULL,
    
    -- Trade Statistics
    avg_pnl_per_trade DECIMAL(8,2) NULL,
    avg_win_amount DECIMAL(8,2) NULL,
    avg_loss_amount DECIMAL(8,2) NULL,
    avg_win_pct DECIMAL(5,2) NULL,
    avg_loss_pct DECIMAL(5,2) NULL,
    
    -- Streaks
    max_win_streak INT NULL,
    max_lose_streak INT NULL,
    
    -- Position Sizing
    avg_contracts_per_trade DECIMAL(6,2) NULL,
    avg_risk_amount_per_trade DECIMAL(8,2) NULL,
    avg_risk_pct_per_trade DECIMAL(5,2) NULL,
    
    -- Decision Breakdown (JSON for flexibility)
    decision_counts NVARCHAR(MAX) NULL, -- JSON: {"bull": 15, "bear": 12, "iron_butterfly": 18}
    decision_win_rates NVARCHAR(MAX) NULL, -- JSON: {"bull": 65.5, "bear": 58.3, "iron_butterfly": 72.2}
    
    -- Metadata
    processing_time_seconds DECIMAL(8,2) NULL,
    created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    
    -- Indexes for common queries
    INDEX IX_backtest_runs_timestamp (run_timestamp DESC),
    INDEX IX_backtest_runs_pricing_mode (pricing_mode),
    INDEX IX_backtest_runs_date_range (start_date, end_date),
    INDEX IX_backtest_runs_performance (total_return_pct DESC, win_rate_pct DESC)
);

-- Table to store individual trade results
CREATE TABLE [IBDB_DEV].[dbo].[backtest_trade_results] (
    result_id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    run_id UNIQUEIDENTIFIER NOT NULL REFERENCES [IBDB_DEV].[dbo].[backtest_runs](run_id) ON DELETE CASCADE,
    
    -- Trade Identification
    trading_date DATE NOT NULL,
    trade_sequence INT NOT NULL, -- Order within the backtest run
    
    -- Strategy Decision
    decision NVARCHAR(20) NOT NULL, -- 'bull', 'bear', 'iron_butterfly', 'narrow_bull', 'narrow_bear'
    reasoning NVARCHAR(MAX) NULL,
    bias INT NULL CHECK (bias IN (-1, 0, 1)), -- -1=down, 0=neutral, 1=up
    
    -- Market Data
    execution_price DECIMAL(8,2) NOT NULL, -- SPX price at execution (bar 3 close)
    closing_price DECIMAL(8,2) NOT NULL, -- SPX closing price
    target_price DECIMAL(8,2) NOT NULL,
    
    -- Strike Information
    original_strikes NVARCHAR(100) NULL, -- JSON array: [6045, 6050, 6050, 6055]
    final_strikes NVARCHAR(100) NULL, -- JSON array: [6045, 6050, 6050, 6055]
    spread_price DECIMAL(6,2) NOT NULL, -- Price per contract (positive=debit, negative=credit)
    strikes_moved BIT NOT NULL DEFAULT 0,
    
    -- Position Sizing
    current_balance DECIMAL(12,2) NOT NULL, -- Balance before this trade
    max_risk_amount DECIMAL(8,2) NOT NULL, -- Max risk allowed for this trade
    risk_per_contract DECIMAL(6,2) NOT NULL, -- Risk per contract in dollars
    num_contracts INT NOT NULL, -- Number of contracts traded
    actual_risk_amount DECIMAL(8,2) NOT NULL, -- Actual risk taken
    
    -- Trade Outcome
    trade_successful BIT NOT NULL,
    profit_loss DECIMAL(8,2) NOT NULL, -- Total P&L for the position
    new_balance DECIMAL(12,2) NOT NULL, -- Balance after this trade
    
    -- Technical Analysis Data
    span1 DECIMAL(6,2) NULL,
    span2 DECIMAL(6,2) NULL,
    span3 DECIMAL(6,2) NULL,
    close_pct_bar1 DECIMAL(5,2) NULL,
    close_pct_bar2 DECIMAL(5,2) NULL,
    close_pct_bar3 DECIMAL(5,2) NULL,
    
    -- OHLC Bar Data (JSON for flexibility)
    bar1_ohlc NVARCHAR(100) NULL, -- JSON: [open, high, low, close]
    bar2_ohlc NVARCHAR(100) NULL,
    bar3_ohlc NVARCHAR(100) NULL,
    
    -- Option Details (JSON for flexibility)
    option_types NVARCHAR(50) NULL, -- JSON: ["P", "P", "C", "C"]
    actions NVARCHAR(50) NULL, -- JSON: ["BUY", "SELL", "SELL", "BUY"]
    
    -- Metadata
    processing_time_seconds DECIMAL(6,3) NULL,
    created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    
    -- Constraints
    UNIQUE (run_id, trading_date, trade_sequence), -- Allow multiple trades per day with different sequences
    
    -- Indexes for common queries
    INDEX IX_trade_results_run_date (run_id, trading_date),
    INDEX IX_trade_results_decision (decision),
    INDEX IX_trade_results_performance (trade_successful, profit_loss DESC),
    INDEX IX_trade_results_sequence (run_id, trade_sequence)
);

GO

-- View for easy frontend queries combining run parameters with trade results
CREATE VIEW [dbo].[v_backtest_results_with_params] AS
SELECT 
    tr.*,
    br.run_name,
    br.pricing_mode,
    br.volatility,
    br.fixed_spread_cost,
    br.fixed_butterfly_credit,
    br.starting_balance,
    br.max_risk_per_trade_pct,
    br.skip_monday,
    br.total_return_pct as run_total_return_pct,
    br.win_rate_pct as run_win_rate_pct
FROM [IBDB_DEV].[dbo].[backtest_trade_results] tr
INNER JOIN [IBDB_DEV].[dbo].[backtest_runs] br ON tr.run_id = br.run_id;

GO

-- View for run summaries with key metrics
CREATE VIEW [dbo].[v_backtest_run_summary] AS
SELECT 
    run_id,
    run_name,
    run_timestamp,
    CASE 
        WHEN pricing_mode = 'fixed' THEN CONCAT('Fixed (S:', fixed_spread_cost, ' B:', fixed_butterfly_credit, ')')
        WHEN pricing_mode = 'blackscholes' THEN CONCAT('Black-Scholes (Vol:', CAST(volatility*100 AS INT), '%)')
        ELSE pricing_mode
    END as pricing_description,
    CONCAT(CAST(max_risk_per_trade_pct*100 AS INT), '% Risk') as risk_description,
    starting_balance,
    ending_balance,
    total_return_pct,
    win_rate_pct,
    total_trades,
    max_drawdown,
    max_drawdown_pct,
    max_win_streak,
    max_lose_streak,
    DATEDIFF(day, start_date, end_date) + 1 as date_range_days,
    FORMAT(start_date, 'yyyy-MM-dd') + ' to ' + FORMAT(end_date, 'yyyy-MM-dd') as date_range_display
FROM [IBDB_DEV].[dbo].[backtest_runs]
WHERE total_trades > 0;

-- Example queries for frontend

-- Get all backtest runs sorted by performance
-- SELECT * FROM [IBDB_DEV].[dbo].[v_backtest_run_summary] ORDER BY total_return_pct DESC;

-- Get detailed results for a specific run
-- SELECT * FROM [IBDB_DEV].[dbo].[v_backtest_results_with_params] WHERE run_id = 'your-run-id' ORDER BY trading_date;

-- Compare different pricing modes
-- SELECT pricing_mode, AVG(total_return_pct) as avg_return, AVG(win_rate_pct) as avg_win_rate 
-- FROM [IBDB_DEV].[dbo].[backtest_runs] GROUP BY pricing_mode;

-- Get best performing parameter combinations
-- SELECT fixed_spread_cost, fixed_butterfly_credit, max_risk_per_trade_pct, 
--        AVG(total_return_pct) as avg_return, COUNT(*) as run_count
-- FROM [IBDB_DEV].[dbo].[backtest_runs] 
-- WHERE pricing_mode = 'fixed' 
-- GROUP BY fixed_spread_cost, fixed_butterfly_credit, max_risk_per_trade_pct
-- ORDER BY avg_return DESC; 