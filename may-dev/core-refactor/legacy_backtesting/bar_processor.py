#!/usr/bin/env python3
"""
Bar Processor for SPX Backtesting
Combines 10-second bars into 30-second bars and normalizes opens
"""

from typing import List, Dict, Any, Tuple
import logging

logger = logging.getLogger(__name__)


class BarProcessor:
    """Process and normalize bar data for backtesting"""
    
    @staticmethod
    def combine_10sec_to_30sec_bars(bars_10sec: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Combine 9 ten-second bars into 3 thirty-second bars
        
        Args:
            bars_10sec: List of 9 ten-second bar dictionaries
            
        Returns:
            List of 3 thirty-second bar dictionaries
        """
        if len(bars_10sec) != 9:
            raise ValueError(f"Expected exactly 9 ten-second bars, got {len(bars_10sec)}")
        
        # The user wants:
        # Bar 1: 15:50:00 (combining 15:50:00, 15:50:10, 15:50:20) - 3 bars
        # Bar 2: 15:50:30 (combining 15:50:30, 15:50:40, 15:50:50) - 3 bars
        # Bar 3: 15:51:00 (combining 15:51:00, 15:51:10, 15:51:20) - 3 bars
        
        bars_30sec = []
        
        # Bar 1: First 3 bars (15:50:00, 15:50:10, 15:50:20)
        group_bars = bars_10sec[0:3]
        combined_bar = {
            'date': group_bars[0]['date'],  # Use first bar's timestamp
            'open': group_bars[0]['open'],   # Open from first bar
            'high': max(bar['high'] for bar in group_bars),  # Highest high
            'low': min(bar['low'] for bar in group_bars),    # Lowest low
            'close': group_bars[-1]['close']  # Close from last bar
        }
        bars_30sec.append(combined_bar)
        
        # Bar 2: Next 3 bars (15:50:30, 15:50:40, 15:50:50)
        group_bars = bars_10sec[3:6]
        combined_bar = {
            'date': group_bars[0]['date'],  # Use first bar's timestamp
            'open': group_bars[0]['open'],   # Open from first bar
            'high': max(bar['high'] for bar in group_bars),  # Highest high
            'low': min(bar['low'] for bar in group_bars),    # Lowest low
            'close': group_bars[-1]['close']  # Close from last bar
        }
        bars_30sec.append(combined_bar)
        
        # Bar 3: Last 3 bars (15:51:00, 15:51:10, 15:51:20)
        group_bars = bars_10sec[6:9]
        combined_bar = {
            'date': group_bars[0]['date'],  # Use first bar's timestamp
            'open': group_bars[0]['open'],   # Open from first bar
            'high': max(bar['high'] for bar in group_bars),  # Highest high
            'low': min(bar['low'] for bar in group_bars),    # Lowest low
            'close': group_bars[-1]['close']  # Close from last bar
        }
        bars_30sec.append(combined_bar)
        
        return bars_30sec
    
    @staticmethod
    def adjust_bar_opens(bars_30sec: List[Dict[str, Any]], 
                        pre_bar_close: float) -> List[Tuple[float, float, float, float]]:
        """
        Adjust bar opens to create normalized linked bars and return as OHLC tuples
        
        Args:
            bars_30sec: List of 3 thirty-second bars
            pre_bar_close: Close price from 15:49:50 bar (to set first bar's open)
            
        Returns:
            List of 3 OHLC tuples: (open, high, low, close)
        """
        if len(bars_30sec) != 3:
            raise ValueError(f"Expected exactly 3 thirty-second bars, got {len(bars_30sec)}")
        
        adjusted_bars = []
        
        for i, bar in enumerate(bars_30sec):
            original_open = bar['open']
            original_high = bar['high']
            original_low = bar['low']
            original_close = bar['close']
            
            # Set the new open
            if i == 0:
                # First bar: open = pre_bar_close
                new_open = pre_bar_close
            else:
                # Subsequent bars: open = previous bar's close
                new_open = adjusted_bars[i-1][3]  # Previous close
            
            # Adjust high and low if necessary
            # If new open is outside the original range, expand the range
            new_high = max(original_high, new_open)
            new_low = min(original_low, new_open)
            
            # Close stays the same
            new_close = original_close
            
            # Ensure close is within the adjusted range
            new_high = max(new_high, new_close)
            new_low = min(new_low, new_close)
            
            adjusted_bar = (new_open, new_high, new_low, new_close)
            adjusted_bars.append(adjusted_bar)
            
            logger.debug(f"Bar {i+1}: Original OHLC=({original_open:.2f}, {original_high:.2f}, {original_low:.2f}, {original_close:.2f}) "
                        f"→ Adjusted OHLC=({new_open:.2f}, {new_high:.2f}, {new_low:.2f}, {new_close:.2f})")
        
        return adjusted_bars
    
    @staticmethod
    def process_daily_data(daily_data: Dict[str, Any]) -> Tuple[List[Tuple[float, float, float, float]], float]:
        """
        Process a day's raw data into normalized 30-second bars
        
        Args:
            daily_data: Dictionary with pre_bar, bars_10sec, and closing_price
            
        Returns:
            Tuple of (adjusted_bars, closing_price)
            adjusted_bars: List of 3 OHLC tuples
            closing_price: End-of-day closing price
        """
        
        # Step 1: Combine 10-second bars into 30-second bars
        bars_30sec = BarProcessor.combine_10sec_to_30sec_bars(daily_data['bars_10sec'])
        
        # Step 2: Adjust opens to create normalized bars
        pre_bar_close = daily_data['pre_bar']['close']
        adjusted_bars = BarProcessor.adjust_bar_opens(bars_30sec, pre_bar_close)
        
        # Step 3: Get closing price
        closing_price = daily_data['closing_price']
        
        logger.info(f"Processed daily data for {daily_data['trading_date']}")
        logger.info(f"Pre-bar close: {pre_bar_close:.2f}")
        logger.info(f"Adjusted bars: {adjusted_bars}")
        logger.info(f"Closing price: {closing_price:.2f}")
        
        return adjusted_bars, closing_price


if __name__ == "__main__":
    # Test the bar processor with sample data
    logging.basicConfig(level=logging.INFO)
    
    # Sample data structure (simulate what database would return)
    sample_bars_10sec = []
    base_price = 5870.0
    
    # Create 9 sample 10-second bars (3+3+3 for the three 30-second periods)
    for i in range(9):
        # Simulate some price movement
        open_price = base_price + i * 0.1
        high_price = open_price + 0.5
        low_price = open_price - 0.3
        close_price = open_price + 0.2
        
        bar = {
            'date': f'2025-01-02 15:50:{i*10:02d}.000',
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price
        }
        sample_bars_10sec.append(bar)
    
    sample_daily_data = {
        'trading_date': '2025-01-02',
        'pre_bar': {'close': 5868.5},
        'bars_10sec': sample_bars_10sec,
        'closing_price': 5875.0
    }
    
    # Process the data
    adjusted_bars, closing_price = BarProcessor.process_daily_data(sample_daily_data)
    
    print("\nProcessed Results:")
    print(f"Adjusted bars: {adjusted_bars}")
    print(f"Closing price: {closing_price}") 