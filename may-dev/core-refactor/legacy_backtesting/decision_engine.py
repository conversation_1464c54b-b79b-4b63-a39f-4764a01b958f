#!/usr/bin/env python3
"""
Standalone SPX Decision Engine
Extracted from the main trading system for backtesting and analysis
"""

from typing import Tuple, Dict, List, Any
from dataclasses import dataclass


@dataclass
class DecisionResult:
    """Result of the decision engine analysis"""
    decision: str
    strikes: List[float]
    option_types: List[str]  # 'C' or 'P'
    actions: List[str]       # 'BUY' or 'SELL'
    target_price: float
    reasoning: str
    bias: int               # -1, 0, +1 for iron butterfly
    spans: Dict[str, float] # span1, span2, span3
    close_percentages: List[float]  # Close % within each span


def calculate_spans(bar1_ohlc: Tuple[float, float, float, float], 
                   bar2_ohlc: Tuple[float, float, float, float], 
                   bar3_ohlc: Tuple[float, float, float, float]) -> <PERSON><PERSON>[float, float, float]:
    """
    Calculate SPAN1, SPAN2, SPAN3 from normalized bar data
    
    Args:
        bar1_ohlc: (open, high, low, close) for first bar
        bar2_ohlc: (open, high, low, close) for second bar  
        bar3_ohlc: (open, high, low, close) for third bar
        
    Returns:
        (span1, span2, span3) as floats
    """
    # SPAN1: First bar range
    span1 = bar1_ohlc[1] - bar1_ohlc[2]  # high - low
    
    # SPAN2: Combined range of first two bars
    span2 = (max(bar1_ohlc[1], bar2_ohlc[1]) - 
             min(bar1_ohlc[2], bar2_ohlc[2]))
    
    # SPAN3: Combined range of all three bars
    span3 = (max(bar1_ohlc[1], bar2_ohlc[1], bar3_ohlc[1]) - 
             min(bar1_ohlc[2], bar2_ohlc[2], bar3_ohlc[2]))
    
    return span1, span2, span3


def analyze_first_bar_decision(bar1_ohlc: Tuple[float, float, float, float], 
                              span1_points: float) -> Tuple[str, int, float]:
    """
    Analyze first bar for initial decision and bias tracking
    
    Args:
        bar1_ohlc: (open, high, low, close)
        span1_points: SPAN1 range in points
        
    Returns:
        (decision, bias, close_percentage)
    """
    if span1_points <= 0:
        return "iron_butterfly", 0, 50.0
        
    # Calculate close percentage within SPAN1
    close_pct = ((bar1_ohlc[3] - bar1_ohlc[2]) / span1_points) * 100
    
    # Bias tracking for iron butterfly strike selection
    bias = 0  # Default neutral
    if close_pct >= 70.0 and close_pct < 88.0:
        bias = +1  # bias up
    elif close_pct <= 30.0 and close_pct > 12.0:
        bias = -1  # bias down
    
    # Decision logic - extracted from spx_strategy.py
    if close_pct < 12.0:
        return "bear", bias, close_pct
    elif close_pct > 88.0:
        return "bull", bias, close_pct
    else:
        return "iron_butterfly", bias, close_pct


def check_second_bar_breakouts(bar1_ohlc: Tuple[float, float, float, float],
                              bar2_ohlc: Tuple[float, float, float, float], 
                              span2_points: float, 
                              current_decision: str) -> str:
    """
    Check for breakout conditions in second bar
    
    Args:
        bar1_ohlc: (open, high, low, close) 
        bar2_ohlc: (open, high, low, close)
        span2_points: SPAN2 range in points
        current_decision: Current decision from first bar
        
    Returns:
        Updated decision (or same if no breakout)
    """
    if span2_points <= 0:
        return current_decision
        
    span2_low = min(bar1_ohlc[2], bar2_ohlc[2])
    bear_threshold_price = span2_low + (12.0 / 100.0 * span2_points)
    bull_threshold_price = span2_low + (88.0 / 100.0 * span2_points)
    
    # Bear breakout: 2nd bar's low < 1st bar's low AND close < 12% of SPAN2
    if (bar2_ohlc[2] < bar1_ohlc[2] and 
        bar2_ohlc[3] < bear_threshold_price):
        return "bear"
    
    # Bull breakout: 2nd bar's high > 1st bar's high AND close > 88% of SPAN2
    elif (bar2_ohlc[1] > bar1_ohlc[1] and 
          bar2_ohlc[3] > bull_threshold_price):
        return "bull"
    
    return current_decision  # No breakout detected


def check_third_bar_breakouts(bar1_ohlc: Tuple[float, float, float, float],
                             bar2_ohlc: Tuple[float, float, float, float],
                             bar3_ohlc: Tuple[float, float, float, float], 
                             span3_points: float, 
                             current_decision: str) -> str:
    """
    Check for final breakout conditions in third bar
    
    Args:
        bar1_ohlc: (open, high, low, close)
        bar2_ohlc: (open, high, low, close) 
        bar3_ohlc: (open, high, low, close)
        span3_points: SPAN3 range in points
        current_decision: Current decision
        
    Returns:
        Updated decision (or same if no breakout)
    """
    if span3_points <= 0:
        return current_decision
        
    # Calculate first 2-bar's low and high
    two_bar_low = min(bar1_ohlc[2], bar2_ohlc[2])
    two_bar_high = max(bar1_ohlc[1], bar2_ohlc[1])
    
    span3_low = min(bar1_ohlc[2], bar2_ohlc[2], bar3_ohlc[2])
    bear_threshold_price = span3_low + (12.0 / 100.0 * span3_points)
    bull_threshold_price = span3_low + (88.0 / 100.0 * span3_points)

    # Bear breakout: 3rd bar's low < first 2-bar's low AND close < 12% of SPAN3
    if (bar3_ohlc[2] < two_bar_low and 
        bar3_ohlc[3] < bear_threshold_price):
        return "bear"

    # Bull breakout: 3rd bar's high > first 2-bar's high AND close > 88% of SPAN3
    elif (bar3_ohlc[1] > two_bar_high and 
          bar3_ohlc[3] > bull_threshold_price):
        return "bull"
    
    return current_decision


def apply_narrow_conversions(decision: str, span3_points: float, threshold: float = 4.0) -> str:
    """
    Apply narrow conversions if SPAN3 < threshold points
    
    Args:
        decision: Current decision
        span3_points: SPAN3 range in points
        threshold: Threshold for narrow conversion (default 4.0, can be VIX-based)
        
    Returns:
        Updated decision with narrow conversion if applicable
    """
    if span3_points < threshold:
        if decision == "bull" or decision == "BF-bull":
            return "narrow_bull"
        elif decision == "bear" or decision == "BF-bear":
            return "narrow_bear"
    
    return decision


def calculate_progressive_bias(bar1_ohlc: Tuple[float, float, float, float], span1: float,
                              bar2_ohlc: Tuple[float, float, float, float], span2: float,
                              bar3_ohlc: Tuple[float, float, float, float], span3: float) -> int:
    """
    Calculate bias progressively through all 3 bars (matches actual system)
    
    Args:
        bar1_ohlc: First bar OHLC
        span1: SPAN1 points
        bar2_ohlc: Second bar OHLC  
        span2: SPAN2 points
        bar3_ohlc: Third bar OHLC
        span3: SPAN3 points
        
    Returns:
        Final tracked bias (-1, 0, +1)
    """
    # Initialize bias
    current_bias = 0
    
    # Bar 1 bias calculation
    if span1 > 0:
        close_pct1 = ((bar1_ohlc[3] - bar1_ohlc[2]) / span1) * 100
        if close_pct1 >= 70.0 and close_pct1 < 88.0:
            current_bias = +1  # bias up
        elif close_pct1 <= 30.0 and close_pct1 > 12.0:
            current_bias = -1  # bias down
    
    # Bar 2 bias calculation (can override bar 1)
    if span2 > 0:
        span2_low = min(bar1_ohlc[2], bar2_ohlc[2])
        close_pct2 = ((bar2_ohlc[3] - span2_low) / span2) * 100
        if close_pct2 >= 70.0 and close_pct2 < 88.0:
            current_bias = +1  # Override previous bias
        elif close_pct2 <= 30.0 and close_pct2 > 12.0:
            current_bias = -1  # Override previous bias
    
    # Bar 3 bias calculation (final override)
    if span3 > 0:
        span3_low = min(bar1_ohlc[2], bar2_ohlc[2], bar3_ohlc[2])
        close_pct3 = ((bar3_ohlc[3] - span3_low) / span3) * 100
        if close_pct3 >= 70.0 and close_pct3 < 88.0:
            current_bias = +1  # Override previous bias - FINAL
        elif close_pct3 <= 30.0 and close_pct3 > 12.0:
            current_bias = -1  # Override previous bias - FINAL
    
    return current_bias


def get_atm_strike(current_price: float) -> float:
    """Round to nearest 5-point strike"""
    return round(current_price / 5) * 5


def get_next_strike_up(current_price: float) -> float:
    """Next strike above current price"""
    return (int(current_price / 5) + 1) * 5


def get_next_strike_down(current_price: float) -> float:
    """Next strike below current price"""  
    return int(current_price / 5) * 5


def determine_iron_butterfly_strike(current_price: float, bias: int) -> float:
    """
    Determine iron butterfly center strike based on bias direction
    Matches actual system's logic: bias determines rounding direction
    
    Args:
        current_price: Current SPX price
        bias: Bias value (-1, 0, +1)
        
    Returns:
        Center strike for iron butterfly
    """
    import math
    
    if bias == +1:  # bias up → round up
        return math.ceil(current_price / 5) * 5
    elif bias == -1:  # bias down → round down
        return math.floor(current_price / 5) * 5
    else:  # neutral → round nearest
        return round(current_price / 5) * 5


def get_strategy_strikes_and_types(decision: str, center_strike: float, current_price: float) -> Dict[str, Any]:
    """
    Get strikes, option types, actions, and target price for a decision
    
    Args:
        decision: Strategy decision
        center_strike: Center/ATM strike 
        current_price: Current SPX price
        
    Returns:
        Dictionary with strikes, option_types, actions, target_price
    """
    
    if decision == "bull" or decision == "BF-bull":
        # Bull spread: Buy ATM call, sell +5 call (5870/5875 example)
        buy_strike = center_strike  
        sell_strike = center_strike + 5
        return {
            'strikes': [buy_strike, sell_strike],
            'option_types': ['C', 'C'],
            'actions': ['BUY', 'SELL'],
            'target_price': sell_strike  # Max profit at short strike or above
        }
    
    elif decision == "bear" or decision == "BF-bear":
        # Bear spread: Buy ATM put, sell -5 put (5870/5865 example)
        buy_strike = center_strike
        sell_strike = center_strike - 5
        return {
            'strikes': [buy_strike, sell_strike],
            'option_types': ['P', 'P'], 
            'actions': ['BUY', 'SELL'],
            'target_price': sell_strike  # Max profit at short strike or below
        }
    
    elif decision == "iron_butterfly":
        # Iron butterfly: Sell ATM call+put, buy OTM call+put (±5 wings)
        return {
            'strikes': [center_strike - 5, center_strike, center_strike, center_strike + 5],
            'option_types': ['P', 'P', 'C', 'C'],
            'actions': ['BUY', 'SELL', 'SELL', 'BUY'],
            'target_price': center_strike  # Center strike
        }
    
    else:
        # Default to iron butterfly
        return {
            'strikes': [center_strike - 5, center_strike, center_strike, center_strike + 5],
            'option_types': ['P', 'P', 'C', 'C'],
            'actions': ['BUY', 'SELL', 'SELL', 'BUY'],
            'target_price': center_strike  # Center strike
        }


def analyze_normalized_bars(bar1_ohlc: Tuple[float, float, float, float], 
                           bar2_ohlc: Tuple[float, float, float, float], 
                           bar3_ohlc: Tuple[float, float, float, float],
                           narrow_threshold: float = 4.0) -> DecisionResult:
    """
    Main decision engine function - analyzes 3 normalized bars and returns trading decision
    
    Args:
        bar1_ohlc: (open, high, low, close) for first bar 
        bar2_ohlc: (open, high, low, close) for second bar
        bar3_ohlc: (open, high, low, close) for third bar
    
    Returns:
        DecisionResult with complete strategy information
    """
    
    # Step 1: Calculate spans
    span1, span2, span3 = calculate_spans(bar1_ohlc, bar2_ohlc, bar3_ohlc)
    
    # Step 2: First bar analysis
    decision, initial_bias, close_pct1 = analyze_first_bar_decision(bar1_ohlc, span1)
    reasoning = f"Bar 1: {decision} (close at {close_pct1:.1f}% of SPAN1)"
    
    # Step 3: Second bar breakout check
    previous_decision = decision
    decision = check_second_bar_breakouts(bar1_ohlc, bar2_ohlc, span2, decision)
    if decision != previous_decision:
        reasoning += f" → Bar 2 breakout: {decision}"
    
    # Step 4: Third bar breakout check  
    previous_decision = decision
    decision = check_third_bar_breakouts(bar1_ohlc, bar2_ohlc, bar3_ohlc, span3, decision)
    if decision != previous_decision:
        reasoning += f" → Bar 3 breakout: {decision}"
    
    # Step 5: BF conversion (if still IRON_BUTTERFLY and no breakout occurred)
    if decision == "iron_butterfly":
        # Convert to BF-Bull/BF-Bear based on first bar close percentage
        if close_pct1 > 66.0:
            decision = "BF-bull"
            reasoning += f" → BF conversion: BF-bull (1st bar close {close_pct1:.1f}% > 66%)"
        else:
            decision = "BF-bear"
            reasoning += f" → BF conversion: BF-bear (1st bar close {close_pct1:.1f}% ≤ 66%)"
    
    # Step 6: Narrow conversions (to identify and log them)
    previous_decision = decision
    decision = apply_narrow_conversions(decision, span3, narrow_threshold)
    if decision != previous_decision:
        reasoning += f" → Narrow conversion: {decision} (SPAN3={span3:.1f} < {narrow_threshold})"
    
    # Step 6: Calculate progressive bias (for iron butterfly strike selection)
    final_bias = calculate_progressive_bias(bar1_ohlc, span1, bar2_ohlc, span2, bar3_ohlc, span3)
    
    # Step 7: Determine strikes based on decision
    current_price = bar3_ohlc[3]  # Use bar 3 close as current price
    
    if decision == "iron_butterfly":
        center_strike = determine_iron_butterfly_strike(current_price, final_bias)
        bias_desc = "up" if final_bias > 0 else "down" if final_bias < 0 else "neutral"
        reasoning += f" | IB strike: bias {bias_desc} → {center_strike}"
    else:
        center_strike = get_atm_strike(current_price)
    
    strategy_info = get_strategy_strikes_and_types(decision, center_strike, current_price)
    
    # Calculate close percentages for each span
    close_percentages = []
    
    # Bar 1 close % in SPAN1
    if span1 > 0:
        close_percentages.append(((bar1_ohlc[3] - bar1_ohlc[2]) / span1) * 100)
    else:
        close_percentages.append(50.0)
    
    # Bar 2 close % in SPAN2
    if span2 > 0:
        span2_low = min(bar1_ohlc[2], bar2_ohlc[2])
        close_percentages.append(((bar2_ohlc[3] - span2_low) / span2) * 100)
    else:
        close_percentages.append(50.0)
    
    # Bar 3 close % in SPAN3
    if span3 > 0:
        span3_low = min(bar1_ohlc[2], bar2_ohlc[2], bar3_ohlc[2])
        close_percentages.append(((bar3_ohlc[3] - span3_low) / span3) * 100)
    else:
        close_percentages.append(50.0)
    
    return DecisionResult(
        decision=decision,
        strikes=strategy_info['strikes'],
        option_types=strategy_info['option_types'], 
        actions=strategy_info['actions'],
        target_price=strategy_info['target_price'],
        reasoning=reasoning,
        bias=final_bias,
        spans={'span1': span1, 'span2': span2, 'span3': span3},
        close_percentages=close_percentages
    )


# Convenience functions for testing
def print_decision_result(result: DecisionResult):
    """Pretty print decision result"""
    print(f"\n=== SPX DECISION RESULT ===")
    print(f"Decision: {result.decision.upper()}")
    print(f"Reasoning: {result.reasoning}")
    print(f"Target Price: ${result.target_price:.0f}")
    print(f"Bias: {result.bias} ({'up' if result.bias > 0 else 'down' if result.bias < 0 else 'neutral'})")
    
    print(f"\nStrike Details:")
    for i, (strike, opt_type, action) in enumerate(zip(result.strikes, result.option_types, result.actions)):
        print(f"  Leg {i+1}: {action} ${strike:.0f}{opt_type}")
    
    print(f"\nSpans: SPAN1={result.spans['span1']:.1f}, SPAN2={result.spans['span2']:.1f}, SPAN3={result.spans['span3']:.1f}")
    print(f"Close %: Bar1={result.close_percentages[0]:.1f}%, Bar2={result.close_percentages[1]:.1f}%, Bar3={result.close_percentages[2]:.1f}%")


if __name__ == "__main__":
    # Test with sample data
    print("Testing SPX Decision Engine...")
    
    # Test case 1: Bull signal
    bar1 = (5800.0, 5805.0, 5795.0, 5804.0)  # Close high in range
    bar2 = (5803.0, 5810.0, 5800.0, 5808.0)  # Breakout higher
    bar3 = (5808.0, 5815.0, 5805.0, 5812.0)  # Continue higher
    
    result = analyze_normalized_bars(bar1, bar2, bar3)
    print_decision_result(result) 