#!/usr/bin/env python3
"""
Quick Run Script for SPX Historical Backtesting
Simple entry point with sensible defaults
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# Add paths to import from the main system
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from historical_backtester import HistoricalBacktester


def run_quick_backtest(days_back: int = 30, simple_mode: bool = False, skip_monday: bool = False):
    """
    Run a backtest for the last N days
    
    Args:
        days_back: Number of days to go back from today
        simple_mode: If True, use simple mode with fixed costs
        skip_monday: If True, skip Monday and post-gap trades
    """
    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days_back)
    
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')
    
    mode_str = "SIMPLE MODE" if simple_mode else "ADVANCED MODE"
    monday_str = " + NO MONDAY" if skip_monday else ""
    print(f"🚀 Running SPX Backtest for {start_date_str} to {end_date_str} ({mode_str}{monday_str})")
    print("="*60)
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Run backtest
    backtester = HistoricalBacktester(start_date_str, end_date_str, simple_mode=simple_mode, skip_monday=skip_monday)
    results = backtester.run_backtest()
    
    if results:
        # Print summary
        backtester.print_summary()
        
        # Save results
        backtester.save_results_to_csv()
        backtester.save_summary_to_json()
        
        print(f"\n✅ Backtest completed! Results saved to backtesting/ directory.")
        print(f"📊 Processed {len(results)} trading days")
        
        return True
    else:
        print("❌ No results generated. Check database connection and date range.")
        return False


def main():
    """Main entry point"""
    print("SPX Historical Backtesting - Quick Run")
    print("="*60)
    
    # Parse command line arguments
    days_back = 30  # Default
    simple_mode = False
    skip_monday = False
    
    if len(sys.argv) > 1:
        for arg in sys.argv[1:]:
            if arg.lower() in ['--simple', '-s']:
                simple_mode = True
            elif arg.lower() in ['--skip-monday', '--no-monday', '-m']:
                skip_monday = True
            elif arg.lower() in ['--help', '-h']:
                print("Usage: python run_backtest.py [days_back] [--simple|-s] [--skip-monday|-m]")
                print("Examples:")
                print("  python run_backtest.py 30")
                print("  python run_backtest.py 30 --simple")
                print("  python run_backtest.py 30 --skip-monday")
                print("  python run_backtest.py --simple --skip-monday")
                print("\nOptions:")
                print("  --simple, -s       Use simple mode with fixed costs")
                print("  --skip-monday, -m  Skip Monday and post-gap day trades")
                sys.exit(0)
            else:
                try:
                    days_back = int(arg)
                except ValueError:
                    print("Usage: python run_backtest.py [days_back] [--simple|-s] [--skip-monday|-m]")
                    print("Example: python run_backtest.py 30 --simple --skip-monday")
                    sys.exit(1)
    
    print(f"📅 Backtesting last {days_back} days...")
    
    success = run_quick_backtest(days_back, simple_mode, skip_monday)
    
    if success:
        print("\n🎉 Backtest completed successfully!")
        print("\nFor more options, use:")
        print("python backtesting/historical_backtester.py --help")
    else:
        print("\n⚠️  Backtest failed. Check the troubleshooting section in README.md")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main() 