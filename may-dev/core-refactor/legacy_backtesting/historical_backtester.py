#!/usr/bin/env python3
"""
Historical Backtester for SPX Trading Strategy
Main runner that processes all historical data and generates results
"""

import sys
import os
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
import logging
import csv
from datetime import datetime, timedelta
import json
import random

# Monday Mode Configuration
MONDAY_MODE_ENABLED = True          # Skip Monday trades (and post-gap days)

# Add paths to import from the main system
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Import our new components
from database_connector import DatabaseConnector
from bar_processor import BarProcessor
from decision_engine import analyze_normalized_bars, DecisionResult
from options_pricer import (
    find_optimal_strikes, 
    price_bull_call_spread, 
    price_bear_put_spread, 
    price_iron_butterfly
)

try:
    from database_output import BacktestDatabaseOutput
    DATABASE_OUTPUT_AVAILABLE = True
except ImportError:
    DATABASE_OUTPUT_AVAILABLE = False
    logger.warning("Database output not available. Install pyodbc to enable database output.")

logger = logging.getLogger(__name__)


@dataclass
class BacktestResult:
    """Result of backtesting for a single day"""
    trading_date: str
    decision: str
    target_price: float
    closing_price: float
    reasoning: str
    bias: int
    spans: Dict[str, float]
    strikes: List[float]
    option_types: List[str]
    actions: List[str]
    close_percentages: List[float]
    bars: List[Tuple[float, float, float, float]]
    
    # Options pricing information
    execution_price: float  # SPX price at execution (bar 3 close)
    original_strikes: List[float]  # Initial strikes before optimization
    final_strikes: List[float]  # Final strikes after optimization
    spread_price: float  # Actual spread price paid/collected
    strikes_moved: bool  # Whether strikes were moved due to pricing
    
    # Position sizing and risk management
    current_balance: float  # Account balance before this trade
    max_risk_amount: float  # Maximum risk for this trade (balance * risk_pc)
    risk_per_contract: float  # Risk per contract in dollars
    num_contracts: int  # Number of contracts traded
    actual_risk_amount: float  # Actual risk taken (num_contracts * risk_per_contract)
    
    # Trade outcome
    trade_successful: bool
    profit_loss: float  # Total P&L for the position (includes contract sizing)
    new_balance: float  # Account balance after this trade
    
    # Narrow strategy fill data
    narrow_fill_ohlc: List[Tuple[float, float, float, float]]  # OHLC bars from 15:51:30-16:00:00 (10-second intervals)
    narrow_fill_consolidated_ohlc: Tuple[float, float, float, float]  # Single OHLC for entire 15:51:30-16:00:00 period
    
    # Trade identification for dual trades
    is_dual_trade: bool = False  # Whether this is part of a dual trade day
    dual_trade_type: str = ""  # "primary" or "secondary" for dual trades
    
    # Metadata
    processing_time: float = 0.0
    
    # Extra butterfly trade information
    extra_butterfly_result: Optional[Any] = None  # ExtraButterflyResult
    extra_butterfly_trade_successful: bool = False
    extra_butterfly_profit_loss: float = 0.0
    extra_butterfly_filled: bool = False  # Track if trade was filled
    extra_butterfly_num_contracts: int = 0  # Number of contracts for extra butterfly
    extra_butterfly_actual_risk: float = 0.0  # Actual risk amount for extra butterfly


class HistoricalBacktester:
    """Main backtesting engine for historical SPX data"""
    
    def __init__(self, start_date: Optional[str] = None, end_date: Optional[str] = None, volatility: float = 0.45, simple_mode: bool = False, pricing_mode: str = "fixed", skip_monday: bool = False, starting_balance: float = 10000.0, max_risk_per_trade_pc: float = 0.07, simple_spread_cost: float = 2.20, simple_butterfly_credit: float = 3.40, simple_itm_threshold: float = 2.50, max_risk_bull_pc: Optional[float] = None, max_risk_bear_pc: Optional[float] = None, max_risk_iron_butterfly_pc: Optional[float] = None, alternate_strategy: bool = False, butterfly_conversion_only: bool = False, butterfly_narrow_conversions: bool = False, butterfly_conversion_with_narrows: bool = False, butterfly_conversion_latest: bool = False, extra_butterfly: bool = False, vix_based_threshold: bool = False, skip_target_validation: bool = False):
        """
        Initialize Historical Backtester
        
        Args:
            start_date: Start date for backtesting (YYYY-MM-DD format). If None, uses earliest available data.
            end_date: End date for backtesting (YYYY-MM-DD format). If None, uses latest available data.
            volatility: Implied volatility for pricing (default 0.45 = 45%)
            simple_mode: Use simple mode with simplified strike selection
            pricing_mode: Pricing method - 'fixed', 'basic', or 'advanced'
            skip_monday: Skip Monday trades (default False)
            starting_balance: Starting account balance (default $10,000)
            max_risk_per_trade_pc: Default maximum risk per trade as percentage of balance (default 0.07 = 7%)
            simple_spread_cost: Fixed cost for spreads in fixed mode (default 2.20)
            simple_butterfly_credit: Fixed credit for iron butterflies in fixed mode (default 3.40)
            simple_itm_threshold: ITM threshold for basic pricing (default 2.50)
            max_risk_bull_pc: Maximum risk for bull strategies (if None, uses max_risk_per_trade_pc)
            max_risk_bear_pc: Maximum risk for bear strategies (if None, uses max_risk_per_trade_pc)
            max_risk_iron_butterfly_pc: Maximum risk for iron butterfly strategies (if None, uses max_risk_per_trade_pc)
            alternate_strategy: Use alternate strategy conversion rules (default False)
            butterfly_conversion_only: Convert only iron butterflies to spreads, keep narrow strategies unchanged (default False)
            butterfly_narrow_conversions: Convert iron butterflies to bull/bear, but use narrow_bull/narrow_bear if span3 < 4 (default False)
            butterfly_conversion_with_narrows: Convert butterflies based on 1st bar close >=66% and span3 threshold of 4 points (default False)
            butterfly_conversion_latest: Latest conversion mode with dual trades for narrow strategies (default False)
            vix_based_threshold: Use VIX-based narrow conversion threshold (4.0 when VIX > 16.5, 6.0 when VIX <= 16.5) (default False)
            skip_target_validation: Skip target price validation for narrow spreads (backtesting only) (default False)
        """
        self.start_date = start_date
        self.end_date = end_date
        self.volatility = volatility
        self.simple_mode = simple_mode
        self.pricing_mode = pricing_mode
        self.skip_monday = skip_monday
        self.starting_balance = starting_balance
        self.current_balance = starting_balance
        self.max_risk_per_trade_pc = max_risk_per_trade_pc
        self.alternate_strategy = alternate_strategy
        self.butterfly_conversion_only = butterfly_conversion_only
        self.butterfly_narrow_conversions = butterfly_narrow_conversions
        self.butterfly_conversion_with_narrows = butterfly_conversion_with_narrows
        self.butterfly_conversion_latest = butterfly_conversion_latest
        self.extra_butterfly = extra_butterfly
        self.vix_based_threshold = vix_based_threshold
        self.skip_target_validation = skip_target_validation
        
        # Strategy-specific risk limits (fall back to default if not specified)
        self.max_risk_bull_pc = max_risk_bull_pc if max_risk_bull_pc is not None else max_risk_per_trade_pc
        self.max_risk_bear_pc = max_risk_bear_pc if max_risk_bear_pc is not None else max_risk_per_trade_pc
        self.max_risk_iron_butterfly_pc = max_risk_iron_butterfly_pc if max_risk_iron_butterfly_pc is not None else max_risk_per_trade_pc
        
        self.simple_spread_cost = simple_spread_cost
        self.simple_butterfly_credit = simple_butterfly_credit
        self.simple_itm_threshold = simple_itm_threshold
        
        # Initialize database connector
        self.db_connector = DatabaseConnector()
        
        # Initialize extra butterfly engine if enabled
        if self.extra_butterfly:
            from extra_butterfly_engine import ExtraButterflyEngine
            self.extra_butterfly_engine = ExtraButterflyEngine()
        else:
            self.extra_butterfly_engine = None
        self.results: List[BacktestResult] = []
        self.all_trading_dates: List[str] = []  # Cache all trading dates for gap detection
    
    def is_monday_or_post_gap(self, trading_date: str) -> Tuple[bool, str]:
        """
        Check if a trading date is Monday or follows a gap (holiday/weekend)
        
        Args:
            trading_date: Date string in 'YYYY-MM-DD' format
            
        Returns:
            (is_post_gap, reason) tuple where reason is 'monday', 'post_gap', or 'normal'
        """
        try:
            current_date = datetime.strptime(trading_date, '%Y-%m-%d')
            
            # Check if it's Monday
            if current_date.weekday() == 0:  # Monday = 0
                return True, 'monday'
            
            # Check if previous business day had trading data
            if not self.all_trading_dates:
                return False, 'normal'  # Can't determine gap without full date list
            
            # Find current date index in trading dates
            try:
                current_index = self.all_trading_dates.index(trading_date)
            except ValueError:
                return False, 'normal'  # Date not in trading dates
                
            if current_index == 0:
                return False, 'normal'  # First date in range
            
            # Get previous trading date
            prev_trading_date = self.all_trading_dates[current_index - 1]
            prev_date = datetime.strptime(prev_trading_date, '%Y-%m-%d')
            
            # Calculate expected previous business day
            expected_prev = current_date - timedelta(days=1)
            while expected_prev.weekday() >= 5:  # Skip weekends
                expected_prev -= timedelta(days=1)
            
            # If there's a gap (more than expected business days), it's post-gap
            days_diff = (current_date - prev_date).days
            if days_diff > 3:  # More than a normal weekend gap
                return True, 'post_gap'
                
            return False, 'normal'
            
        except Exception as e:
            logger.warning(f"Error checking Monday/gap status for {trading_date}: {e}")
            return False, 'normal'
    
    def get_narrow_conversion_threshold(self, trading_date: str) -> float:
        """
        Get the narrow conversion threshold based on VIX level if VIX-based threshold is enabled
        
        Args:
            trading_date: Date in 'YYYY-MM-DD' format
            
        Returns:
            Narrow conversion threshold (4.0 or 6.0)
        """
        if not self.vix_based_threshold:
            return 4.0  # Default threshold
        
        try:
            vix_close = self.db_connector.get_vix_closing_price(trading_date)
            if vix_close is None:
                logger.warning(f"No VIX data found for {trading_date}, using default threshold 4.0")
                return 4.0
            
            if vix_close <= 16.5:
                logger.info(f"VIX {vix_close:.2f} <= 16.5, using narrow threshold 6.0")
                return 6.0
            else:
                logger.info(f"VIX {vix_close:.2f} > 16.5, using narrow threshold 4.0")
                return 4.0
                
        except Exception as e:
            logger.error(f"Error getting VIX-based threshold for {trading_date}: {e}")
            return 4.0  # Fallback to default
    
    def get_simple_mode_strikes(self, decision: str, execution_price: float, original_center_strike: float = None) -> Tuple[List[float], float]:
        """
        Get strikes using simple mode logic with different pricing models
        
        Args:
            decision: Strategy decision
            execution_price: SPX price at execution
            original_center_strike: Original center strike from decision engine (for iron butterfly)
            
        Returns:
            (final_strikes, spread_price)
        """
        if decision in ["bull", "narrow_bull", "BF-bull"]:
            # Bull spread: determine strikes first
            atm_strike = round(execution_price / 5) * 5  # Round to nearest 5
            
            if decision == "narrow_bull":
                # Narrow bull: long call = closest strike to SPX, then "one better" (move away)
                closest_strike = round(execution_price / 5) * 5  # Round to nearest strike
                # Closest spread: long call at closest strike to SPX
                closest_long = closest_strike
                closest_short = closest_strike + 5
                # One better: move both strikes down by 5 (away from SPX)
                long_strike = closest_long - 5   # Buy call one better
                short_strike = closest_short - 5 # Sell call one better
                
                # VALIDATION: For narrow_bull, target_price (short_strike) must be > execution_price
                # If not, move strikes up until target is valid (unless skip_target_validation is enabled)
                if not self.skip_target_validation:
                    while short_strike <= execution_price:
                        long_strike += 5
                        short_strike += 5
                
                # All narrow strategies now use fixed $2.05 pricing
                return [long_strike, short_strike], 2.05  # Fixed $2.05 debit for narrow
            else:
                # Normal bull or BF-bull: ATM-5/ATM
                long_strike = atm_strike - 5
                short_strike = atm_strike
                
                # Check if more than threshold in the money - move to next strike
                if execution_price > (long_strike + self.simple_itm_threshold):
                    long_strike += 5
                    short_strike += 5
                
                # Pricing based on mode
                if self.pricing_mode == "fixed":
                    return [long_strike, short_strike], self.simple_spread_cost  # Fixed debit
                elif self.pricing_mode == "basic":
                    spread_price = self.basic_spread_price(execution_price, long_strike, short_strike)
                    return [long_strike, short_strike], spread_price
                else:  # advanced mode - will be handled elsewhere
                    return [long_strike, short_strike], self.simple_spread_cost  # Default
            
        elif decision in ["bear", "narrow_bear", "BF-bear"]:
            # Bear spread: determine strikes first
            atm_strike = round(execution_price / 5) * 5  # Round to nearest 5
            
            if decision == "narrow_bear":
                # Narrow bear: long put = closest strike to SPX, then "one better" (move away)
                closest_strike = round(execution_price / 5) * 5  # Round to nearest strike
                # Closest spread: long put at closest strike to SPX
                closest_long = closest_strike
                closest_short = closest_strike - 5
                # One better: move both strikes up by 5 (away from SPX)
                long_strike = closest_long + 5   # Buy put one better
                short_strike = closest_short + 5 # Sell put one better
                
                # VALIDATION: For narrow_bear, target_price (short_strike) must be < execution_price
                # If not, move strikes down until target is valid (unless skip_target_validation is enabled)
                if not self.skip_target_validation:
                    while short_strike >= execution_price:
                        long_strike -= 5
                        short_strike -= 5
                
                # All narrow strategies now use fixed $2.05 pricing
                return [long_strike, short_strike], 2.05  # Fixed $2.05 debit for narrow
            else:
                # Normal bear or BF-bear: ATM+5/ATM
                long_strike = atm_strike + 5
                short_strike = atm_strike
                
                # Check if more than threshold in the money - move to next strike
                if execution_price < (long_strike - self.simple_itm_threshold):
                    long_strike -= 5
                    short_strike -= 5
                
                # Pricing based on mode
                if self.pricing_mode == "fixed":
                    return [long_strike, short_strike], self.simple_spread_cost  # Fixed debit
                elif self.pricing_mode == "basic":
                    spread_price = self.basic_spread_price(execution_price, long_strike, short_strike)
                    return [long_strike, short_strike], spread_price
                else:  # advanced mode - will be handled elsewhere
                    return [long_strike, short_strike], self.simple_spread_cost  # Default
            
        elif decision == "iron_butterfly":
            # Iron butterfly: use the bias-adjusted center strike from decision engine
            center_strike = original_center_strike if original_center_strike else round(execution_price / 5) * 5
            
            # Only apply ITM threshold check if we didn't get a bias-adjusted center strike
            # (i.e., if original_center_strike is None, meaning no bias was applied)
            if original_center_strike is None:
                # Check if more than threshold from center - move center strike
                if execution_price > (center_strike + self.simple_itm_threshold):
                    center_strike += 5
                elif execution_price < (center_strike - self.simple_itm_threshold):
                    center_strike -= 5
                
            # Iron butterfly has 4 strikes: [put_wing, atm_put, atm_call, call_wing]
            final_strikes = [center_strike - 5, center_strike, center_strike, center_strike + 5]
            
            # Pricing based on mode - all iron butterflies now fill at $3.40 credit
            return final_strikes, -3.40  # Fixed credit (negative)
            
        else:
            # Default case
            return [round(execution_price / 5) * 5], 0.0

    def calculate_position_size(self, decision: str, spread_price: float, filled: bool = False) -> Tuple[float, float, int, float]:
        """
        Calculate position size based on current balance and risk management
        
        Args:
            decision: Strategy decision (affects risk calculation)
            spread_price: Spread price per contract
            filled: Whether narrow strategy actually filled (only used for narrow strategies)
            
        Returns:
            (max_risk_amount, risk_per_contract, num_contracts, actual_risk_amount)
        """
        # Get strategy-specific risk percentage
        if decision in ["bull", "narrow_bull", "BF-bull"]:
            risk_pc = self.max_risk_bull_pc
        elif decision in ["bear", "narrow_bear", "BF-bear"]:
            risk_pc = self.max_risk_bear_pc
        elif decision == "iron_butterfly":
            risk_pc = self.max_risk_iron_butterfly_pc
        else:
            risk_pc = self.max_risk_per_trade_pc  # fallback
        
        # Calculate maximum risk for this trade
        max_risk_amount = self.current_balance * risk_pc
        
        # For narrow strategies that didn't fill, return 0 contracts for data collection
        if decision in ["narrow_bull", "narrow_bear"] and not filled:
            # Still calculate what the position would have been for data collection
            if spread_price <= 0:
                return max_risk_amount, 0.0, 0, 0.0
            
            risk_per_contract = abs(spread_price) * 100  # Each contract represents $100 per point
            num_contracts = int(max_risk_amount // risk_per_contract) if risk_per_contract > 0 else 0
            return max_risk_amount, risk_per_contract, 0, 0.0  # 0 contracts for unfilled narrow
        
        # Calculate risk per contract based on strategy type
        if decision == "iron_butterfly":
            # For iron butterflies: risk = (strike_width - credit_received) * 100
            # spread_price is negative for credit spreads
            if spread_price >= 0:
                # If spread_price is positive, something is wrong - skip trade
                return max_risk_amount, 0.0, 0, 0.0
            
            credit_received = abs(spread_price)  # Convert negative credit to positive
            strike_width = 5.0  # Iron butterfly always has 5-point wings
            risk_per_contract = (strike_width - credit_received) * 100
        else:
            # For bull/bear spreads: risk = debit_paid * 100
            if spread_price <= 0:
                return max_risk_amount, 0.0, 0, 0.0
            
            risk_per_contract = abs(spread_price) * 100
        
        # Calculate number of contracts we can afford
        num_contracts = int(max_risk_amount // risk_per_contract) if risk_per_contract > 0 else 0
        
        # Calculate actual risk amount
        actual_risk_amount = num_contracts * risk_per_contract
        
        return max_risk_amount, risk_per_contract, num_contracts, actual_risk_amount

    def optimize_strikes_and_price(self, decision_result: DecisionResult, execution_price: float) -> Tuple[List[float], List[float], float, bool]:
        """
        Optimize strikes based on actual options pricing or simple mode
        
        Args:
            decision_result: Original decision from strategy
            execution_price: SPX price at execution (bar 3 close)
            
        Returns:
            (original_strikes, final_strikes, spread_price, strikes_moved)
        """
        original_strikes = decision_result.strikes.copy()
        decision = decision_result.decision
        
        # Use simple mode if enabled
        if self.simple_mode:
            try:
                # For iron butterfly, use target_price as the original center strike (bias-adjusted)
                original_center_strike = decision_result.target_price if decision == "iron_butterfly" else None
                final_strikes, spread_price = self.get_simple_mode_strikes(decision, execution_price, original_center_strike)
                strikes_moved = (original_strikes != final_strikes)
                return original_strikes, final_strikes, spread_price, strikes_moved
            except Exception as e:
                logger.error(f"Error in simple mode strikes: {str(e)}")
                return original_strikes, original_strikes, 0.0, False
        
        # Use basic pricing mode if enabled (but not simple mode)
        elif self.pricing_mode == "basic":
            try:
                atm_strike = round(execution_price / 5) * 5  # Round to nearest 5
                
                if decision in ["bull", "narrow_bull", "BF-bull"]:
                    if decision == "narrow_bull":
                        # Narrow bull: long call = closest strike to SPX, then "one better" (move away)
                        closest_strike = round(execution_price / 5) * 5  # Round to nearest strike
                        # Closest spread: long call at closest strike to SPX
                        closest_long = closest_strike
                        closest_short = closest_strike + 5
                        # One better: move both strikes down by 5 (away from SPX)
                        long_strike = closest_long - 5   # Buy call one better
                        short_strike = closest_short - 5 # Sell call one better
                        
                        # VALIDATION: For narrow_bull, target_price (short_strike) must be > execution_price
                        # If not, move strikes up until target is valid (unless skip_target_validation is enabled)
                        if not self.skip_target_validation:
                            while short_strike <= execution_price:
                                long_strike += 5
                                short_strike += 5
                        
                        spread_price = 2.05  # Fixed $2.05 for narrow strategies
                        final_strikes = [long_strike, short_strike]
                    else:
                        # Normal bull or BF-bull: use optimization logic for strike selection
                        long_strike, short_strike, _ = find_optimal_strikes(
                            S=execution_price,
                            strategy='bull',
                            center_strike=atm_strike,
                            max_debit=2.50,
                            sigma=self.volatility
                        )
                        
                        # Use basic pricing
                        spread_price = self.basic_spread_price(execution_price, long_strike, short_strike)
                        final_strikes = [long_strike, short_strike]
                    
                elif decision in ["bear", "narrow_bear", "BF-bear"]:
                    if decision == "narrow_bear":
                        # Narrow bear: long put = closest strike to SPX, then "one better" (move away)
                        closest_strike = round(execution_price / 5) * 5  # Round to nearest strike
                        # Closest spread: long put at closest strike to SPX
                        closest_long = closest_strike
                        closest_short = closest_strike - 5
                        # One better: move both strikes up by 5 (away from SPX)
                        long_strike = closest_long + 5   # Buy put one better
                        short_strike = closest_short + 5 # Sell put one better
                        
                        # VALIDATION: For narrow_bear, target_price (short_strike) must be < execution_price
                        # If not, move strikes down until target is valid (unless skip_target_validation is enabled)
                        if not self.skip_target_validation:
                            while short_strike >= execution_price:
                                long_strike -= 5
                                short_strike -= 5
                        
                        spread_price = 2.05  # Fixed $2.05 for narrow strategies
                        final_strikes = [long_strike, short_strike]
                    else:
                        # Normal bear or BF-bear: use optimization logic for strike selection
                        long_strike, short_strike, _ = find_optimal_strikes(
                            S=execution_price,
                            strategy='bear',
                            center_strike=atm_strike,
                            max_debit=2.50,
                            sigma=self.volatility
                        )
                        
                        # Use basic pricing
                        spread_price = self.basic_spread_price(execution_price, long_strike, short_strike)
                        final_strikes = [long_strike, short_strike]
                    
                elif decision == "iron_butterfly":
                    # Use bias-adjusted center strike if available
                    center_strike = decision_result.target_price if hasattr(decision_result, 'target_price') else atm_strike
                    
                    # All iron butterflies now fill at $3.40 credit (no distance check)
                    spread_price = -3.40
                        
                    # Iron butterfly has 4 strikes: [put_wing, atm_put, atm_call, call_wing]
                    final_strikes = [center_strike - 5, center_strike, center_strike, center_strike + 5]
                    
                else:
                    # Unknown strategy, use original strikes
                    final_strikes = original_strikes
                    spread_price = 0.0
                    
                # Check if strikes were moved
                strikes_moved = (original_strikes != final_strikes)
                
                return original_strikes, final_strikes, spread_price, strikes_moved
                
            except Exception as e:
                logger.error(f"Error in basic pricing mode: {str(e)}")
                return original_strikes, original_strikes, 0.0, False
        
        # Original complex pricing logic
        try:
            if decision in ["bull", "narrow_bull", "BF-bull"]:
                # Find optimal bull strikes
                atm_strike = round(execution_price / 5) * 5  # Round to nearest 5
                
                if decision == "narrow_bull":
                    # Narrow bull: long call = closest strike to SPX, then "one better" (move away)
                    closest_strike = round(execution_price / 5) * 5  # Round to nearest strike
                    # Closest spread: long call at closest strike to SPX
                    closest_long = closest_strike
                    closest_short = closest_strike + 5
                    # One better: move both strikes down by 5 (away from SPX)
                    long_strike = closest_long - 5   # Buy call one better
                    short_strike = closest_short - 5 # Sell call one better
                    
                    # VALIDATION: For narrow_bull, target_price (short_strike) must be > execution_price
                    # If not, move strikes up until target is valid (unless skip_target_validation is enabled)
                    if not self.skip_target_validation:
                        while short_strike <= execution_price:
                            long_strike += 5
                            short_strike += 5
                    
                    spread_price = 2.05  # Fixed price for narrow strategies
                    final_strikes = [long_strike, short_strike]
                else:
                    # Normal bull or BF-bull: use optimization
                    long_strike, short_strike, spread_price = find_optimal_strikes(
                        S=execution_price,
                        strategy='bull',
                        center_strike=atm_strike,
                        max_debit=2.50,
                        sigma=self.volatility
                    )
                    final_strikes = [long_strike, short_strike]
                
            elif decision in ["bear", "narrow_bear", "BF-bear"]:
                # Find optimal bear strikes
                atm_strike = round(execution_price / 5) * 5  # Round to nearest 5
                
                if decision == "narrow_bear":
                    # Narrow bear: long put = closest strike to SPX, then "one better" (move away)
                    closest_strike = round(execution_price / 5) * 5  # Round to nearest strike
                    # Closest spread: long put at closest strike to SPX
                    closest_long = closest_strike
                    closest_short = closest_strike - 5
                    # One better: move both strikes up by 5 (away from SPX)
                    long_strike = closest_long + 5   # Buy put one better
                    short_strike = closest_short + 5 # Sell put one better
                    
                    # VALIDATION: For narrow_bear, target_price (short_strike) must be < execution_price
                    # If not, move strikes down until target is valid (unless skip_target_validation is enabled)
                    if not self.skip_target_validation:
                        while short_strike >= execution_price:
                            long_strike -= 5
                            short_strike -= 5
                    
                    spread_price = 2.05  # Fixed price for narrow strategies
                    final_strikes = [long_strike, short_strike]
                else:
                    # Normal bear or BF-bear: use optimization
                    long_strike, short_strike, spread_price = find_optimal_strikes(
                        S=execution_price,
                        strategy='bear',
                        center_strike=atm_strike,
                        max_debit=2.50,
                        sigma=self.volatility
                    )
                    final_strikes = [long_strike, short_strike]
            
            elif decision == "iron_butterfly":
                # Price iron butterfly at current execution price
                center_strike = round(execution_price / 5) * 5  # Round to nearest 5
                
                # All iron butterflies now fill at $3.40 credit (no distance check)
                spread_price = -3.40
                    
                # Iron butterfly has 4 strikes: [put_wing, atm_put, atm_call, call_wing]
                final_strikes = [center_strike - 5, center_strike, center_strike, center_strike + 5]
                
            else:
                # Unknown strategy, use original strikes
                final_strikes = original_strikes
                spread_price = 0.0
            
            # Check if strikes were moved
            strikes_moved = (original_strikes != final_strikes)
            
            return original_strikes, final_strikes, spread_price, strikes_moved
            
        except Exception as e:
            logger.error(f"Error optimizing strikes: {str(e)}")
            return original_strikes, original_strikes, 0.0, False

    def determine_trade_outcome(self, decision_result: DecisionResult, closing_price: float, final_strikes: List[float], spread_price: float, filled: bool = False) -> Tuple[bool, float, float, float, int, float]:
        """
        Determine trade outcome based on actual options pricing and position sizing
        
        Args:
            decision_result: DecisionResult from decision engine
            closing_price: SPX closing price
            final_strikes: Final optimized strikes
            spread_price: Actual spread price paid/collected per contract
            filled: Whether narrow strategy actually filled (only used for narrow strategies)
            
        Returns:
            (trade_successful, profit_loss, max_risk_amount, risk_per_contract, num_contracts, actual_risk_amount)
        """
        try:
            decision = decision_result.decision
            
            # Calculate position sizing
            max_risk_amount, risk_per_contract, num_contracts, actual_risk_amount = self.calculate_position_size(decision, spread_price, filled)
            
            # Handle narrow strategies with 0 contracts (data collection only)
            if decision in ["narrow_bull", "narrow_bear"] and not filled:
                logger.info(f"Narrow strategy {decision}: 0 contracts traded (data collection only)")
                return False, 0.0, max_risk_amount, risk_per_contract, 0, 0.0
            
            # Skip trade if we can't afford even 1 contract
            if num_contracts == 0:
                logger.warning(f"Insufficient balance for trade: need ${risk_per_contract:.0f}, have ${max_risk_amount:.0f} max risk")
                return False, 0.0, max_risk_amount, risk_per_contract, 0, 0.0
            
            # Calculate P&L based on strategy type and position size
            if decision in ["bull", "narrow_bull", "BF-bull"]:
                # Bull spread: Debit spread - paid spread_price upfront per contract
                long_strike = final_strikes[0]   # Long call
                short_strike = final_strikes[1]  # Short call
                debit_paid_per_contract = spread_price  # This is the debit per contract
                total_debit_paid = debit_paid_per_contract * 100 * num_contracts  # Convert to dollars
                
                if closing_price >= short_strike:
                    # Max profit - each spread worth full $5.00
                    spread_value_per_contract = 500.0  # $5.00 * 100
                    total_spread_value = spread_value_per_contract * num_contracts
                    profit_loss = total_spread_value - abs(total_debit_paid)
                    trade_successful = True
                elif closing_price <= long_strike:
                    # Max loss - spread worthless $0.00
                    profit_loss = -abs(total_debit_paid)
                    trade_successful = False
                else:
                    # Partial value: intrinsic value of spread
                    intrinsic_value_per_contract = (closing_price - long_strike) * 100
                    total_intrinsic_value = intrinsic_value_per_contract * num_contracts
                    profit_loss = total_intrinsic_value - abs(total_debit_paid)
                    trade_successful = profit_loss > 0
            
            elif decision in ["bear", "narrow_bear", "BF-bear"]:
                # Bear spread: Debit spread - paid spread_price upfront per contract
                long_strike = final_strikes[0]   # Long put
                short_strike = final_strikes[1]  # Short put
                debit_paid_per_contract = spread_price  # This is the debit per contract
                total_debit_paid = debit_paid_per_contract * 100 * num_contracts  # Convert to dollars
                
                if closing_price <= short_strike:
                    # Max profit - each spread worth full $5.00
                    spread_value_per_contract = 500.0  # $5.00 * 100
                    total_spread_value = spread_value_per_contract * num_contracts
                    profit_loss = total_spread_value - abs(total_debit_paid)
                    trade_successful = True
                elif closing_price >= long_strike:
                    # Max loss - spread worthless $0.00
                    profit_loss = -abs(total_debit_paid)
                    trade_successful = False
                else:
                    # Partial value: intrinsic value of spread
                    intrinsic_value_per_contract = (long_strike - closing_price) * 100
                    total_intrinsic_value = intrinsic_value_per_contract * num_contracts
                    profit_loss = total_intrinsic_value - abs(total_debit_paid)
                    trade_successful = profit_loss > 0
            
            elif decision == "iron_butterfly":
                # Iron butterfly: Credit spread - collect |spread_price| upfront per contract
                center_strike = final_strikes[1]  # Center strike (ATM put/call)
                credit_per_contract = abs(spread_price)  # spread_price is negative for credit
                total_credit_received = credit_per_contract * 100 * num_contracts  # Convert to dollars
                distance_from_center = abs(closing_price - center_strike)
                
                if distance_from_center <= 5.0:
                    # Linear P&L from center to wings
                    # At center: keep full credit, At wings: lose (spread_width - credit)
                    loss_per_contract = distance_from_center * 100  # $1 per point away from center
                    total_loss = loss_per_contract * num_contracts
                    profit_loss = total_credit_received - total_loss
                    trade_successful = profit_loss > 0
                else:
                    # Max loss zone: spread width ($500 per contract) minus credit received
                    max_loss_per_contract = 500.0 - (credit_per_contract * 100)
                    total_max_loss = max_loss_per_contract * num_contracts
                    profit_loss = -total_max_loss
                    trade_successful = False
            
            else:
                # Unknown decision type
                profit_loss = 0.0
                trade_successful = False
                max_risk_amount = 0.0
                risk_per_contract = 0.0
                num_contracts = 0
                actual_risk_amount = 0.0
        
        except Exception as e:
            logger.error(f"Error determining trade outcome: {str(e)}")
            profit_loss = 0.0
            trade_successful = False
            max_risk_amount = 0.0
            risk_per_contract = 0.0
            num_contracts = 0
            actual_risk_amount = 0.0
        
        return trade_successful, profit_loss, max_risk_amount, risk_per_contract, num_contracts, actual_risk_amount
    
    def process_single_day(self, trading_date: str) -> List[BacktestResult]:
        """
        Process a single trading day
        
        Args:
            trading_date: Date in 'YYYY-MM-DD' format
            
        Returns:
            List of BacktestResult objects (1 for normal trades, 2 for dual trades)
        """
        start_time = datetime.now()
        
        try:
            # Step 1: Get raw data from database
            daily_data = self.db_connector.get_daily_end_of_day_data(trading_date)
            if not daily_data:
                logger.warning(f"No data available for {trading_date}")
                return []
            
            # Step 2: Process bars
            adjusted_bars, closing_price = BarProcessor.process_daily_data(daily_data)
            
            # Step 3: Run decision engine
            # Get narrow threshold (VIX-based if enabled, otherwise default 4.0)
            narrow_threshold = self.get_narrow_conversion_threshold(trading_date) if self.vix_based_threshold else 4.0
            decision_result = analyze_normalized_bars(
                adjusted_bars[0], 
                adjusted_bars[1], 
                adjusted_bars[2],
                narrow_threshold=narrow_threshold
            )
            
            # Step 3.5: Check if this should be a dual trade for butterfly_conversion_latest mode (BEFORE conversion)
            # We need to detect dual trades before conversion to catch all cases that will become narrow
            span3 = decision_result.spans.get('span3', 0.0)
            original_decision = decision_result.decision
            bias = decision_result.bias
            
            # For butterfly_conversion_latest: dual trade ONLY if strategy will actually be narrow after conversion
            will_become_narrow = False
            if self.butterfly_conversion_latest:
                # Get VIX-based threshold for narrow conversions
                narrow_threshold = self.get_narrow_conversion_threshold(trading_date)
                
                # Case 1: Any strategy that will become narrow due to SPAN3 < threshold
                if span3 < narrow_threshold and original_decision in ["bull", "bear", "iron_butterfly"]:
                    will_become_narrow = True
                # Case 2: Strategies that are already narrow from the decision engine
                elif original_decision in ["narrow_bull", "narrow_bear"]:
                    will_become_narrow = True
                # Note: Iron butterflies that don't have SPAN3 < threshold will NOT get dual trades
                # They convert to bull/bear but don't become narrow, so no dual trade
            
            is_dual_trade = will_become_narrow
            
            # Step 3.6: Apply alternate strategy conversion if enabled
            decision_result = self.apply_alternate_strategy_conversion(decision_result, trading_date)
            
            # Step 4: Optimize strikes and get pricing
            execution_price = adjusted_bars[2][3]  # Bar 3 close price
            original_strikes, final_strikes, spread_price, strikes_moved = self.optimize_strikes_and_price(
                decision_result, execution_price
            )
            
            # Skip if trade was filtered out due to insufficient credit or distance
            if spread_price == 0.0 and not strikes_moved and final_strikes == original_strikes:
                logger.info(f"Skipping {trading_date}: trade filtered out by pricing/distance checks")
                return []
            
            # Step 4.5: Check narrow strategy fill opportunities
            narrow_filled = False
            fill_spx_level = 0.0
            fill_time = ""
            narrow_fill_ohlc = []
            narrow_fill_consolidated_ohlc = (0.0, 0.0, 0.0, 0.0)
            
            if decision_result.decision in ["narrow_bull", "narrow_bear"]:
                narrow_filled, fill_spx_level, fill_time, narrow_fill_ohlc, narrow_fill_consolidated_ohlc = self.check_narrow_fill_opportunity(
                    trading_date, decision_result.decision, final_strikes
                )
                
                if narrow_filled:
                    logger.info(f"Narrow strategy {decision_result.decision} FILLED at SPX {fill_spx_level:.2f} at {fill_time}")
                else:
                    logger.info(f"Narrow strategy {decision_result.decision} did not fill - keeping 0 contracts")
            
            # Update target price based on final strikes
            if decision_result.decision in ["bull", "narrow_bull", "BF-bull"]:
                new_target_price = final_strikes[1]  # Short strike for max profit
            elif decision_result.decision in ["bear", "narrow_bear", "BF-bear"]:
                new_target_price = final_strikes[1]  # Short strike for max profit
            elif decision_result.decision == "iron_butterfly":
                new_target_price = final_strikes[1]  # Center strike for max profit
            else:
                new_target_price = decision_result.target_price
            
            # Step 5: Determine trade outcome with actual pricing
            # Note: decision_result already contains converted decision type if alternate strategy is enabled
            trade_successful, profit_loss, max_risk_amount, risk_per_contract, num_contracts, actual_risk_amount = self.determine_trade_outcome(
                decision_result, closing_price, final_strikes, spread_price, narrow_filled
            )
            
            # All strategies should be recorded in output data, even with 0 contracts
            # Only log when we couldn't afford the trade (for debugging)
            if num_contracts == 0 and decision_result.decision not in ["narrow_bull", "narrow_bear"]:
                logger.info(f"Recording {trading_date}: {decision_result.decision} with 0 contracts (insufficient balance)")
            
            # Skip if narrow strategy that didn't fill (already logged above)
            if decision_result.decision in ["narrow_bull", "narrow_bear"] and not narrow_filled:
                # For unfilled narrow strategies, we still record them but with 0 contracts and no P&L
                pass  # Continue to create result for data collection
            
            # Update balance after trade (narrow strategies only affect balance if they filled)
            new_balance = self.current_balance + profit_loss
            
            # Prepare results list (will contain 1 or 2 results for dual trades)
            results = []
            
            # Handle dual trade for butterfly_conversion_latest mode
            secondary_result = None
            if is_dual_trade:
                # Determine the appropriate bias for the secondary Iron Butterfly
                # Based on what the final narrow strategy will be
                if decision_result.decision == "narrow_bull":
                    secondary_bias = 1  # bias up for narrow_bull
                elif decision_result.decision == "narrow_bear":
                    secondary_bias = -1  # bias down for narrow_bear
                else:
                    # This should not happen - dual trades should only be for narrow strategies
                    logger.warning(f"Dual trade detected for non-narrow strategy: {decision_result.decision}")
                    secondary_bias = 1 if "bull" in decision_result.decision else -1
                
                secondary_decision_result = self.create_secondary_iron_butterfly(
                    decision_result, secondary_bias, execution_price
                )
                
                # Price and size the secondary trade
                secondary_original_strikes, secondary_final_strikes, secondary_spread_price, secondary_strikes_moved = self.optimize_strikes_and_price(
                    secondary_decision_result, execution_price
                )
                
                # Determine secondary trade outcome
                secondary_trade_successful, secondary_profit_loss, secondary_max_risk_amount, secondary_risk_per_contract, secondary_num_contracts, secondary_actual_risk_amount = self.determine_trade_outcome(
                    secondary_decision_result, closing_price, secondary_final_strikes, secondary_spread_price, False
                )
                
                # Update balance to account for secondary trade
                new_balance += secondary_profit_loss
                
                # Update reasoning for primary trade to show it's a dual trade
                decision_result.reasoning += f" | DUAL-PRIMARY"
            
            # Step 6: Create result(s)
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Create primary result
            primary_result = BacktestResult(
                trading_date=trading_date,
                decision=decision_result.decision,
                target_price=new_target_price,
                closing_price=closing_price,
                reasoning=decision_result.reasoning,
                bias=decision_result.bias,
                spans=decision_result.spans,
                strikes=decision_result.strikes,
                option_types=decision_result.option_types,
                actions=decision_result.actions,
                close_percentages=decision_result.close_percentages,
                bars=adjusted_bars,
                execution_price=execution_price,
                original_strikes=original_strikes,
                final_strikes=final_strikes,
                spread_price=spread_price,
                strikes_moved=strikes_moved,
                current_balance=self.current_balance,
                max_risk_amount=max_risk_amount,
                risk_per_contract=risk_per_contract,
                num_contracts=num_contracts,
                actual_risk_amount=actual_risk_amount,
                trade_successful=trade_successful,
                profit_loss=profit_loss,
                new_balance=self.current_balance + profit_loss,  # Only include primary P&L in primary result
                narrow_fill_ohlc=narrow_fill_ohlc,
                narrow_fill_consolidated_ohlc=narrow_fill_consolidated_ohlc,
                is_dual_trade=is_dual_trade,
                dual_trade_type="primary" if is_dual_trade else "",
                processing_time=processing_time
            )
            results.append(primary_result)
            
            # Create secondary result if dual trade
            if is_dual_trade and secondary_result is None:  # Create secondary result
                secondary_target_price = secondary_decision_result.target_price
                secondary_result = BacktestResult(
                    trading_date=trading_date,
                    decision=secondary_decision_result.decision,
                    target_price=secondary_target_price,
                    closing_price=closing_price,
                    reasoning=secondary_decision_result.reasoning + " | DUAL-SECONDARY",
                    bias=secondary_decision_result.bias,
                    spans=secondary_decision_result.spans,
                    strikes=secondary_decision_result.strikes,
                    option_types=secondary_decision_result.option_types,
                    actions=secondary_decision_result.actions,
                    close_percentages=secondary_decision_result.close_percentages,
                    bars=adjusted_bars,
                    execution_price=execution_price,
                    original_strikes=secondary_original_strikes,
                    final_strikes=secondary_final_strikes,
                    spread_price=secondary_spread_price,
                    strikes_moved=secondary_strikes_moved,
                    current_balance=self.current_balance + profit_loss,  # Balance after primary trade
                    max_risk_amount=secondary_max_risk_amount,
                    risk_per_contract=secondary_risk_per_contract,
                    num_contracts=secondary_num_contracts,
                    actual_risk_amount=secondary_actual_risk_amount,
                    trade_successful=secondary_trade_successful,
                    profit_loss=secondary_profit_loss,
                    new_balance=new_balance,  # Final balance after both trades
                    narrow_fill_ohlc=[],  # Secondary trades don't have narrow fill data
                    narrow_fill_consolidated_ohlc=(0.0, 0.0, 0.0, 0.0),
                    is_dual_trade=True,
                    dual_trade_type="secondary",
                    processing_time=processing_time
                )
                results.append(secondary_result)
            
            # Update current balance for next trade
            self.current_balance = new_balance
            
            # Create detailed log message
            fill_info = ""
            if decision_result.decision in ["narrow_bull", "narrow_bear"]:
                if narrow_filled:
                    fill_info = f" FILLED@{fill_spx_level:.1f}({fill_time})"
                else:
                    fill_info = " NO_FILL"
            
            # Step 7: Process extra butterfly trade if enabled
            extra_butterfly_result = None
            extra_butterfly_trade_successful = False
            extra_butterfly_profit_loss = 0.0
            extra_butterfly_filled = False
            
            if self.extra_butterfly and self.extra_butterfly_engine:
                try:
                    # Get SPX prices at 15:51:30 and 15:56:30
                    spx_1530 = self.db_connector.get_spx_price_at_time(trading_date, "15:51:30")
                    spx_1556 = self.db_connector.get_spx_price_at_time(trading_date, "15:56:30")
                    
                    if spx_1530 is not None and spx_1556 is not None:
                        # Process extra butterfly logic
                        extra_butterfly_result = self.extra_butterfly_engine.process_extra_butterfly(
                            original_target=new_target_price,
                            spx_1530=spx_1530,
                            spx_1556=spx_1556,
                            strategy_type=decision_result.decision
                        )
                        
                        if extra_butterfly_result and extra_butterfly_result.should_trade:
                            # Calculate position sizing for extra butterfly using 7% risk management
                            if extra_butterfly_result.strategy_type == "iron_butterfly":
                                # Calculate position size using iron butterfly risk management
                                spread_price = -3.00  # $3.00 credit for extra butterfly
                                max_risk_amount, risk_per_contract, num_contracts, actual_risk_amount = self.calculate_position_size(
                                    "iron_butterfly", spread_price, filled=True
                                )
                                
                                if num_contracts > 0:
                                    # Calculate P&L based on position size
                                    wing_distance = 5.0  # 5-point wings
                                    credit_per_contract = 3.00  # Fixed credit for extra butterfly
                                    max_loss_per_contract = wing_distance - credit_per_contract  # $2.00 max loss per contract
                                    
                                    # Determine if trade is successful based on closing price
                                    if (closing_price >= extra_butterfly_result.strikes[0] and 
                                        closing_price <= extra_butterfly_result.strikes[2]):
                                        # Price stays within wings - profit
                                        extra_butterfly_trade_successful = True
                                        extra_butterfly_profit_loss = credit_per_contract * 100 * num_contracts  # Convert to dollars
                                    else:
                                        # Price moves beyond wings - loss
                                        extra_butterfly_trade_successful = False
                                        extra_butterfly_profit_loss = -(max_loss_per_contract * 100 * num_contracts)  # Convert to dollars
                                    
                                    extra_butterfly_filled = True
                                    logger.info(f"Extra butterfly: {extra_butterfly_result.case} "
                                               f"strikes={extra_butterfly_result.strikes} "
                                               f"contracts={num_contracts} "
                                               f"credit=${credit_per_contract:.2f} per contract "
                                               f"{'WIN' if extra_butterfly_trade_successful else 'LOSS'} "
                                               f"P&L=${extra_butterfly_profit_loss:.2f}")
                                    
                                    # Store position sizing information
                                    extra_butterfly_num_contracts = num_contracts
                                    extra_butterfly_actual_risk = actual_risk_amount
                                else:
                                    # No contracts could be allocated
                                    extra_butterfly_filled = False
                                    extra_butterfly_trade_successful = False
                                    extra_butterfly_profit_loss = 0.0
                                    logger.info(f"Extra butterfly: {extra_butterfly_result.case} - No contracts allocated (insufficient balance)")
                                    
                                    # Store position sizing information
                                    extra_butterfly_num_contracts = 0
                                    extra_butterfly_actual_risk = 0.0
                            else:
                                # Other strategy types (bull/bear spreads) - simplified for now
                                extra_butterfly_filled = True
                                extra_butterfly_trade_successful = False  # Default to loss for now
                                extra_butterfly_profit_loss = -2.20  # Fixed loss for now
                                logger.info(f"Extra butterfly: {extra_butterfly_result.case} "
                                           f"strikes={extra_butterfly_result.strikes} "
                                           f"P&L=${extra_butterfly_profit_loss:.2f}")
                                
                                # Store position sizing information for non-iron butterfly
                                extra_butterfly_num_contracts = 1
                                extra_butterfly_actual_risk = 2.20
                        else:
                            logger.info(f"Extra butterfly: No trade (case: {extra_butterfly_result.case if extra_butterfly_result else 'No data'})")
                    else:
                        logger.warning(f"Extra butterfly: Missing SPX data for {trading_date}")
                        
                except Exception as e:
                    logger.error(f"Extra butterfly error for {trading_date}: {str(e)}")
            
            # Update results with extra butterfly information
            for result in results:
                result.extra_butterfly_result = extra_butterfly_result
                result.extra_butterfly_trade_successful = extra_butterfly_trade_successful
                result.extra_butterfly_profit_loss = extra_butterfly_profit_loss
                result.extra_butterfly_filled = extra_butterfly_filled
                result.extra_butterfly_num_contracts = extra_butterfly_num_contracts if 'extra_butterfly_num_contracts' in locals() else 0
                result.extra_butterfly_actual_risk = extra_butterfly_actual_risk if 'extra_butterfly_actual_risk' in locals() else 0.0
                
                # Update balance to include extra butterfly P&L
                if extra_butterfly_filled:
                    result.new_balance += extra_butterfly_profit_loss
            
            if is_dual_trade:
                logger.info(f"Processed {trading_date}: DUAL TRADE "
                           f"primary={decision_result.decision}{fill_info} P&L=${profit_loss:.0f} "
                           f"secondary={secondary_decision_result.decision} P&L=${secondary_profit_loss:.0f} "
                           f"combined_balance=${new_balance:.0f}")
            else:
                logger.info(f"Processed {trading_date}: {decision_result.decision}{fill_info} "
                           f"exec=${execution_price:.1f} "
                           f"strikes={final_strikes} "
                           f"price=${spread_price:.2f} "
                           f"contracts={num_contracts} "
                           f"risk=${actual_risk_amount:.0f} "
                           f"{'MOVED' if strikes_moved else 'ORIG'} "
                           f"target=${new_target_price:.0f} "
                           f"close=${closing_price:.0f} "
                           f"{'WIN' if trade_successful else 'LOSS'} "
                           f"P&L=${profit_loss:.0f} "
                           f"balance=${new_balance:.0f}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error processing {trading_date}: {str(e)}")
            return []
    
    def run_backtest(self) -> List[BacktestResult]:
        """
        Run the complete backtest
        
        Returns:
            List of BacktestResult objects
        """
        logger.info("Starting historical backtest...")
        
        # Reset balance to starting balance
        self.current_balance = self.starting_balance
        logger.info(f"Starting balance: ${self.starting_balance:.0f}, Max risk per trade: {self.max_risk_per_trade_pc:.1%}")
        
        with self.db_connector:
            # Get trading dates
            trading_dates = self.db_connector.get_trading_dates(self.start_date, self.end_date)
            logger.info(f"Found {len(trading_dates)} trading dates to process")
            
            if not trading_dates:
                logger.warning("No trading dates found in the specified range")
                return []
            
            # Cache all trading dates for gap detection
            self.all_trading_dates = trading_dates
            
            # Process each day
            self.results = []
            skipped_count = 0
            skipped_reasons = {'monday': 0, 'post_gap': 0}
            
            for i, trading_date in enumerate(trading_dates):
                # Check if we should skip this day (Monday mode)
                if self.skip_monday:
                    is_skip, reason = self.is_monday_or_post_gap(trading_date)
                    if is_skip:
                        skipped_count += 1
                        skipped_reasons[reason] += 1
                        logger.info(f"Skipping {i+1}/{len(trading_dates)}: {trading_date} (reason: {reason})")
                        continue
                
                logger.info(f"Processing {i+1}/{len(trading_dates)}: {trading_date}")
                
                day_results = self.process_single_day(trading_date)
                if day_results:
                    self.results.extend(day_results)  # Add all results from this day
                
                # Progress reporting
                if (i + 1) % 10 == 0:
                    logger.info(f"Completed {i+1}/{len(trading_dates)} days")
        
        # Log skipping summary
        if self.skip_monday and skipped_count > 0:
            logger.info(f"Monday mode: Skipped {skipped_count} days total "
                       f"(Mondays: {skipped_reasons['monday']}, Post-gap: {skipped_reasons['post_gap']})")
        
        logger.info(f"Backtest completed. Processed {len(self.results)} days successfully.")
        return self.results
    
    def run_backtest_on_dates(self, trading_dates: List[str]) -> List[BacktestResult]:
        """
        Run the backtest on a specific list of trading dates (for random sampling)
        
        Args:
            trading_dates: List of trading dates to process (in YYYY-MM-DD format)
            
        Returns:
            List of BacktestResult objects
        """
        logger.info(f"Starting backtest on {len(trading_dates)} randomly sampled trading days...")
        
        # Reset balance to starting balance
        self.current_balance = self.starting_balance
        
        with self.db_connector:
            # Process each day
            self.results = []
            skipped_count = 0
            skipped_reasons = {'monday': 0, 'post_gap': 0}
            
            for i, trading_date in enumerate(trading_dates):
                # Check if we should skip this day (Monday mode)
                if self.skip_monday:
                    is_skip, reason = self.is_monday_or_post_gap(trading_date)
                    if is_skip:
                        skipped_count += 1
                        skipped_reasons[reason] += 1
                        logger.debug(f"Skipping {i+1}/{len(trading_dates)}: {trading_date} (reason: {reason})")
                        continue
                
                logger.debug(f"Processing {i+1}/{len(trading_dates)}: {trading_date}")
                
                day_results = self.process_single_day(trading_date)
                if day_results:
                    self.results.extend(day_results)  # Add all results from this day
        
        # Log skipping summary
        if self.skip_monday and skipped_count > 0:
            logger.debug(f"Monday mode: Skipped {skipped_count} days total "
                        f"(Mondays: {skipped_reasons['monday']}, Post-gap: {skipped_reasons['post_gap']})")
        
        logger.info(f"Random backtest completed. Processed {len(self.results)} days successfully.")
        return self.results
    
    def generate_summary_stats(self) -> Dict[str, Any]:
        """Generate summary statistics from the backtest results"""
        if not self.results:
            return {}
        
        # Filter for actual trades only (num_contracts > 0)
        actual_trades = [r for r in self.results if r.num_contracts > 0]
        total_decisions = len(self.results)
        total_trades = len(actual_trades)
        
        if total_trades == 0:
            return {'total_decisions': total_decisions, 'total_trades': 0}
        
        winning_trades = sum(1 for r in actual_trades if r.trade_successful)
        losing_trades = total_trades - winning_trades
        win_rate = winning_trades / total_trades * 100
        
        total_pnl = sum(r.profit_loss for r in actual_trades)
        average_pnl = total_pnl / total_trades
        
        # Balance tracking (use all results for balance progression)
        starting_balance = self.starting_balance
        ending_balance = self.results[-1].new_balance if self.results else starting_balance
        total_return = ((ending_balance - starting_balance) / starting_balance) * 100
        
        # Calculate max drawdown (use all results for balance progression)
        peak_balance = starting_balance
        max_drawdown = 0.0
        max_drawdown_pct = 0.0
        
        for result in self.results:
            current_balance = result.new_balance
            
            # Update peak if we hit a new high
            if current_balance > peak_balance:
                peak_balance = current_balance
            
            # Calculate current drawdown
            drawdown = peak_balance - current_balance
            drawdown_pct = (drawdown / peak_balance) * 100 if peak_balance > 0 else 0.0
            
            # Update max drawdown
            if drawdown > max_drawdown:
                max_drawdown = drawdown
            if drawdown_pct > max_drawdown_pct:
                max_drawdown_pct = drawdown_pct
        
        # Calculate win/lose streaks (only for actual trades)
        current_win_streak = 0
        current_lose_streak = 0
        max_win_streak = 0
        max_lose_streak = 0
        
        for result in actual_trades:
            if result.trade_successful:
                # Win - increment win streak, reset lose streak
                current_win_streak += 1
                current_lose_streak = 0
                max_win_streak = max(max_win_streak, current_win_streak)
            else:
                # Loss - increment lose streak, reset win streak
                current_lose_streak += 1
                current_win_streak = 0
                max_lose_streak = max(max_lose_streak, current_lose_streak)
        
        # Position sizing statistics (only for actual trades)
        avg_contracts = sum(r.num_contracts for r in actual_trades) / total_trades
        avg_risk_amount = sum(r.actual_risk_amount for r in actual_trades) / total_trades
        avg_risk_pct = (avg_risk_amount / starting_balance) * 100  # Approximate since balance changes
        
        # Separate winning and losing trades (only actual trades)
        winning_results = [r for r in actual_trades if r.trade_successful]
        losing_results = [r for r in actual_trades if not r.trade_successful]
        
        # Calculate average win/loss amounts
        avg_win_amount = sum(r.profit_loss for r in winning_results) / len(winning_results) if winning_results else 0
        avg_loss_amount = sum(r.profit_loss for r in losing_results) / len(losing_results) if losing_results else 0
        
        # Calculate win/loss percentage based on actual capital at risk per trade
        total_win_pct = 0
        total_loss_pct = 0
        
        for result in winning_results:
            # Calculate % return based on actual risk taken
            if result.actual_risk_amount > 0:
                win_pct = (result.profit_loss / result.actual_risk_amount) * 100
                total_win_pct += win_pct
        
        for result in losing_results:
            # Calculate % loss based on actual risk taken
            if result.actual_risk_amount > 0:
                loss_pct = abs(result.profit_loss / result.actual_risk_amount) * 100
                total_loss_pct += loss_pct
        
        avg_win_pct = total_win_pct / len(winning_results) if winning_results else 0
        avg_loss_pct = total_loss_pct / len(losing_results) if losing_results else 0
        
        # Decision breakdown (separate counts for decisions vs actual trades)
        decision_counts_all = {}  # All decisions including no-fills
        decision_counts_filled = {}  # Only filled trades
        for result in self.results:
            decision_counts_all[result.decision] = decision_counts_all.get(result.decision, 0) + 1
        for result in actual_trades:
            decision_counts_filled[result.decision] = decision_counts_filled.get(result.decision, 0) + 1
        
        # Win rates by decision type (only for filled trades)
        decision_win_rates = {}
        decision_profit_loss_stats = {}  # New: strategy-specific P&L metrics
        
        for decision in decision_counts_filled.keys():
            decision_results = [r for r in actual_trades if r.decision == decision]
            decision_wins = sum(1 for r in decision_results if r.trade_successful)
            decision_win_rates[decision] = decision_wins / len(decision_results) * 100 if decision_results else 0
            
            # Calculate strategy-specific profit/loss metrics
            winning_results = [r for r in decision_results if r.trade_successful]
            losing_results = [r for r in decision_results if not r.trade_successful]
            
            # Average win/loss amounts
            avg_win_amount = sum(r.profit_loss for r in winning_results) / len(winning_results) if winning_results else 0
            avg_loss_amount = sum(r.profit_loss for r in losing_results) / len(losing_results) if losing_results else 0
            
            # Average win/loss percentages (based on actual risk taken)
            total_win_pct = 0
            for result in winning_results:
                if result.actual_risk_amount > 0:
                    win_pct = (result.profit_loss / result.actual_risk_amount) * 100
                    total_win_pct += win_pct
            avg_win_pct = total_win_pct / len(winning_results) if winning_results else 0
            
            total_loss_pct = 0
            for result in losing_results:
                if result.actual_risk_amount > 0:
                    loss_pct = abs(result.profit_loss / result.actual_risk_amount) * 100
                    total_loss_pct += loss_pct
            avg_loss_pct = total_loss_pct / len(losing_results) if losing_results else 0
            
            decision_profit_loss_stats[decision] = {
                'avg_win_amount': avg_win_amount,
                'avg_loss_amount': avg_loss_amount,
                'avg_win_pct': avg_win_pct,
                'avg_loss_pct': avg_loss_pct,
                'total_trades': len(decision_results),
                'winning_trades': len(winning_results),
                'losing_trades': len(losing_results)
            }
        
        # Generate monthly breakdown
        monthly_breakdown = self.generate_monthly_breakdown()
        
        # Extra butterfly statistics
        extra_butterfly_stats = {}
        if self.extra_butterfly:
            extra_butterfly_trades = [r for r in self.results if hasattr(r, 'extra_butterfly_filled') and r.extra_butterfly_filled]
            
            if extra_butterfly_trades:
                extra_butterfly_wins = sum(1 for r in extra_butterfly_trades if getattr(r, 'extra_butterfly_trade_successful', False))
                extra_butterfly_total_pnl = sum(getattr(r, 'extra_butterfly_profit_loss', 0.0) for r in extra_butterfly_trades)
                extra_butterfly_avg_pnl = extra_butterfly_total_pnl / len(extra_butterfly_trades)
                extra_butterfly_win_rate = (extra_butterfly_wins / len(extra_butterfly_trades)) * 100
                
                # Count by case type
                case_counts = {}
                for r in extra_butterfly_trades:
                    case = ""
                    if hasattr(r, 'extra_butterfly_result') and r.extra_butterfly_result:
                        case = r.extra_butterfly_result.case
                    case_counts[case] = case_counts.get(case, 0) + 1
                
                extra_butterfly_stats = {
                    'total_trades': len(extra_butterfly_trades),
                    'winning_trades': extra_butterfly_wins,
                    'losing_trades': len(extra_butterfly_trades) - extra_butterfly_wins,
                    'win_rate_pct': extra_butterfly_win_rate,
                    'total_pnl': extra_butterfly_total_pnl,
                    'avg_pnl': extra_butterfly_avg_pnl,
                    'case_counts': case_counts
                }
            else:
                extra_butterfly_stats = {
                    'total_trades': 0,
                    'winning_trades': 0,
                    'losing_trades': 0,
                    'win_rate_pct': 0,
                    'total_pnl': 0,
                    'avg_pnl': 0,
                    'case_counts': {}
                }
        
        return {
            'total_decisions': total_decisions,  # All decisions including no-fills
            'total_trades': total_trades,  # Only filled trades
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate_pct': win_rate,
            'total_pnl': total_pnl,
            'average_pnl': average_pnl,
            'starting_balance': starting_balance,
            'ending_balance': ending_balance,
            'total_return_pct': total_return,
            'avg_contracts_per_trade': avg_contracts,
            'avg_risk_amount_per_trade': avg_risk_amount,
            'avg_risk_pct_per_trade': avg_risk_pct,
            'max_risk_per_trade_pct': self.max_risk_per_trade_pc * 100,
            'avg_win_amount': avg_win_amount,
            'avg_loss_amount': avg_loss_amount,
            'avg_win_pct': avg_win_pct,
            'avg_loss_pct': avg_loss_pct,
            'decision_counts': decision_counts_filled,  # Filled trades by decision
            'decision_counts_all': decision_counts_all,  # All decisions including no-fills
            'decision_win_rates': decision_win_rates,
            'decision_profit_loss_stats': decision_profit_loss_stats,
            'volatility': self.volatility,
            'simple_mode': self.simple_mode,
            'skip_monday': self.skip_monday,
            'alternate_strategy': self.alternate_strategy,
            'butterfly_conversion_only': self.butterfly_conversion_only,
            'butterfly_narrow_conversions': self.butterfly_narrow_conversions,
            'butterfly_conversion_with_narrows': self.butterfly_conversion_with_narrows,
            'extra_butterfly': self.extra_butterfly,
            'extra_butterfly_stats': extra_butterfly_stats,
            'date_range': {
                'start': self.results[0].trading_date if self.results else None,
                'end': self.results[-1].trading_date if self.results else None
            },
            'max_drawdown': max_drawdown,
            'max_drawdown_pct': max_drawdown_pct,
            'max_win_streak': max_win_streak,
            'max_lose_streak': max_lose_streak,
            'monthly_breakdown': monthly_breakdown,
            'decision_profit_loss_stats': decision_profit_loss_stats
        }
    
    def generate_monthly_breakdown(self) -> Dict[str, Any]:
        """
        Generate monthly performance breakdown
        
        Returns:
            Dictionary with monthly statistics
        """
        if not self.results:
            return {}
        
        from datetime import datetime
        from collections import defaultdict
        
        # Group results by year-month
        monthly_results = defaultdict(list)
        
        for result in self.results:
            # Parse trading date (format: YYYY-MM-DD)
            trade_date = datetime.strptime(result.trading_date, '%Y-%m-%d')
            month_key = f"{trade_date.year}-{trade_date.month:02d}"
            monthly_results[month_key].append(result)
        
        # Calculate stats for each month
        monthly_stats = {}
        running_balance = self.starting_balance
        
        for month_key in sorted(monthly_results.keys()):
            month_results = monthly_results[month_key]
            # Filter for actual filled trades only
            month_trades = [r for r in month_results if r.num_contracts > 0]
            
            # Get starting balance for this month
            month_start_balance = running_balance
            
            # Calculate month stats (only for filled trades)
            total_trades = len(month_trades)
            winning_trades = sum(1 for r in month_trades if r.trade_successful)
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
            
            month_pnl = sum(r.profit_loss for r in month_trades)
            # Use last result (decision or trade) for balance progression
            month_end_balance = month_results[-1].new_balance if month_results else month_start_balance
            
            # Calculate monthly return
            monthly_return = ((month_end_balance - month_start_balance) / month_start_balance * 100) if month_start_balance > 0 else 0
            
            # Parse month for display
            year, month = month_key.split('-')
            month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                          'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            month_display = f"{month_names[int(month)-1]} {year}"
            
            monthly_stats[month_key] = {
                'display_name': month_display,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': total_trades - winning_trades,
                'win_rate_pct': win_rate,
                'month_pnl': month_pnl,
                'month_start_balance': month_start_balance,
                'month_end_balance': month_end_balance,
                'monthly_return_pct': monthly_return
            }
            
            # Update running balance for next month
            running_balance = month_end_balance
        
        return monthly_stats

    def generate_monday_comparison(self) -> Dict[str, Any]:
        """
        Generate comparison statistics for Monday/post-gap vs other days
        
        Returns:
            Dictionary with comparison statistics
        """
        if not self.results:
            return {}
        
        # Separate results by Monday/post-gap status (only actual trades)
        actual_trades = [r for r in self.results if r.num_contracts > 0]
        monday_results = []
        other_results = []
        
        for result in actual_trades:
            is_post_gap, reason = self.is_monday_or_post_gap(result.trading_date)
            if is_post_gap:
                monday_results.append(result)
            else:
                other_results.append(result)
        
        def calculate_stats(results_list, name):
            if not results_list:
                return {
                    'name': name,
                    'total_trades': 0,
                    'win_rate_pct': 0.0,
                    'total_pnl': 0.0,
                    'avg_pnl': 0.0
                }
            
            total = len(results_list)
            wins = sum(1 for r in results_list if r.trade_successful)
            win_rate = wins / total * 100
            total_pnl = sum(r.profit_loss for r in results_list)
            avg_pnl = total_pnl / total
            
            return {
                'name': name,
                'total_trades': total,
                'winning_trades': wins,
                'losing_trades': total - wins,
                'win_rate_pct': win_rate,
                'total_pnl': total_pnl,
                'avg_pnl': avg_pnl
            }
        
        monday_stats = calculate_stats(monday_results, 'Monday/Post-Gap')
        other_stats = calculate_stats(other_results, 'Other Days')
        
        # Calculate differences
        win_rate_diff = monday_stats['win_rate_pct'] - other_stats['win_rate_pct']
        avg_pnl_diff = monday_stats['avg_pnl'] - other_stats['avg_pnl']
        
        return {
            'monday_post_gap': monday_stats,
            'other_days': other_stats,
            'comparison': {
                'win_rate_difference': win_rate_diff,
                'avg_pnl_difference': avg_pnl_diff,
                'monday_better_win_rate': win_rate_diff > 0,
                'monday_better_pnl': avg_pnl_diff > 0
            }
        }
    
    def save_results_to_csv(self, filename: str = None, output_dir: str = "backtesting"):
        """Save detailed results to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            vol_str = f"{int(self.volatility * 100):02d}"  # Convert 0.45 to "45"
            
            if self.simple_mode:
                # Include simple mode parameters in filename
                spread_str = f"{self.simple_spread_cost:.2f}".replace(".", "")  # 2.20 -> "220"
                butterfly_str = f"{self.simple_butterfly_credit:.2f}".replace(".", "")  # 3.50 -> "350"
                threshold_str = f"{self.simple_itm_threshold:.2f}".replace(".", "")  # 2.50 -> "250"
                mode_str = "simple"
                params_str = f"s{spread_str}_b{butterfly_str}_t{threshold_str}"
            else:
                mode_str = "vol"
                params_str = f"{vol_str}"
            
            # Add Monday mode and alternate strategy suffix
            monday_suffix = "_nomonday" if self.skip_monday else ""
            alt_suffix = ""
            if self.alternate_strategy:
                alt_suffix = "_alt"
            elif self.butterfly_conversion_only:
                alt_suffix = "_bf_only"
            elif self.butterfly_narrow_conversions:
                alt_suffix = "_bf_narrow"
            elif self.butterfly_conversion_with_narrows:
                alt_suffix = "_bf_conv_narrow"
            elif self.butterfly_conversion_latest:
                alt_suffix = "_bf_conv_latest"
            
            # Add extra butterfly suffix
            extra_suffix = "_extra_butterfly" if self.extra_butterfly else ""
            
            filename = f"{output_dir}/backtest_results_{mode_str}_{params_str}{monday_suffix}{alt_suffix}{extra_suffix}_{timestamp}.csv"
        
        with open(filename, 'w', newline='') as csvfile:
            fieldnames = [
                'trading_date', 'decision', 'execution_price', 'original_strikes', 'final_strikes', 
                'spread_price', 'strikes_moved', 'target_price', 'closing_price',
                'current_balance', 'max_risk_amount', 'risk_per_contract', 'num_contracts', 'actual_risk_amount',
                'trade_successful', 'profit_loss', 'new_balance',
                'reasoning', 'bias', 'span1', 'span2', 'span3', 
                'close_pct_bar1', 'close_pct_bar2', 'close_pct_bar3',
                'bar1_ohlc', 'bar2_ohlc', 'bar3_ohlc', 'processing_time',
                'is_dual_trade', 'dual_trade_type',
                # Extra butterfly fields
                'extra_butterfly_filled', 'extra_butterfly_case', 'extra_butterfly_strikes', 
                'extra_butterfly_profit_loss', 'extra_butterfly_trade_successful'
            ]
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in self.results:
                # Extract extra butterfly info
                extra_case = ""
                extra_strikes = ""
                if hasattr(result, 'extra_butterfly_result') and result.extra_butterfly_result:
                    extra_case = result.extra_butterfly_result.case
                    extra_strikes = str(result.extra_butterfly_result.strikes) if result.extra_butterfly_result.strikes else ""
                
                writer.writerow({
                    'trading_date': result.trading_date,
                    'decision': result.decision,
                    'execution_price': result.execution_price,
                    'original_strikes': f"{result.original_strikes}",
                    'final_strikes': f"{result.final_strikes}",
                    'spread_price': result.spread_price,
                    'strikes_moved': result.strikes_moved,
                    'target_price': result.target_price,
                    'closing_price': result.closing_price,
                    'current_balance': result.current_balance,
                    'max_risk_amount': result.max_risk_amount,
                    'risk_per_contract': result.risk_per_contract,
                    'num_contracts': result.num_contracts,
                    'actual_risk_amount': result.actual_risk_amount,
                    'trade_successful': result.trade_successful,
                    'profit_loss': result.profit_loss,
                    'new_balance': result.new_balance,
                    'reasoning': result.reasoning,
                    'bias': result.bias,
                    'span1': result.spans['span1'],
                    'span2': result.spans['span2'],
                    'span3': result.spans['span3'],
                    'close_pct_bar1': result.close_percentages[0],
                    'close_pct_bar2': result.close_percentages[1],
                    'close_pct_bar3': result.close_percentages[2],
                    'bar1_ohlc': f"{result.bars[0]}",
                    'bar2_ohlc': f"{result.bars[1]}",
                    'bar3_ohlc': f"{result.bars[2]}",
                    'processing_time': result.processing_time,
                    'is_dual_trade': result.is_dual_trade,
                    'dual_trade_type': result.dual_trade_type,
                    # Extra butterfly data
                    'extra_butterfly_filled': getattr(result, 'extra_butterfly_filled', False),
                    'extra_butterfly_case': extra_case,
                    'extra_butterfly_strikes': extra_strikes,
                    'extra_butterfly_profit_loss': getattr(result, 'extra_butterfly_profit_loss', 0.0),
                    'extra_butterfly_trade_successful': getattr(result, 'extra_butterfly_trade_successful', False)
                })
        
        logger.info(f"Results saved to {filename}")
    
    def save_summary_to_json(self, filename: str = None, output_dir: str = "backtesting"):
        """Save summary statistics to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            vol_str = f"{int(self.volatility * 100):02d}"  # Convert 0.45 to "45"
            
            if self.simple_mode:
                # Include simple mode parameters in filename
                spread_str = f"{self.simple_spread_cost:.2f}".replace(".", "")  # 2.20 -> "220"
                butterfly_str = f"{self.simple_butterfly_credit:.2f}".replace(".", "")  # 3.50 -> "350"
                threshold_str = f"{self.simple_itm_threshold:.2f}".replace(".", "")  # 2.50 -> "250"
                mode_str = "simple"
                params_str = f"s{spread_str}_b{butterfly_str}_t{threshold_str}"
            else:
                mode_str = "vol"
                params_str = f"{vol_str}"
            
            # Add Monday mode and alternate strategy suffix
            monday_suffix = "_nomonday" if self.skip_monday else ""
            alt_suffix = ""
            if self.alternate_strategy:
                alt_suffix = "_alt"
            elif self.butterfly_conversion_only:
                alt_suffix = "_bf_only"
            elif self.butterfly_narrow_conversions:
                alt_suffix = "_bf_narrow"
            elif self.butterfly_conversion_with_narrows:
                alt_suffix = "_bf_conv_narrow"
            elif self.butterfly_conversion_latest:
                alt_suffix = "_bf_conv_latest"
            
            # Add extra butterfly suffix
            extra_suffix = "_extra_butterfly" if self.extra_butterfly else ""
            
            filename = f"{output_dir}/backtest_summary_{mode_str}_{params_str}{monday_suffix}{alt_suffix}{extra_suffix}_{timestamp}.json"
        
        summary = self.generate_summary_stats()
        
        with open(filename, 'w') as jsonfile:
            json.dump(summary, jsonfile, indent=2)
        
        logger.info(f"Summary saved to {filename}")
    
    def save_results_to_database(self, run_name: str = None) -> str:
        """
        Save backtest results to SQL Server database
        Uses environment variables: DB_IP, DB_NAME, DB_USER, DB_PASSWORD
        
        Args:
            run_name: Optional user-friendly name for this run
            
        Returns:
            run_id of the created backtest run
        """
        if not DATABASE_OUTPUT_AVAILABLE:
            logger.error("Database output not available. Install pyodbc: pip install pyodbc")
            return None
            
        if not self.results:
            logger.warning("No results to save to database")
            return None
        
        try:
            db_output = BacktestDatabaseOutput()  # No connection string needed
            run_id = db_output.save_backtest_results(self, self.results, run_name)
            logger.info(f"Results saved to database with run_id: {run_id}")
            return run_id
        except Exception as e:
            logger.error(f"Failed to save results to database: {str(e)}")
            return None
    
    def print_summary(self):
        """Print summary statistics to console"""
        summary = self.generate_summary_stats()
        
        if not summary:
            print("No results to summarize.")
            return
        
        print("\n" + "="*60)
        print("HISTORICAL BACKTEST SUMMARY")
        print("="*60)
        print(f"Date Range: {summary['date_range']['start']} to {summary['date_range']['end']}")
        print(f"Volatility: {summary['volatility']:.1%}")
        print(f"Pricing Mode: {self.pricing_mode.upper()}")
        if self.simple_mode:
            print(f"Simple Mode: ENABLED (Simplified strike selection)")
        if self.alternate_strategy:
            print(f"Alternate Strategy: ENABLED (narrow_bull→bull, narrow_bear→bear, iron_butterfly→bull/bear)")
        elif self.butterfly_conversion_only:
            print(f"Butterfly Conversion: ENABLED (iron_butterfly→bull/bear, narrow strategies unchanged)")
        elif self.butterfly_narrow_conversions:
            print(f"Butterfly-Narrow Conversions: ENABLED (iron_butterfly→bull/bear, but narrow_bull/narrow_bear if span3 < 4)")
        elif self.butterfly_conversion_with_narrows:
            print(f"Butterfly Conversion with Narrows: ENABLED (iron_butterfly→bull/bear based on 1st bar close >=66% and span3 threshold of 4)")
        elif self.butterfly_conversion_latest:
            print(f"Butterfly Conversion Latest: ENABLED (iron_butterfly bias=0→bull/bear based on SPAN1 close >66%, narrow strategies place dual trades)")
        
        # Add extra butterfly mode info
        if self.extra_butterfly:
            print(f"Extra Butterfly Mode: ENABLED (additional iron butterfly at 15:56:30 based on price movement)")
        
        # Add VIX-based threshold info
        if self.vix_based_threshold:
            print(f"VIX-Based Threshold: ENABLED (4.0 when VIX > 16.5, 6.0 when VIX <= 16.5)")
        
        print(f"Risk Management:")
        print(f"  Default: {self.max_risk_per_trade_pc*100:.1f}% of balance per trade")
        if self.max_risk_bull_pc != self.max_risk_per_trade_pc:
            print(f"  Bull strategies: {self.max_risk_bull_pc*100:.1f}% of balance")
        if self.max_risk_bear_pc != self.max_risk_per_trade_pc:
            print(f"  Bear strategies: {self.max_risk_bear_pc*100:.1f}% of balance")
        if self.max_risk_iron_butterfly_pc != self.max_risk_per_trade_pc:
            print(f"  Iron butterfly: {self.max_risk_iron_butterfly_pc*100:.1f}% of balance")
        
        if self.pricing_mode == "fixed":
            print(f"Fixed Pricing: ${self.simple_spread_cost:.2f} spreads, -$3.40 butterflies")
        elif self.pricing_mode == "basic":
            print(f"Basic Linear Pricing: Bull/Bear spreads use linear model, Iron butterflies -$3.40")
        else:
            print(f"Advanced Black-Scholes Pricing: Volatility {self.volatility*100:.0f}%")
        if self.skip_monday:
            print("Monday Mode: ENABLED (Skipping Monday/post-gap trades)")
        
        print(f"\n--- ACCOUNT PERFORMANCE ---")
        print(f"Starting Balance: ${summary['starting_balance']:.0f}")
        print(f"Ending Balance: ${summary['ending_balance']:.0f}")
        print(f"Total Return: {summary['total_return_pct']:+.1f}%")
        print(f"Total P&L: ${summary['total_pnl']:+.0f}")
        print(f"Max Drawdown: ${summary['max_drawdown']:.0f} ({summary['max_drawdown_pct']:.1f}%)")
        
        print(f"\n--- TRADE STATISTICS ---")
        # Show both decisions and actual trades if they differ
        if summary.get('total_decisions', 0) != summary['total_trades']:
            print(f"Total Decisions: {summary['total_decisions']} ({summary['total_decisions'] - summary['total_trades']} did not fill)")
        print(f"Total Trades: {summary['total_trades']}")
        print(f"Winning Trades: {summary['winning_trades']}")
        print(f"Losing Trades: {summary['losing_trades']}")
        print(f"Win Rate: {summary['win_rate_pct']:.1f}%")
        print(f"Average P&L per Trade: ${summary['average_pnl']:+.0f}")
        print(f"Average Win: ${summary['avg_win_amount']:.0f} ({summary['avg_win_pct']:.1f}%)")
        print(f"Average Loss: ${summary['avg_loss_amount']:+.0f} ({summary['avg_loss_pct']:.1f}%)")
        print(f"Max Win Streak: {summary['max_win_streak']} trades")
        print(f"Max Lose Streak: {summary['max_lose_streak']} trades")
        
        print(f"\n--- POSITION SIZING ---")
        print(f"Max Risk per Trade: {summary['max_risk_per_trade_pct']:.1f}% of balance")
        print(f"Avg Contracts per Trade: {summary['avg_contracts_per_trade']:.1f}")
        print(f"Avg Risk per Trade: ${summary['avg_risk_amount_per_trade']:.0f} ({summary['avg_risk_pct_per_trade']:.1f}% of starting balance)")
        
        print("\nDecision Breakdown:")
        # Show both decision counts and filled trade counts if available
        if summary.get('decision_counts_all'):
            for decision in summary['decision_counts_all'].keys():
                decisions_count = summary['decision_counts_all'][decision]
                filled_count = summary['decision_counts'].get(decision, 0)
                win_rate = summary['decision_win_rates'].get(decision, 0)
                
                # Get strategy-specific P&L stats
                pnl_stats = summary.get('decision_profit_loss_stats', {}).get(decision, {})
                
                if decisions_count != filled_count:
                    no_fill_count = decisions_count - filled_count
                    print(f"  {decision}: {filled_count} trades, {no_fill_count} no-fills ({win_rate:.1f}% win rate)")
                else:
                    print(f"  {decision}: {filled_count} trades ({win_rate:.1f}% win rate)")
                
                # Display strategy-specific profit/loss metrics if available
                if pnl_stats and filled_count > 0:
                    avg_win_amt = pnl_stats.get('avg_win_amount', 0)
                    avg_loss_amt = pnl_stats.get('avg_loss_amount', 0)
                    avg_win_pct = pnl_stats.get('avg_win_pct', 0)
                    avg_loss_pct = pnl_stats.get('avg_loss_pct', 0)
                    
                    if avg_win_amt != 0 or avg_loss_amt != 0:
                        print(f"    - Average Win: ${avg_win_amt:.0f} ({avg_win_pct:.1f}%)")
                        print(f"    - Average Loss: ${avg_loss_amt:.0f} ({avg_loss_pct:.1f}%)")
        else:
            # Fallback for old format
            for decision, count in summary['decision_counts'].items():
                win_rate = summary['decision_win_rates'][decision]
                print(f"  {decision}: {count} trades ({win_rate:.1f}% win rate)")
                
                # Get strategy-specific P&L stats for fallback too
                pnl_stats = summary.get('decision_profit_loss_stats', {}).get(decision, {})
                if pnl_stats and count > 0:
                    avg_win_amt = pnl_stats.get('avg_win_amount', 0)
                    avg_loss_amt = pnl_stats.get('avg_loss_amount', 0)
                    avg_win_pct = pnl_stats.get('avg_win_pct', 0)
                    avg_loss_pct = pnl_stats.get('avg_loss_pct', 0)
                    
                    if avg_win_amt != 0 or avg_loss_amt != 0:
                        print(f"    - Average Win: ${avg_win_amt:.0f} ({avg_win_pct:.1f}%)")
                        print(f"    - Average Loss: ${avg_loss_amt:.0f} ({avg_loss_pct:.1f}%)")
        
        # Add dual trade statistics if butterfly_conversion_latest mode is enabled
        if self.butterfly_conversion_latest:
            dual_trades = [r for r in self.results if r.is_dual_trade]
            if dual_trades:
                # Group by trading date to count dual trade days
                dual_trade_dates = set(r.trading_date for r in dual_trades)
                total_dual_trade_days = len(dual_trade_dates)
                
                primary_trades = [r for r in dual_trades if r.dual_trade_type == "primary"]
                secondary_trades = [r for r in dual_trades if r.dual_trade_type == "secondary"]
                
                total_primary_pnl = sum(r.profit_loss for r in primary_trades)
                total_secondary_pnl = sum(r.profit_loss for r in secondary_trades)
                total_combined_pnl = total_primary_pnl + total_secondary_pnl
                
                avg_primary_pnl = total_primary_pnl / len(primary_trades) if primary_trades else 0
                avg_secondary_pnl = total_secondary_pnl / len(secondary_trades) if secondary_trades else 0
                avg_combined_pnl_per_day = total_combined_pnl / total_dual_trade_days if total_dual_trade_days > 0 else 0
                
                print(f"\n{'='*60}")
                print("DUAL TRADE STATISTICS")
                print("="*60)
                print(f"Total Dual Trade Days: {total_dual_trade_days}")
                print(f"Primary Trades: {len(primary_trades)} (Avg P&L: ${avg_primary_pnl:+.0f})")
                print(f"Secondary Trades: {len(secondary_trades)} (Avg P&L: ${avg_secondary_pnl:+.0f})")
                print(f"Combined P&L: ${total_combined_pnl:+.0f} (Avg per day: ${avg_combined_pnl_per_day:+.0f})")
        
        # Add extra butterfly statistics if enabled
        if self.extra_butterfly:
            extra_butterfly_stats = summary.get('extra_butterfly_stats', {})
            if extra_butterfly_stats and extra_butterfly_stats.get('total_trades', 0) > 0:
                total_eb_trades = extra_butterfly_stats['total_trades']
                eb_wins = extra_butterfly_stats['winning_trades']
                eb_losses = extra_butterfly_stats['losing_trades']
                eb_win_rate = extra_butterfly_stats['win_rate_pct']
                eb_total_pnl = extra_butterfly_stats['total_pnl']
                eb_avg_pnl = extra_butterfly_stats['avg_pnl']
                case_counts = extra_butterfly_stats['case_counts']
                
                print(f"\n{'='*60}")
                print("EXTRA BUTTERFLY STATISTICS (15:56:30 TRADES)")
                print("="*60)
                print(f"Total Extra Butterfly Trades: {total_eb_trades}")
                print(f"Wins: {eb_wins} | Losses: {eb_losses} | Win Rate: {eb_win_rate:.1f}%")
                print(f"Total P&L: ${eb_total_pnl:+.0f} | Average P&L: ${eb_avg_pnl:+.0f}")
                
                if case_counts:
                    print(f"\nExtra Butterfly Cases:")
                    for case, count in case_counts.items():
                        if case:  # Skip empty case names
                            print(f"  {case}: {count} trades")
            else:
                print(f"\n{'='*60}")
                print("EXTRA BUTTERFLY STATISTICS (15:56:30 TRADES)")
                print("="*60)
                print("No extra butterfly trades were executed")
        
        # Add Monday comparison if not in skip_monday mode
        if not self.skip_monday:
            monday_comparison = self.generate_monday_comparison()
            if monday_comparison and monday_comparison.get('monday_post_gap', {}).get('total_trades', 0) > 0:
                print(f"\n{'='*60}")
                print("MONDAY/POST-GAP COMPARISON")
                print("="*60)
                
                monday_stats = monday_comparison['monday_post_gap']
                other_stats = monday_comparison['other_days']
                comparison = monday_comparison['comparison']
                
                print(f"Monday/Post-Gap Days: {monday_stats['total_trades']} trades, "
                      f"{monday_stats['win_rate_pct']:.1f}% win rate, "
                      f"${monday_stats['avg_pnl']:+.0f} avg P&L")
                
                print(f"Other Days:           {other_stats['total_trades']} trades, "
                      f"{other_stats['win_rate_pct']:.1f}% win rate, "
                      f"${other_stats['avg_pnl']:+.0f} avg P&L")
                
                print(f"\nDifferences:")
                print(f"  Win Rate: {comparison['win_rate_difference']:+.1f}% "
                      f"({'Monday/Post-Gap better' if comparison['monday_better_win_rate'] else 'Other days better'})")
                print(f"  Avg P&L:  ${comparison['avg_pnl_difference']:+.0f} "
                      f"({'Monday/Post-Gap better' if comparison['monday_better_pnl'] else 'Other days better'})")
        
        # Add monthly breakdown if available
        if summary.get('monthly_breakdown'):
            print(f"\n{'='*60}")
            print("MONTHLY PERFORMANCE BREAKDOWN")
            print("="*60)
            print(f"{'Month':<12} {'Trades':<7} {'Win %':<7} {'P&L':<10} {'Return %':<10} {'Balance':<10}")
            print("-" * 60)
            
            monthly_breakdown = summary['monthly_breakdown']
            for month_key in sorted(monthly_breakdown.keys()):
                month_data = monthly_breakdown[month_key]
                print(f"{month_data['display_name']:<12} "
                      f"{month_data['total_trades']:<7} "
                      f"{month_data['win_rate_pct']:<7.1f} "
                      f"${month_data['month_pnl']:<9.0f} "
                      f"{month_data['monthly_return_pct']:+<9.1f}% "
                      f"${month_data['month_end_balance']:<9.0f}")
        
        print("="*60)

    def round_up_to_nickel(self, price: float) -> float:
        """
        Round price up to nearest 5 cents (nickel)
        
        Args:
            price: Original price
            
        Returns:
            Price rounded up to nearest $0.05
        """
        import math
        return math.ceil(price * 20) / 20

    def basic_spread_price(self, current_spx: float, long_strike: float, short_strike: float) -> float:
        """
        Calculate spread price using basic linear approximation
        
        Args:
            current_spx: Current SPX price
            long_strike: Long strike of the spread (the one we buy)
            short_strike: Short strike of the spread (the one we sell)
            
        Returns:
            Spread price using linear model (minimum $0.80, maximum $3.20), rounded up to nearest 5 cents
        """
        # Determine if this is a call spread or put spread based on strike relationship
        if long_strike < short_strike:
            # Call spread: buy lower strike, sell higher strike
            # Price increases as SPX moves toward/above long strike
            distance_from_atm = current_spx - long_strike
            raw_price = max(0.80, min(3.20, (1/3) * distance_from_atm + 1.7))
        else:
            # Put spread: buy higher strike, sell lower strike  
            # Price increases as SPX moves toward/below long strike
            distance_from_atm = long_strike - current_spx
            raw_price = max(0.80, min(3.20, (1/3) * distance_from_atm + 1.7))
        
        # Round up to nearest 5 cents
        return self.round_up_to_nickel(raw_price)
    
    def check_narrow_fill_opportunity(self, trading_date: str, decision: str, final_strikes: List[float]) -> Tuple[bool, float, str, List[Tuple[float, float, float, float]], Tuple[float, float, float, float]]:
        """
        Check if narrow strategy would have filled during 15:51:30-16:00:00 period
        Based on SPX moving to a level where the spread would be priced at $2.05 or under
        
        Args:
            trading_date: Trading date
            decision: narrow_bull or narrow_bear
            final_strikes: [long_strike, short_strike]
            
        Returns:
            (filled, fill_spx_level, fill_time, narrow_fill_ohlc, narrow_fill_consolidated_ohlc) - 
            whether it filled, at what SPX level, when, detailed OHLC bars, and consolidated OHLC
        """
        
        # Get 10-second data from 15:51:30 to 16:00:00
        fill_period_data = self.db_connector.get_narrow_fill_data(trading_date)
        
        if not fill_period_data:
            logger.warning(f"No narrow fill data available for {trading_date}")
            return False, 0.0, "", [], (0.0, 0.0, 0.0, 0.0)
        
        # Convert to OHLC tuples for detailed bars
        narrow_fill_ohlc = []
        for bar in fill_period_data:
            narrow_fill_ohlc.append((bar['open'], bar['high'], bar['low'], bar['close']))
        
        # Calculate consolidated OHLC for entire period
        if narrow_fill_ohlc:
            consolidated_open = narrow_fill_ohlc[0][0]  # First bar's open
            consolidated_high = max(bar[1] for bar in narrow_fill_ohlc)  # Highest high
            consolidated_low = min(bar[2] for bar in narrow_fill_ohlc)   # Lowest low
            consolidated_close = narrow_fill_ohlc[-1][3]  # Last bar's close
            narrow_fill_consolidated_ohlc = (consolidated_open, consolidated_high, consolidated_low, consolidated_close)
        else:
            narrow_fill_consolidated_ohlc = (0.0, 0.0, 0.0, 0.0)
        
        long_strike = final_strikes[0]
        short_strike = final_strikes[1]
        target_fill_price = 2.05  # Our target limit order price
        
        # Check each 10-second bar to see if spread would have been priced at $2.05 or under
        for bar in fill_period_data:
            bar_time = bar['date'].strftime('%H:%M:%S') if hasattr(bar['date'], 'strftime') else str(bar['date'])
            
            # For narrow bull: check if SPX drop would make spread cheaper
            # Bull call spreads get cheaper as SPX moves down (away from strikes)
            if decision == "narrow_bull":
                # Test the low of this bar - would spread be <= $2.05?
                test_spx = bar['low']
                spread_price = self.basic_spread_price(test_spx, long_strike, short_strike)
                
                if spread_price <= target_fill_price:
                    logger.info(f"Narrow bull {long_strike}/{short_strike} would fill at SPX {test_spx:.2f} (spread=${spread_price:.2f}) at {bar_time}")
                    return True, test_spx, bar_time, narrow_fill_ohlc, narrow_fill_consolidated_ohlc
            
            # For narrow bear: check if SPX rise would make spread cheaper  
            # Bear put spreads get cheaper as SPX moves up (away from strikes)
            elif decision == "narrow_bear":
                # Test the high of this bar - would spread be <= $2.05?
                test_spx = bar['high'] 
                spread_price = self.basic_spread_price(test_spx, long_strike, short_strike)
                
                if spread_price <= target_fill_price:
                    logger.info(f"Narrow bear {long_strike}/{short_strike} would fill at SPX {test_spx:.2f} (spread=${spread_price:.2f}) at {bar_time}")
                    return True, test_spx, bar_time, narrow_fill_ohlc, narrow_fill_consolidated_ohlc
        
        # No fill opportunity found
        logger.info(f"Narrow {decision} {long_strike}/{short_strike} did not reach $2.05 fill threshold on {trading_date}")
        return False, 0.0, "", narrow_fill_ohlc, narrow_fill_consolidated_ohlc
    
    def apply_alternate_strategy_conversion(self, decision_result: DecisionResult, trading_date: str) -> DecisionResult:
        """
        Apply alternate strategy conversion rules:
        
        Full alternate strategy (alternate_strategy=True):
        - narrow_bull -> bull (with identical strike selection logic)
        - narrow_bear -> bear (with identical strike selection logic)  
        - iron_butterfly -> bull if 1st bar close > 66%, bear if < 66% (with identical strike selection logic)
        
        Butterfly-only conversion (butterfly_conversion_only=True):
        - iron_butterfly -> bull if 1st bar close > 66%, bear if < 66% (with identical strike selection logic)
        - narrow strategies remain unchanged
        
        Butterfly-narrow conversions (butterfly_narrow_conversions=True):
        - iron_butterfly -> bull/bear based on 1st bar close > 66%, but use narrow_bull/narrow_bear if span3 < threshold
        - narrow strategies remain unchanged
        
        Butterfly conversion with narrows (butterfly_conversion_with_narrows=True):
        - iron_butterfly -> bull if 1st bar close >= 66% AND span3 >= threshold
        - iron_butterfly -> narrow_bull if 1st bar close >= 66% AND span3 < threshold
        - iron_butterfly -> bear if 1st bar close < 66% AND span3 >= threshold
        - iron_butterfly -> narrow_bear if 1st bar close < 66% AND span3 < threshold
        
        Butterfly conversion latest (butterfly_conversion_latest=True):
        - iron_butterfly with bias=0 -> bull if SPAN1 close > 66%, bear if SPAN1 close < 66%
        - For SPAN3 < threshold: Convert ALL strategies to narrow versions
        - For narrow strategies: place TWO trades (narrow + IBF with bias)
        
        VIX-based threshold (vix_based_threshold=True):
        - Threshold = 4.0 when VIX > 16.5
        - Threshold = 6.0 when VIX <= 16.5
        - Applied to all conversion modes that use span3 thresholds
        
        Args:
            decision_result: Original decision result from decision engine
            
        Returns:
            Modified decision result with converted strategy
        """
        if not self.alternate_strategy and not self.butterfly_conversion_only and not self.butterfly_narrow_conversions and not self.butterfly_conversion_with_narrows and not self.butterfly_conversion_latest:
            return decision_result  # No conversion needed
        
        original_decision = decision_result.decision
        first_bar_close_pct = decision_result.close_percentages[0]
        span3 = decision_result.spans.get('span3', 0.0)
        
        # Get VIX-based narrow conversion threshold
        narrow_threshold = self.get_narrow_conversion_threshold(trading_date)
        
        new_decision = original_decision
        new_reasoning = decision_result.reasoning
        
        # Rule 1: Convert narrow_bull to bull (only if full alternate strategy enabled)
        if original_decision == "narrow_bull" and self.alternate_strategy and not self.butterfly_conversion_only and not self.butterfly_narrow_conversions:
            new_decision = "bull"
            new_reasoning += f" → ALT: narrow_bull converted to bull"
        
        # Rule 2: Convert narrow_bear to bear (only if full alternate strategy enabled)
        elif original_decision == "narrow_bear" and self.alternate_strategy and not self.butterfly_conversion_only and not self.butterfly_narrow_conversions:
            new_decision = "bear"
            new_reasoning += f" → ALT: narrow_bear converted to bear"
        
        # Rule 3: Convert iron_butterfly based on 1st bar close percentage (all conversion modes)
        elif original_decision == "iron_butterfly" and (self.alternate_strategy or self.butterfly_conversion_only or self.butterfly_narrow_conversions or self.butterfly_conversion_with_narrows):
            
            # Handle the new butterfly_conversion_with_narrows mode
            if self.butterfly_conversion_with_narrows:
                if first_bar_close_pct >= 66.0:
                    if span3 >= narrow_threshold:
                        new_decision = "bull"
                        new_reasoning += f" → BF-CONV-NARROW: iron_butterfly→bull (1st bar {first_bar_close_pct:.1f}% >= 66%, span3 {span3:.1f} >= {narrow_threshold})"
                    else:
                        new_decision = "narrow_bull"
                        new_reasoning += f" → BF-CONV-NARROW: iron_butterfly→narrow_bull (1st bar {first_bar_close_pct:.1f}% >= 66%, span3 {span3:.1f} < {narrow_threshold})"
                else:
                    if span3 >= narrow_threshold:
                        new_decision = "bear"
                        new_reasoning += f" → BF-CONV-NARROW: iron_butterfly→bear (1st bar {first_bar_close_pct:.1f}% < 66%, span3 {span3:.1f} >= {narrow_threshold})"
                    else:
                        new_decision = "narrow_bear"
                        new_reasoning += f" → BF-CONV-NARROW: iron_butterfly→narrow_bear (1st bar {first_bar_close_pct:.1f}% < 66%, span3 {span3:.1f} < {narrow_threshold})"
            
            # Handle existing conversion modes
            elif first_bar_close_pct > 66.0:
                # Determine target strategy based on mode and span3
                if self.butterfly_narrow_conversions and span3 < narrow_threshold:
                    new_decision = "narrow_bull"
                    new_reasoning += f" → BF-NARROW: iron_butterfly→narrow_bull (1st bar {first_bar_close_pct:.1f}% > 66%, span3 {span3:.1f} < {narrow_threshold})"
                elif self.butterfly_conversion_only:
                    new_decision = "BF-bull"
                    new_reasoning += f" → BF-ONLY: iron_butterfly→bull (1st bar {first_bar_close_pct:.1f}% > 66%)"
                else:
                    new_decision = "bull"
                    conversion_type = "ALT" if self.alternate_strategy else "BF-NARROW"
                    new_reasoning += f" → {conversion_type}: iron_butterfly→bull (1st bar {first_bar_close_pct:.1f}% > 66%)"
            else:
                # Determine target strategy based on mode and span3
                if self.butterfly_narrow_conversions and span3 < narrow_threshold:
                    new_decision = "narrow_bear"
                    new_reasoning += f" → BF-NARROW: iron_butterfly→narrow_bear (1st bar {first_bar_close_pct:.1f}% < 66%, span3 {span3:.1f} < {narrow_threshold})"
                elif self.butterfly_conversion_only:
                    new_decision = "BF-bear"
                    new_reasoning += f" → BF-ONLY: iron_butterfly→bear (1st bar {first_bar_close_pct:.1f}% < 66%)"
                else:
                    new_decision = "bear"
                    conversion_type = "ALT" if self.alternate_strategy else "BF-NARROW"
                    new_reasoning += f" → {conversion_type}: iron_butterfly→bear (1st bar {first_bar_close_pct:.1f}% < 66%)"
        
        # Handle the new butterfly_conversion_latest mode
        elif self.butterfly_conversion_latest:
            span1_close_pct = first_bar_close_pct  # SPAN1 close % is same as first bar close %
            span3 = decision_result.spans.get('span3', 0.0)
            bias = decision_result.bias
            
            # First, check for iron butterfly conversion (ALL iron butterflies, regardless of bias)
            if original_decision == "iron_butterfly":
                if span1_close_pct >= 66.0:
                    new_decision = "bull"
                    new_reasoning += f" → BF-CONV-LATEST: iron_butterfly→bull (SPAN1 close {span1_close_pct:.1f}% >= 66%)"
                else:
                    new_decision = "bear" 
                    new_reasoning += f" → BF-CONV-LATEST: iron_butterfly→bear (SPAN1 close {span1_close_pct:.1f}% < 66%)"
            
            # Then, check for narrow conversions for ALL strategies
            if span3 < narrow_threshold:
                if new_decision == "bull":
                    new_decision = "narrow_bull"
                    new_reasoning += f" → NARROW: bull→narrow_bull (SPAN3 {span3:.1f} < {narrow_threshold})"
                elif new_decision == "bear":
                    new_decision = "narrow_bear"
                    new_reasoning += f" → NARROW: bear→narrow_bear (SPAN3 {span3:.1f} < {narrow_threshold})"
                elif original_decision == "bull":
                    new_decision = "narrow_bull"
                    new_reasoning += f" → NARROW: bull→narrow_bull (SPAN3 {span3:.1f} < {narrow_threshold})"
                elif original_decision == "bear":
                    new_decision = "narrow_bear"
                    new_reasoning += f" → NARROW: bear→narrow_bear (SPAN3 {span3:.1f} < {narrow_threshold})"
                # Note: In butterfly_conversion_latest mode, iron_butterfly should have already
                # been converted to bull/bear by the butterfly conversion logic above.
                # If we reach here with iron_butterfly, it means no conversion happened,
                # so we don't convert iron_butterfly directly to narrow_iron_butterfly.
        
        # If conversion happened, create new decision result
        if new_decision != original_decision:
            # Create new decision result with converted strategy
            # The strike optimization will handle the new decision type with identical logic to original bulls/bears
            converted_result = DecisionResult(
                decision=new_decision,
                strikes=decision_result.strikes,  # Will be updated in optimization with bull/bear logic
                option_types=decision_result.option_types,
                actions=decision_result.actions,
                target_price=decision_result.target_price,
                reasoning=new_reasoning,
                bias=decision_result.bias,
                spans=decision_result.spans,
                close_percentages=decision_result.close_percentages
            )
            
            logger.info(f"Strategy conversion: {original_decision} → {new_decision}")
            return converted_result
        
        return decision_result

    def create_secondary_iron_butterfly(self, primary_decision_result: DecisionResult, bias: int, execution_price: float) -> DecisionResult:
        """
        Create a secondary iron butterfly trade for dual trades in butterfly_conversion_latest mode
        
        Args:
            primary_decision_result: Primary trade decision result
            bias: Bias for the iron butterfly (+1 for up, -1 for down)
            execution_price: Current SPX price for strike selection
            
        Returns:
            DecisionResult for the iron butterfly trade
        """
        from decision_engine import determine_iron_butterfly_strike, get_strategy_strikes_and_types
        
        # Determine center strike based on bias
        center_strike = determine_iron_butterfly_strike(execution_price, bias)
        
        # Get iron butterfly structure
        strategy_info = get_strategy_strikes_and_types("iron_butterfly", center_strike, execution_price)
        
        # Calculate target price based on narrow strategy with adjustments
        # The IBF target price should match the narrow strategy's target price (which is the short strike), 
        # but if that target is "worse" than execution price, adjust it to align with IBF strikes
        narrow_target_price = primary_decision_result.target_price
        
        # Iron butterfly strikes: [center-5, center, center, center+5]
        ibf_strikes = strategy_info['strikes']  # [wing_put, center_put, center_call, wing_call]
        ibf_center = ibf_strikes[1]  # Center strike
        ibf_put_wing = ibf_strikes[0]  # Lower wing (center - 5)
        ibf_call_wing = ibf_strikes[3]  # Upper wing (center + 5)
        
        # Determine adjusted target price - should align with one of the IBF strikes
        if primary_decision_result.decision == "narrow_bear":
            # For narrow bear: target should be at or below execution price for max profit
            # Choose the IBF strike that best aligns with this requirement
            if narrow_target_price >= execution_price:
                # Target is worse (too high), need to adjust down
                # Choose the highest IBF strike that's below execution price
                candidate_strikes = [s for s in [ibf_put_wing, ibf_center] if s < execution_price]
                if candidate_strikes:
                    ibf_target_price = max(candidate_strikes)
                else:
                    # All IBF strikes are >= execution, use the lowest one
                    ibf_target_price = min(ibf_put_wing, ibf_center)
            else:
                # Narrow target is already good (below execution), but align with IBF strikes
                # Choose the IBF strike closest to the narrow target
                candidate_strikes = [ibf_put_wing, ibf_center, ibf_call_wing]
                ibf_target_price = min(candidate_strikes, key=lambda x: abs(x - narrow_target_price))
                
        elif primary_decision_result.decision == "narrow_bull":
            # For narrow bull: target should be at or above execution price for max profit
            # Choose the IBF strike that best aligns with this requirement
            if narrow_target_price <= execution_price:
                # Target is worse (too low), need to adjust up
                # Choose the lowest IBF strike that's above execution price
                candidate_strikes = [s for s in [ibf_center, ibf_call_wing] if s > execution_price]
                if candidate_strikes:
                    ibf_target_price = min(candidate_strikes)
                else:
                    # All IBF strikes are <= execution, use the highest one
                    ibf_target_price = max(ibf_center, ibf_call_wing)
            else:
                # Narrow target is already good (above execution), but align with IBF strikes
                # Choose the IBF strike closest to the narrow target
                candidate_strikes = [ibf_put_wing, ibf_center, ibf_call_wing]
                ibf_target_price = min(candidate_strikes, key=lambda x: abs(x - narrow_target_price))
        else:
            # Not a narrow strategy, use the IBF center strike
            ibf_target_price = ibf_center
        
        # Create decision result for iron butterfly
        bias_desc = "up" if bias > 0 else "down"
        reasoning = f"Secondary IBF(bias {bias_desc}) for dual trade with {primary_decision_result.decision}"
        
        return DecisionResult(
            decision="iron_butterfly",
            strikes=strategy_info['strikes'],
            option_types=strategy_info['option_types'],
            actions=strategy_info['actions'],
            target_price=ibf_target_price,  # Use adjusted target price that aligns with IBF strikes
            reasoning=reasoning,
            bias=bias,
            spans=primary_decision_result.spans,
            close_percentages=primary_decision_result.close_percentages
        )

    def generate_trade_report(self, result: BacktestResult, output_dir: str = "backtest_trade_reports") -> str:
        """
        Generate detailed markdown trade report for a single backtest trade
        
        Args:
            result: BacktestResult for the trade
            output_dir: Directory to save the report
            
        Returns:
            Filepath of the generated report
        """
        import os
        from pathlib import Path
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # Generate filename
        filename = f"{result.trading_date}_{result.decision}_backtest.md"
        filepath = output_path / filename
        
        # Generate report content
        markdown = self._generate_backtest_trade_markdown(result)
        
        # Save to file
        with open(filepath, 'w') as f:
            f.write(markdown)
        
        logger.info(f"📊 Backtest trade report saved: {filepath}")
        return str(filepath)
    
    def _generate_backtest_trade_markdown(self, result: BacktestResult) -> str:
        """Generate markdown content for backtest trade report"""
        
        # Header
        success_icon = "✅" if result.trade_successful else "❌"
        pnl_color = "🟢" if result.profit_loss > 0 else "🔴" if result.profit_loss < 0 else "⚪"
        
        markdown = f"""# Backtest Trade Report {success_icon}

## Trade Summary
- **Date:** {result.trading_date}
- **Strategy:** **{result.decision}**
- **Result:** {success_icon} {"SUCCESS" if result.trade_successful else "FAILURE"}
- **P&L:** {pnl_color} **${result.profit_loss:,.2f}**
- **Processing Time:** {result.processing_time:.3f}s

## Market Analysis

### Bar Data
| Bar | OHLC | Close % | Range |
|-----|------|---------|-------|
"""
        
        # Add bar data with close percentages
        for i, (bar, close_pct) in enumerate(zip(result.bars, result.close_percentages), 1):
            o, h, l, c = bar
            span_range = h - l
            markdown += f"| {i} | O:{o:.2f} H:{h:.2f} L:{l:.2f} C:{c:.2f} | {close_pct:.1f}% | {span_range:.2f} |\n"
        
        # Span calculations
        markdown += f"\n### Span Analysis\n"
        for i, (span_key, span_value) in enumerate(result.spans.items(), 1):
            markdown += f"- **{span_key.upper()}:** {span_value:.2f} points\n"
        
        # Decision logic
        markdown += f"\n## Decision Logic\n\n"
        markdown += f"**Reasoning:** {result.reasoning}\n\n"
        markdown += f"**Bias:** {result.bias}\n\n"
        
        # Alternate strategy conversion (if applicable)
        if self.alternate_strategy and "ALT:" in result.reasoning:
            markdown += f"### 🔄 Alternate Strategy Conversion\n"
            reasoning_parts = result.reasoning.split("→ ALT:")
            if len(reasoning_parts) > 1:
                original_part = reasoning_parts[0].strip()
                conversion_part = reasoning_parts[1].strip()
                markdown += f"- **Original Strategy:** {original_part}\n"
                markdown += f"- **Conversion:** {conversion_part}\n\n"
        
        # Strike optimization
        markdown += f"## Strike Selection & Pricing\n\n"
        markdown += f"### Original vs Final Strikes\n"
        markdown += f"- **Original Strikes:** {result.original_strikes}\n"
        markdown += f"- **Final Strikes:** {result.final_strikes}\n"
        markdown += f"- **Strikes Moved:** {'Yes' if result.strikes_moved else 'No'}\n"
        markdown += f"- **Execution Price:** ${result.execution_price:.2f}\n"
        markdown += f"- **Target Price:** ${result.target_price:.2f}\n"
        markdown += f"- **Closing Price:** ${result.closing_price:.2f}\n\n"
        
        # Options details
        markdown += f"### Options Strategy Details\n"
        if len(result.final_strikes) == 2:
            # Spread strategy
            long_strike, short_strike = result.final_strikes
            
            # Safely handle option_types and actions - they might have more than 2 elements for converted butterflies
            if len(result.option_types) >= 2:
                option_long, option_short = result.option_types[0], result.option_types[1]
            else:
                option_long, option_short = result.option_types[0] if result.option_types else "UNKNOWN", "UNKNOWN"
                
            if len(result.actions) >= 2:
                action_long, action_short = result.actions[0], result.actions[1]
            else:
                action_long, action_short = result.actions[0] if result.actions else "UNKNOWN", "UNKNOWN"
            
            markdown += f"- **Long Position:** {action_long} {long_strike} {option_long}\n"
            markdown += f"- **Short Position:** {action_short} {short_strike} {option_short}\n"
            markdown += f"- **Spread Price:** ${result.spread_price:.2f}\n"
            
        elif len(result.final_strikes) == 4:
            # Iron butterfly
            strikes = result.final_strikes
            options = result.option_types
            actions = result.actions
            
            markdown += f"- **Iron Butterfly Center:** {strikes[1]}\n"
            markdown += f"- **Wing Strikes:** {strikes[0]} / {strikes[3]}\n"
            markdown += f"- **Credit Received:** ${abs(result.spread_price):.2f}\n"
        
        # Risk management
        markdown += f"\n## Risk Management\n\n"
        markdown += f"- **Account Balance:** ${result.current_balance:,.2f}\n"
        markdown += f"- **Max Risk Allowed:** ${result.max_risk_amount:,.2f}\n"
        markdown += f"- **Risk Per Contract:** ${result.risk_per_contract:.2f}\n"
        markdown += f"- **Contracts Traded:** {result.num_contracts}\n"
        markdown += f"- **Actual Risk Taken:** ${result.actual_risk_amount:,.2f}\n"
        markdown += f"- **New Balance:** ${result.new_balance:,.2f}\n\n"
        
        # Trade outcome analysis
        markdown += f"## Trade Outcome Analysis\n\n"
        
        if result.decision in ["bull", "narrow_bull", "BF-bull"]:
            # Handle different strike configurations
            if len(result.final_strikes) == 2:
                long_strike, short_strike = result.final_strikes
            elif len(result.final_strikes) == 4:
                long_strike, short_strike = result.final_strikes[0], result.final_strikes[1]  # Put spread portion
            else:
                long_strike, short_strike = result.final_strikes[0], result.final_strikes[-1]
                
            strategy_type = "BF-Bull" if result.decision == "BF-bull" else "Bull"
            markdown += f"### {strategy_type} Call Spread Analysis\n"
            markdown += f"- **Breakeven:** {long_strike + result.spread_price:.2f}\n"
            markdown += f"- **Max Profit:** ${(short_strike - long_strike - result.spread_price) * 100 * result.num_contracts:,.2f}\n"
            markdown += f"- **Max Loss:** ${result.spread_price * 100 * result.num_contracts:,.2f}\n"
            
            if result.closing_price >= short_strike:
                markdown += f"- **Outcome:** Maximum profit (SPX closed at {result.closing_price:.2f} ≥ {short_strike})\n"
            elif result.closing_price <= long_strike:
                markdown += f"- **Outcome:** Maximum loss (SPX closed at {result.closing_price:.2f} ≤ {long_strike})\n"
            else:
                intrinsic = result.closing_price - long_strike
                markdown += f"- **Outcome:** Partial profit (SPX closed at {result.closing_price:.2f}, intrinsic value: ${intrinsic:.2f})\n"
                
        elif result.decision in ["bear", "narrow_bear", "BF-bear"]:
            # Handle different strike configurations
            if len(result.final_strikes) == 2:
                long_strike, short_strike = result.final_strikes
            elif len(result.final_strikes) == 4:
                long_strike, short_strike = result.final_strikes[2], result.final_strikes[3]  # Call spread portion
            else:
                long_strike, short_strike = result.final_strikes[0], result.final_strikes[-1]
                
            strategy_type = "BF-Bear" if result.decision == "BF-bear" else "Bear"
            markdown += f"### {strategy_type} Put Spread Analysis\n"
            markdown += f"- **Breakeven:** {long_strike - result.spread_price:.2f}\n"
            markdown += f"- **Max Profit:** ${(long_strike - short_strike - result.spread_price) * 100 * result.num_contracts:,.2f}\n"
            markdown += f"- **Max Loss:** ${result.spread_price * 100 * result.num_contracts:,.2f}\n"
            
            if result.closing_price <= short_strike:
                markdown += f"- **Outcome:** Maximum profit (SPX closed at {result.closing_price:.2f} ≤ {short_strike})\n"
            elif result.closing_price >= long_strike:
                markdown += f"- **Outcome:** Maximum loss (SPX closed at {result.closing_price:.2f} ≥ {long_strike})\n"
            else:
                intrinsic = long_strike - result.closing_price
                markdown += f"- **Outcome:** Partial profit (SPX closed at {result.closing_price:.2f}, intrinsic value: ${intrinsic:.2f})\n"
                
        elif result.decision == "iron_butterfly":
            center_strike = result.final_strikes[1]
            distance_from_center = abs(result.closing_price - center_strike)
            markdown += f"### Iron Butterfly Analysis\n"
            markdown += f"- **Center Strike:** {center_strike}\n"
            markdown += f"- **Distance from Center:** {distance_from_center:.2f} points\n"
            markdown += f"- **Credit Received:** ${abs(result.spread_price) * 100 * result.num_contracts:,.2f}\n"
            
            if distance_from_center <= 2.5:
                markdown += f"- **Outcome:** Near maximum loss (SPX too close to center)\n"
            else:
                markdown += f"- **Outcome:** Profit region (SPX away from center)\n"
        
        # Narrow strategy fill analysis (if applicable)
        if result.decision in ["narrow_bull", "narrow_bear"] and result.narrow_fill_ohlc:
            markdown += f"\n## Narrow Strategy Fill Analysis\n\n"
            markdown += f"### Fill Period Data (15:51:30 - 16:00:00)\n"
            
            if result.narrow_fill_consolidated_ohlc != (0.0, 0.0, 0.0, 0.0):
                # Safely unpack OHLC data
                if len(result.narrow_fill_consolidated_ohlc) == 4:
                    o, h, l, c = result.narrow_fill_consolidated_ohlc
                    markdown += f"- **Consolidated OHLC:** O:{o:.2f} H:{h:.2f} L:{l:.2f} C:{c:.2f}\n"
                    markdown += f"- **Range:** {h - l:.2f} points\n"
                    markdown += f"- **10-second Bars:** {len(result.narrow_fill_ohlc)} bars captured\n"
                else:
                    markdown += f"- **Consolidated OHLC:** {result.narrow_fill_consolidated_ohlc}\n"
                    markdown += f"- **10-second Bars:** {len(result.narrow_fill_ohlc)} bars captured\n"
        
        # Pricing mode analysis
        markdown += f"\n## Pricing Analysis\n\n"
        markdown += f"- **Pricing Mode:** {self.pricing_mode.title()}\n"
        
        if self.pricing_mode == "basic":
            # Handle different strike configurations (2 for spreads, 4 for butterflies)
            if len(result.final_strikes) >= 2:
                if len(result.final_strikes) == 2:
                    long_strike, short_strike = result.final_strikes
                elif len(result.final_strikes) == 4:
                    # Iron butterfly: [wing_put, center_put, center_call, wing_call]
                    # For distance calculation, use the relevant strikes based on decision
                    if result.decision in ["bull", "narrow_bull", "BF-bull"]:
                        long_strike, short_strike = result.final_strikes[0], result.final_strikes[1]  # Put spread portion
                    else:
                        long_strike, short_strike = result.final_strikes[2], result.final_strikes[3]  # Call spread portion
                else:
                    # Fallback for unexpected configurations
                    long_strike, short_strike = result.final_strikes[0], result.final_strikes[-1]
                
                if result.decision in ["bull", "narrow_bull", "BF-bull"]:
                    distance = result.execution_price - long_strike
                else:
                    distance = long_strike - result.execution_price
                markdown += f"- **Distance from ATM:** {distance:.2f} points\n"
                markdown += f"- **Linear Formula:** max(0.80, (1/3) * {distance:.2f} + 1.625) = ${result.spread_price:.2f}\n"
        
        # Footer
        markdown += f"\n---\n\n### Configuration\n"
        # Only show volatility for advanced pricing mode where it's actually used
        if self.pricing_mode == "advanced":
            markdown += f"- **Volatility:** {self.volatility * 100:.0f}%\n"
        markdown += f"- **Max Risk Per Trade:** {self.max_risk_per_trade_pc * 100:.1f}%\n"
        markdown += f"- **Alternate Strategy:** {'Enabled' if self.alternate_strategy else 'Disabled'}\n"
        markdown += f"- **Skip Monday:** {'Yes' if self.skip_monday else 'No'}\n"
        
        markdown += f"\n*Report generated on {result.trading_date} for backtest analysis*\n"
        
        return markdown

    def generate_losing_trades_reports(self, results: List[BacktestResult], output_dir: str = "backtest_losing_trades") -> List[str]:
        """
        Generate detailed trade reports for all losing trades
        
        Args:
            results: List of BacktestResult objects
            output_dir: Directory to save the reports
            
        Returns:
            List of filepaths for generated reports
        """
        losing_trades = [r for r in results if not r.trade_successful]
        
        if not losing_trades:
            logger.info("No losing trades found - no reports to generate")
            return []
        
        logger.info(f"Generating detailed reports for {len(losing_trades)} losing trades...")
        
        report_paths = []
        for result in losing_trades:
            filepath = self.generate_trade_report(result, output_dir)
            report_paths.append(filepath)
        
        # Generate summary report for all losing trades
        summary_path = self._generate_losing_trades_summary(losing_trades, output_dir)
        report_paths.append(summary_path)
        
        logger.info(f"Generated {len(report_paths)} reports in {output_dir}/")
        return report_paths
    
    def generate_all_trades_reports(self, results: List[BacktestResult], output_dir: str = "backtest_all_trades") -> List[str]:
        """
        Generate detailed trade reports for ALL trades (winning and losing)
        
        Args:
            results: List of BacktestResult objects
            output_dir: Directory to save the reports
            
        Returns:
            List of filepaths for generated reports
        """
        logger.info(f"Generating detailed reports for {len(results)} trades...")
        
        report_paths = []
        for i, result in enumerate(results, 1):
            if i % 50 == 0:
                logger.info(f"Generated {i}/{len(results)} reports...")
            filepath = self.generate_trade_report(result, output_dir)
            report_paths.append(filepath)
        
        logger.info(f"Generated {len(report_paths)} trade reports in {output_dir}/")
        return report_paths
    
    def _generate_losing_trades_summary(self, losing_trades: List[BacktestResult], output_dir: str) -> str:
        """Generate summary report for all losing trades"""
        from pathlib import Path
        
        output_path = Path(output_dir)
        filepath = output_path / "losing_trades_summary.md"
        
        # Calculate statistics
        total_loss = sum(r.profit_loss for r in losing_trades)
        avg_loss = total_loss / len(losing_trades)
        
        # Group by strategy
        strategy_losses = {}
        for trade in losing_trades:
            strategy = trade.decision
            if strategy not in strategy_losses:
                strategy_losses[strategy] = []
            strategy_losses[strategy].append(trade)
        
        markdown = f"""# Losing Trades Analysis Summary

## Overview
- **Total Losing Trades:** {len(losing_trades)}
- **Total Loss:** ${total_loss:,.2f}
- **Average Loss per Trade:** ${avg_loss:,.2f}

## Breakdown by Strategy

"""
        
        for strategy, trades in strategy_losses.items():
            strategy_loss = sum(r.profit_loss for r in trades)
            avg_strategy_loss = strategy_loss / len(trades)
            
            markdown += f"### {strategy.title()} Strategy\n"
            markdown += f"- **Losing Trades:** {len(trades)}\n"
            markdown += f"- **Total Loss:** ${strategy_loss:,.2f}\n"
            markdown += f"- **Average Loss:** ${avg_strategy_loss:,.2f}\n\n"
        
        # Common failure patterns
        markdown += "## Common Failure Patterns\n\n"
        
        # Analyze pricing modes
        pricing_mode_failures = {}
        for trade in losing_trades:
            if hasattr(self, 'pricing_mode'):
                mode = self.pricing_mode
                if mode not in pricing_mode_failures:
                    pricing_mode_failures[mode] = 0
                pricing_mode_failures[mode] += 1
        
        if pricing_mode_failures:
            markdown += "### By Pricing Mode\n"
            for mode, count in pricing_mode_failures.items():
                pct = (count / len(losing_trades)) * 100
                markdown += f"- **{mode.title()}:** {count} trades ({pct:.1f}%)\n"
            markdown += "\n"
        
        # Worst performing dates
        worst_trades = sorted(losing_trades, key=lambda x: x.profit_loss)[:10]
        markdown += "### Worst Performing Trades\n"
        markdown += "| Date | Strategy | P&L | Reason |\n"
        markdown += "|------|----------|-----|--------|\n"
        
        for trade in worst_trades:
            reason_short = trade.reasoning[:50] + "..." if len(trade.reasoning) > 50 else trade.reasoning
            markdown += f"| {trade.trading_date} | {trade.decision} | ${trade.profit_loss:,.2f} | {reason_short} |\n"
        
        markdown += f"\n*Analysis generated for {len(losing_trades)} losing trades*\n"
        
        # Save summary
        with open(filepath, 'w') as f:
            f.write(markdown)
        
        logger.info(f"📊 Losing trades summary saved: {filepath}")
        return str(filepath)

    def generate_trade_report_to_database(self, result: BacktestResult) -> Optional[str]:
        """
        Generate detailed trade report and save to database
        
        Args:
            result: BacktestResult for the trade
            
        Returns:
            Database report_id if successful, None if failed
        """
        try:
            # Generate markdown content
            markdown = self._generate_backtest_trade_markdown(result)
            
            # Save to database
            from database_output import BacktestDatabaseOutput
            db_output = BacktestDatabaseOutput()
            report_id = db_output.save_trade_report_to_database(result, markdown, self)
            
            if report_id:
                logger.info(f"📊 Backtest trade report saved to database with ID: {report_id}")
                return report_id
            else:
                logger.error("Failed to save trade report to database")
                return None
                
        except Exception as e:
            logger.error(f"Error generating trade report to database: {str(e)}")
            return None
    
    def generate_losing_trades_reports_to_database(self, results: List[BacktestResult]) -> List[str]:
        """
        Generate detailed trade reports for all losing trades and save to database
        
        Args:
            results: List of BacktestResult objects
            
        Returns:
            List of database report_ids for generated reports
        """
        losing_trades = [r for r in results if not r.trade_successful]
        
        if not losing_trades:
            logger.info("No losing trades found - no reports to generate")
            return []
        
        logger.info(f"Generating detailed database reports for {len(losing_trades)} losing trades...")
        
        report_ids = []
        for result in losing_trades:
            report_id = self.generate_trade_report_to_database(result)
            if report_id:
                report_ids.append(report_id)
        
        logger.info(f"Generated {len(report_ids)} trade reports in database")
        return report_ids
    
    def generate_all_trades_reports_to_database(self, results: List[BacktestResult]) -> List[str]:
        """
        Generate detailed trade reports for ALL trades (winning and losing) and save to database
        
        Args:
            results: List of BacktestResult objects
            
        Returns:
            List of database report_ids for generated reports
        """
        logger.info(f"Generating detailed database reports for {len(results)} trades...")
        
        report_ids = []
        for i, result in enumerate(results, 1):
            if i % 50 == 0:
                logger.info(f"Generated {i}/{len(results)} reports...")
            report_id = self.generate_trade_report_to_database(result)
            if report_id:
                report_ids.append(report_id)
        
        logger.info(f"Generated {len(report_ids)} trade reports in database")
        return report_ids


def run_random_backtests(num_runs: int, days_per_run: int, args) -> List[Dict[str, Any]]:
    """
    Run multiple backtests with randomly shuffled trading days
    
    Args:
        num_runs: Number of random backtests to run
        days_per_run: Number of days for each backtest (sampled randomly)
        args: Parsed command line arguments
        
    Returns:
        List of summary statistics for each run
    """
    # Get all available trading dates from the database
    # Create a temporary backtester to get the trading dates
    temp_backtester = HistoricalBacktester(
        start_date=args.start_date,
        end_date=args.end_date,
        volatility=args.volatility,
        simple_mode=args.simple_mode,
        pricing_mode=args.pricing_mode,
        skip_monday=args.skip_monday,
        starting_balance=args.starting_balance,
        max_risk_per_trade_pc=args.max_risk_per_trade_pc,
        max_risk_bull_pc=args.max_risk_bull_pc,
        max_risk_bear_pc=args.max_risk_bear_pc,
        max_risk_iron_butterfly_pc=args.max_risk_iron_butterfly_pc,
        simple_spread_cost=args.simple_spread_cost,
        simple_butterfly_credit=args.simple_butterfly_credit,
        simple_itm_threshold=args.simple_itm_threshold,
        alternate_strategy=args.alternate_strategy,
        butterfly_conversion_only=args.butterfly_conversion_only,
        butterfly_narrow_conversions=args.butterfly_narrow_conversions,
        butterfly_conversion_with_narrows=args.butterfly_conversion_with_narrows,
        butterfly_conversion_latest=args.butterfly_conversion_latest,
        extra_butterfly=args.extra_butterfly,
        vix_based_threshold=args.vix_based_threshold
    )
    
    # Get all available trading dates
    all_trading_dates = temp_backtester.db_connector.get_trading_dates(
        start_date=args.start_date,
        end_date=args.end_date
    )
    
    if not all_trading_dates:
        raise ValueError("No trading dates found in the specified range")
    
    # If days_per_run is None, use all available trading days
    if days_per_run is None:
        days_per_run = len(all_trading_dates)
    
    if len(all_trading_dates) < days_per_run:
        raise ValueError(f"Not enough trading days available. Found {len(all_trading_dates)} days, need {days_per_run}")
    
    results = []
    
    print(f"🎲 Running {num_runs} random backtests with shuffled trading days")
    print(f"📅 Total available trading days: {len(all_trading_dates)}")
    if days_per_run == len(all_trading_dates):
        print(f"🎯 Using all {days_per_run} trading days per run (full dataset)")
    else:
        print(f"🎯 Sampling {days_per_run} random days per run")
    print("="*80)
    
    for run_num in range(1, num_runs + 1):
        if days_per_run == len(all_trading_dates):
            # Use all dates but shuffle them for random trading sequence
            sampled_dates = all_trading_dates.copy()
            random.shuffle(sampled_dates)
            # Keep the shuffled order - this creates different compound growth patterns!
        else:
            # Randomly sample trading days (without replacement for this run)
            sampled_dates = random.sample(all_trading_dates, days_per_run)
            # Keep sampled dates in random order for varied sequence effects
        
        start_date_str = min(sampled_dates)  # Earliest date in sample
        end_date_str = max(sampled_dates)    # Latest date in sample
        
        if days_per_run == len(all_trading_dates):
            print(f"\n📊 Random Run {run_num}/{num_runs}: All {len(sampled_dates)} days (shuffled sequence)")
        else:
            print(f"\n📊 Random Run {run_num}/{num_runs}: {len(sampled_dates)} randomly sampled days")
        print(f"    Date span: {start_date_str} to {end_date_str} (processing in random order)")
        print("-" * 60)
        
        # Create backtester for this run
        backtester = HistoricalBacktester(
            start_date=start_date_str,
            end_date=end_date_str,
            volatility=args.volatility,
            simple_mode=args.simple_mode,
            pricing_mode=args.pricing_mode,
            skip_monday=args.skip_monday,
            starting_balance=args.starting_balance,
            max_risk_per_trade_pc=args.max_risk_per_trade_pc,
            max_risk_bull_pc=args.max_risk_bull_pc,
            max_risk_bear_pc=args.max_risk_bear_pc,
            max_risk_iron_butterfly_pc=args.max_risk_iron_butterfly_pc,
            simple_spread_cost=args.simple_spread_cost,
            simple_butterfly_credit=args.simple_butterfly_credit,
            simple_itm_threshold=args.simple_itm_threshold,
            alternate_strategy=args.alternate_strategy,
            butterfly_conversion_only=args.butterfly_conversion_only,
            butterfly_narrow_conversions=args.butterfly_narrow_conversions,
            butterfly_conversion_with_narrows=args.butterfly_conversion_with_narrows,
            butterfly_conversion_latest=args.butterfly_conversion_latest,
            extra_butterfly=args.extra_butterfly,
            vix_based_threshold=args.vix_based_threshold
        )
        
        # Override the trading dates to use our random sample
        backtester.all_trading_dates = sampled_dates
        
        # Run backtest on the sampled dates
        backtest_results = backtester.run_backtest_on_dates(sampled_dates)
        
        if backtest_results:
            # Generate summary
            summary = backtester.generate_summary_stats()
            summary['run_number'] = run_num
            summary['date_range'] = {
                'start': start_date_str,
                'end': end_date_str,
                'days': days_per_run
            }
            results.append(summary)
            
            # Print brief summary for this run
            print(f"  Total Return: {summary['total_return_pct']:+.1f}%")
            print(f"  Win Rate: {summary['win_rate_pct']:.1f}%")
            print(f"  Total Trades: {summary['total_trades']}")
            print(f"  Final Balance: ${summary['ending_balance']:.0f}")
            
            # Save individual results if requested
            if args.save_individual_runs:
                output_filename = f"random_run_{run_num:02d}_{start_date_str}_{end_date_str}"
                backtester.save_results_to_csv(
                    filename=f"backtest_results_{output_filename}.csv",
                    output_dir=args.output_dir
                )
                backtester.save_summary_to_json(
                    filename=f"backtest_summary_{output_filename}.json", 
                    output_dir=args.output_dir
                )
            
            # Save to database if requested
            if args.save_to_database:
                run_name = f"{args.run_name}_random_{run_num:02d}" if args.run_name else f"Random Run {run_num}"
                run_id = backtester.save_results_to_database(run_name=run_name)
                if run_id:
                    print(f"    📁 Saved to database with run_id: {run_id}")
        else:
            print(f"  ❌ Run {run_num} failed - no results generated")
    
    return results

def print_random_results_summary(results: List[Dict[str, Any]], days_per_run: int):
    """Print summary statistics across all random runs"""
    if not results:
        print("❌ No successful runs to summarize")
        return
    
    print(f"\n{'='*80}")
    print(f"RANDOM BACKTEST SUMMARY ({len(results)} runs, {days_per_run} days each)")
    print("="*80)
    
    # Calculate aggregate statistics
    returns = [r['total_return_pct'] for r in results]
    win_rates = [r['win_rate_pct'] for r in results]
    total_trades = [r['total_trades'] for r in results]
    final_balances = [r['ending_balance'] for r in results]
    
    print(f"📊 RETURN STATISTICS")
    print(f"  Average Return: {sum(returns)/len(returns):+.1f}%")
    print(f"  Median Return:  {sorted(returns)[len(returns)//2]:+.1f}%")
    print(f"  Best Return:    {max(returns):+.1f}%")
    print(f"  Worst Return:   {min(returns):+.1f}%")
    print(f"  Std Deviation:  {(sum([(r - sum(returns)/len(returns))**2 for r in returns])/len(returns))**0.5:.1f}%")
    
    print(f"\n📊 WIN RATE STATISTICS") 
    print(f"  Average Win Rate: {sum(win_rates)/len(win_rates):.1f}%")
    print(f"  Median Win Rate:  {sorted(win_rates)[len(win_rates)//2]:.1f}%")
    print(f"  Best Win Rate:    {max(win_rates):.1f}%")
    print(f"  Worst Win Rate:   {min(win_rates):.1f}%")
    
    print(f"\n📊 TRADE VOLUME")
    print(f"  Average Trades per Run: {sum(total_trades)/len(total_trades):.1f}")
    print(f"  Total Trades Analyzed:  {sum(total_trades)}")
    
    print(f"\n📊 PERFORMANCE DISTRIBUTION")
    positive_returns = [r for r in returns if r > 0]
    negative_returns = [r for r in returns if r < 0]
    
    print(f"  Profitable Runs: {len(positive_returns)}/{len(results)} ({len(positive_returns)/len(results)*100:.1f}%)")
    print(f"  Losing Runs:     {len(negative_returns)}/{len(results)} ({len(negative_returns)/len(results)*100:.1f}%)")
    
    if positive_returns:
        print(f"  Avg Profitable Run: {sum(positive_returns)/len(positive_returns):+.1f}%")
    if negative_returns:
        print(f"  Avg Losing Run:     {sum(negative_returns)/len(negative_returns):+.1f}%")
    
    # Show individual run details
    print(f"\n📋 INDIVIDUAL RUN DETAILS")
    print("Run#  Date Range              Return    Win Rate  Trades  Final Balance")
    print("-" * 75)
    for i, result in enumerate(results, 1):
        date_range = f"{result['date_range']['start']} to {result['date_range']['end']}"
        print(f"{i:2d}    {date_range:20s}  {result['total_return_pct']:+6.1f}%    {result['win_rate_pct']:5.1f}%    {result['total_trades']:4d}    ${result['ending_balance']:8.0f}")

def main():
    """Main entry point for the backtester"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SPX Historical Backtester')
    parser.add_argument('--start-date', help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', help='End date (YYYY-MM-DD)')
    parser.add_argument('--volatility', type=float, default=0.45, help='Volatility assumption (default: 0.45)')
    parser.add_argument('--starting-balance', type=float, default=10000.0, help='Starting account balance (default: 10000)')
    parser.add_argument('--max-risk-per-trade-pc', type=float, default=0.07, help='Default maximum risk per trade as percentage of balance (default: 0.07 = 7 percent)')
    parser.add_argument('--max-risk-bull-pc', type=float, help='Maximum risk for bull strategies as percentage of balance (if not specified, uses --max-risk-per-trade-pc)')
    parser.add_argument('--max-risk-bear-pc', type=float, help='Maximum risk for bear strategies as percentage of balance (if not specified, uses --max-risk-per-trade-pc)')
    parser.add_argument('--max-risk-iron-butterfly-pc', type=float, help='Maximum risk for iron butterfly strategies as percentage of balance (if not specified, uses --max-risk-per-trade-pc)')
    parser.add_argument('--simple-spread-cost', type=float, default=2.20, help='Fixed cost for spreads in simple mode (default: 2.20)')
    parser.add_argument('--simple-butterfly-credit', type=float, default=3.40, help='Fixed credit for butterflies in simple mode (default: 3.40)')
    parser.add_argument('--simple-itm-threshold', type=float, default=2.50, help='ITM threshold for simple mode optimization (default: 2.50)')
    parser.add_argument('--output-dir', default='backtesting', help='Output directory for results')
    parser.add_argument('--simple-mode', action='store_true', help='Use simple mode with simplified strike selection')
    parser.add_argument('--pricing-mode', choices=['fixed', 'basic', 'advanced'], default='fixed', help='Pricing model: fixed, basic (linear), or advanced (Black-Scholes) (default: fixed)')
    parser.add_argument('--skip-monday', action='store_true', help='Skip Monday and post-gap day trades')
    parser.add_argument('--alternate-strategy', action='store_true', help='Use alternate strategy: narrow_bull→bull, narrow_bear→bear, iron_butterfly→bull/bear based on 1st bar close >66%')
    parser.add_argument('--butterfly-conversion-only', action='store_true', help='Convert only iron butterflies to spreads (iron_butterfly→bull/bear based on 1st bar close >66%), keep narrow strategies unchanged')
    parser.add_argument('--butterfly-narrow-conversions', action='store_true', help='Convert iron butterflies to bull/bear based on 1st bar close >66%, but use narrow_bull/narrow_bear if span3 < 4 points')
    parser.add_argument('--butterfly-conversion-with-narrows', action='store_true', help='Convert butterflies based on 1st bar close >=66% and span3 threshold of 4 points (default False)')
    parser.add_argument('--butterfly-conversion-latest', action='store_true', help='Latest conversion mode with dual trades for narrow strategies (default False)')
    parser.add_argument('--extra-butterfly', action='store_true', help='Enable extra butterfly mode: place iron butterfly at 15:56:30 based on new target price')
    parser.add_argument('--vix-based-threshold', action='store_true', help='Use VIX-based narrow conversion threshold: 4.0 when VIX > 16.5, 6.0 when VIX <= 16.5 (default False)')
    parser.add_argument('--skip-target-validation', action='store_true', help='Skip target price validation for narrow spreads (backtesting only - allows target below execution for narrow_bull and target above execution for narrow_bear)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose logging')
    
    # Random backtest options
    parser.add_argument('--random', type=int, help='Run multiple random backtests (specify number of runs)')
    parser.add_argument('--days-per-run', type=int, help='Number of days for each random backtest run (if not specified, uses all available days)')
    parser.add_argument('--save-individual-runs', action='store_true', help='Save CSV/JSON for each individual random run')
    
    # Database output options
    parser.add_argument('--run-name', help='User-friendly name for this backtest run')
    parser.add_argument('--save-to-database', action='store_true', help='Save results to database (requires DB_* environment variables)')
    
    # Trade report generation options
    parser.add_argument('--generate-trade-reports', action='store_true', help='Generate detailed reports for all trades and save to database')
    parser.add_argument('--generate-losing-reports', action='store_true', help='Generate detailed reports only for losing trades and save to database')
    parser.add_argument('--generate-reports-to-files', action='store_true', help='Generate markdown files instead of database reports (legacy mode)')
    parser.add_argument('--trade-reports-dir', default='backtest_trade_reports', help='Directory for trade reports when using --generate-reports-to-files')
    
    args = parser.parse_args()
    
    # Set up logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Handle random backtests
    if args.random:
        if args.random < 1:
            print("❌ Error: --random must be 1 or greater")
            return
        
        if args.start_date or args.end_date:
            print("⚠️  Warning: --start-date and --end-date are ignored when using --random")
        
        try:
            random_results = run_random_backtests(args.random, args.days_per_run, args)
            print_random_results_summary(random_results, args.days_per_run)
            
            # Save aggregate results
            if random_results:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                aggregate_filename = f"random_backtest_summary_{args.random}runs_{args.days_per_run}days_{timestamp}.json"
                
                aggregate_data = {
                    'metadata': {
                        'num_runs': args.random,
                        'days_per_run': args.days_per_run,
                        'timestamp': timestamp,
                        'parameters': vars(args)
                    },
                    'individual_runs': random_results,
                    'aggregate_statistics': {
                        'returns': {
                            'average': sum(r['total_return_pct'] for r in random_results) / len(random_results),
                            'median': sorted([r['total_return_pct'] for r in random_results])[len(random_results)//2],
                            'best': max(r['total_return_pct'] for r in random_results),
                            'worst': min(r['total_return_pct'] for r in random_results),
                            'std_dev': (sum([(r['total_return_pct'] - sum(res['total_return_pct'] for res in random_results)/len(random_results))**2 for r in random_results])/len(random_results))**0.5
                        },
                        'win_rates': {
                            'average': sum(r['win_rate_pct'] for r in random_results) / len(random_results),
                            'median': sorted([r['win_rate_pct'] for r in random_results])[len(random_results)//2],
                            'best': max(r['win_rate_pct'] for r in random_results),
                            'worst': min(r['win_rate_pct'] for r in random_results)
                        },
                        'profitability': {
                            'profitable_runs': len([r for r in random_results if r['total_return_pct'] > 0]),
                            'total_runs': len(random_results),
                            'success_rate': len([r for r in random_results if r['total_return_pct'] > 0]) / len(random_results) * 100
                        }
                    }
                }
                
                import json
                with open(os.path.join(args.output_dir, aggregate_filename), 'w') as f:
                    json.dump(aggregate_data, f, indent=2, default=str)
                
                print(f"\n💾 Aggregate results saved to {args.output_dir}/{aggregate_filename}")
        
        except Exception as e:
            print(f"❌ Error running random backtests: {e}")
            return
        
        return
    
    # Regular single backtest
    # Run backtest
    backtester = HistoricalBacktester(
        start_date=args.start_date, 
        end_date=args.end_date, 
        volatility=args.volatility, 
        simple_mode=args.simple_mode, 
        pricing_mode=args.pricing_mode,
        skip_monday=args.skip_monday,
        starting_balance=args.starting_balance,
        max_risk_per_trade_pc=args.max_risk_per_trade_pc,
        max_risk_bull_pc=args.max_risk_bull_pc,
        max_risk_bear_pc=args.max_risk_bear_pc,
        max_risk_iron_butterfly_pc=args.max_risk_iron_butterfly_pc,
        simple_spread_cost=args.simple_spread_cost,
        simple_butterfly_credit=args.simple_butterfly_credit,
        simple_itm_threshold=args.simple_itm_threshold,
        alternate_strategy=args.alternate_strategy,
        butterfly_conversion_only=args.butterfly_conversion_only,
        butterfly_narrow_conversions=args.butterfly_narrow_conversions,
        butterfly_conversion_with_narrows=args.butterfly_conversion_with_narrows,
        butterfly_conversion_latest=args.butterfly_conversion_latest,
        extra_butterfly=args.extra_butterfly,
        vix_based_threshold=args.vix_based_threshold,
        skip_target_validation=args.skip_target_validation
    )
    results = backtester.run_backtest()
    
    if results:
        # Print summary
        backtester.print_summary()
        
        # Save results
        backtester.save_results_to_csv(output_dir=args.output_dir)
        backtester.save_summary_to_json(output_dir=args.output_dir)
        
        # Save to database if connection string provided
        run_id = None
        if args.save_to_database:
            run_id = backtester.save_results_to_database(run_name=args.run_name)
        
        # Generate trade reports if requested
        if args.generate_trade_reports:
            if args.generate_reports_to_files:
                print(f"\n📊 Generating detailed trade reports for all {len(results)} trades to files...")
                backtester.generate_all_trades_reports(results, args.trade_reports_dir)
                print(f"Trade reports: Saved to {args.trade_reports_dir}/")
            else:
                print(f"\n📊 Generating detailed trade reports for all {len(results)} trades to database...")
                report_ids = backtester.generate_all_trades_reports_to_database(results)
                print(f"Trade reports: {len(report_ids)} reports saved to database")
            
        elif args.generate_losing_reports:
            if args.generate_reports_to_files:
                print(f"\n📊 Generating detailed trade reports for losing trades to files...")
                backtester.generate_losing_trades_reports(results, args.trade_reports_dir)
                print(f"Trade reports: Saved to {args.trade_reports_dir}/")
            else:
                print(f"\n📊 Generating detailed trade reports for losing trades to database...")
                report_ids = backtester.generate_losing_trades_reports_to_database(results)
                print(f"Trade reports: {len(report_ids)} reports saved to database")
        
        print(f"\nResults saved to {args.output_dir}/")
        if args.simple_mode:
            print(f"(Simple mode was enabled: Fixed costs ${backtester.simple_spread_cost:.2f} for spreads, -${backtester.simple_butterfly_credit:.2f} for butterflies)")
        print(f"Position sizing: ${args.starting_balance:.0f} starting balance, {args.max_risk_per_trade_pc:.1%} max risk per trade")
        if run_id:
            print(f"Database: Saved with run_id {run_id}")
    else:
        print("No results generated.")


if __name__ == "__main__":
    main() 