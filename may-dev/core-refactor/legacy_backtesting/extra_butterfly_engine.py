#!/usr/bin/env python3
"""
Extra Butterfly Engine for SPX Backtesting
Handles the extra-butterfly logic at 15:56:30
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class ExtraButterflyResult:
    """Result of extra-butterfly analysis"""
    should_trade: bool
    new_target: float
    case_description: str
    expected_change: float
    realized_change: float
    total_change: float
    century_adjustment: bool
    reasoning: str
    case: str = ""
    strategy_type: str = "iron_butterfly"
    strikes: list = None
    
    def __post_init__(self):
        if self.strikes is None:
            self.strikes = []
        if not self.case:
            self.case = self.case_description


class ExtraButterflyEngine:
    """Handles the extra-butterfly logic at 15:56:30"""
    
    def process_extra_butterfly(
        self,
        original_target: float,
        spx_1530: float,
        spx_1556: float,
        strategy_type: str
    ) -> ExtraButterflyResult:
        """
        Process extra butterfly logic for the historical backtester
        
        Args:
            original_target: Original target price from 15:51:30 decision
            spx_1530: SPX price at 15:51:30
            spx_1556: SPX price at 15:56:30
            strategy_type: Original strategy type (bull, bear, narrow_bull, narrow_bear, iron_butterfly)
            
        Returns:
            ExtraButterflyResult with decision and strike information
        """
        # Use the existing analysis method
        result = self.analyze_extra_butterfly_opportunity(
            original_decision=strategy_type,
            execution_price=spx_1530,
            target_price=original_target,
            spx_56_30=spx_1556
        )
        
        # Add strike information for iron butterfly
        if result.should_trade:
            # Create iron butterfly strikes around the new target
            center_strike = result.new_target
            wing_distance = 5  # 5-point wings
            
            strikes = [
                center_strike - wing_distance,  # Lower wing
                center_strike,                  # Center
                center_strike + wing_distance   # Upper wing
            ]
            
            # Update the result with strike information
            result.strikes = strikes
            result.strategy_type = "iron_butterfly"
        
        return result
    
    def analyze_extra_butterfly_opportunity(
        self, 
        original_decision: str,
        execution_price: float,  # SPX at 15:51:30
        target_price: float,     # Original target
        spx_56_30: float         # SPX at 15:56:30
    ) -> ExtraButterflyResult:
        """
        Analyze whether to place an extra butterfly trade
        
        Args:
            original_decision: Original decision (bull, bear, narrow_bull, narrow_bear)
            execution_price: SPX price at 15:51:30
            target_price: Original target price
            spx_56_30: SPX price at 15:56:30
            
        Returns:
            ExtraButterflyResult with decision and new target
        """
        # Step 1: Calculate changes
        expected_change = target_price - execution_price
        realized_change = spx_56_30 - target_price
        total_change = abs(expected_change - realized_change)
        
        logger.info(f"Extra Butterfly Analysis:")
        logger.info(f"  Original Decision: {original_decision}")
        logger.info(f"  Execution Price (15:51:30): {execution_price:.2f}")
        logger.info(f"  Target Price: {target_price:.2f}")
        logger.info(f"  SPX 15:56:30: {spx_56_30:.2f}")
        logger.info(f"  Expected Change: {expected_change:.2f}")
        logger.info(f"  Realized Change: {realized_change:.2f}")
        logger.info(f"  Total Change: {total_change:.2f}")
        
        # Step 2: Apply case logic
        if total_change < 4:
            case_description = "on-target"
            new_target = target_price
            reasoning = f"Case: {case_description}, Total Change: {total_change:.1f} < 4"
        elif total_change >= 4 and total_change < 6:
            case_description = "drifting-closest"
            new_target = self._get_closest_strike(spx_56_30)
            reasoning = f"Case: {case_description}, Total Change: {total_change:.1f} (4-6), New Target: {new_target:.0f}"
        elif total_change >= 6 and total_change < 10:
            if expected_change * realized_change > 0:  # Same sign
                case_description = "overshoot-continue"
                new_target = self._get_strike_towards_direction(spx_56_30, original_decision)
                reasoning = f"Case: {case_description}, Total Change: {total_change:.1f} (6-10), Same Direction, New Target: {new_target:.0f}"
            else:  # Different sign
                case_description = "reversal-continue"
                new_target = self._get_strike_opposite_direction(spx_56_30, original_decision)
                reasoning = f"Case: {case_description}, Total Change: {total_change:.1f} (6-10), Opposite Direction, New Target: {new_target:.0f}"
        else:  # total_change >= 10
            return ExtraButterflyResult(
                should_trade=False,
                new_target=0.0,
                case_description="too-wild",
                expected_change=expected_change,
                realized_change=realized_change,
                total_change=total_change,
                century_adjustment=False,
                reasoning=f"Case: too-wild, Total Change: {total_change:.1f} >= 10, No Trade"
            )
        
        # Step 3: Apply distance constraint
        if abs(new_target - spx_56_30) > 5:
            new_target = self._move_strike_closer(new_target, spx_56_30)
            reasoning += f", Distance Adjusted: {new_target:.0f}"
        
        # Step 4: Apply century adjustment
        century_adjustment = False
        nearest_century = self._get_nearest_century(spx_56_30)
        if abs(spx_56_30 - nearest_century) <= 5:
            new_target = self._move_strike_towards_century(new_target, spx_56_30)
            century_adjustment = True
            reasoning += f", Century Adjusted: {new_target:.0f}"
        
        return ExtraButterflyResult(
            should_trade=True,
            new_target=new_target,
            case_description=case_description,
            expected_change=expected_change,
            realized_change=realized_change,
            total_change=total_change,
            century_adjustment=century_adjustment,
            reasoning=reasoning
        )
    
    def _get_closest_strike(self, price: float) -> float:
        """Get the closest 5-point strike to the given price"""
        return round(price / 5) * 5
    
    def _get_strike_towards_direction(self, price: float, original_decision: str) -> float:
        """Get strike one step towards the original decision direction"""
        closest_strike = self._get_closest_strike(price)
        
        if original_decision in ["bull", "narrow_bull"]:
            # For bull decisions, move up one strike
            return closest_strike + 5
        elif original_decision in ["bear", "narrow_bear"]:
            # For bear decisions, move down one strike
            return closest_strike - 5
        else:
            return closest_strike
    
    def _get_strike_opposite_direction(self, price: float, original_decision: str) -> float:
        """Get strike one step opposite to the original decision direction"""
        closest_strike = self._get_closest_strike(price)
        
        if original_decision in ["bull", "narrow_bull"]:
            # For bull decisions, move down one strike (opposite)
            return closest_strike - 5
        elif original_decision in ["bear", "narrow_bear"]:
            # For bear decisions, move up one strike (opposite)
            return closest_strike + 5
        else:
            return closest_strike
    
    def _move_strike_closer(self, strike: float, price: float) -> float:
        """Move strike closer to price if distance > 5"""
        if abs(strike - price) > 5:
            if strike > price:
                return strike - 5
            else:
                return strike + 5
        return strike
    
    def _get_nearest_century(self, price: float) -> float:
        """Get the nearest century mark (e.g., 5800, 5900)"""
        return round(price / 100) * 100
    
    def _move_strike_towards_century(self, strike: float, price: float) -> float:
        """Move strike one step towards the century mark"""
        nearest_century = self._get_nearest_century(price)
        
        if price < nearest_century:
            # Price is below century, move strike down
            return strike - 5
        else:
            # Price is above century, move strike up
            return strike + 5


if __name__ == "__main__":
    # Test the extra butterfly engine
    logging.basicConfig(level=logging.DEBUG)
    
    engine = ExtraButterflyEngine()
    
    # Test case 1: on-target (total_change < 4)
    # Execution: 5870, Target: 5875, SPX_56_30: 5874
    # Expected: 5, Realized: -1, Total: 6 → Should be 6-10 range
    print("Test 1: on-target case")
    result1 = engine.analyze_extra_butterfly_opportunity(
        "bull", 5870.0, 5875.0, 5874.0
    )
    print(f"Result: {result1}")
    
    # Test case 2: drifting-closest (total_change 4-6)
    # Execution: 5870, Target: 5875, SPX_56_30: 5879
    # Expected: 5, Realized: 4, Total: 1 → Should be < 4
    print("\nTest 2: drifting-closest case")
    result2 = engine.analyze_extra_butterfly_opportunity(
        "bull", 5870.0, 5875.0, 5879.0
    )
    print(f"Result: {result2}")
    
    # Test case 3: overshoot-continue (total_change 6-10, same direction)
    # Execution: 5870, Target: 5875, SPX_56_30: 5881
    # Expected: 5, Realized: 6, Total: 1 → Should be < 4
    print("\nTest 3: overshoot-continue case")
    result3 = engine.analyze_extra_butterfly_opportunity(
        "bull", 5870.0, 5875.0, 5881.0
    )
    print(f"Result: {result3}")
    
    # Test case 4: reversal-continue (total_change 6-10, opposite direction)
    # Execution: 5870, Target: 5875, SPX_56_30: 5869
    # Expected: 5, Realized: -6, Total: 11 → Should be >= 10
    print("\nTest 4: reversal-continue case")
    result4 = engine.analyze_extra_butterfly_opportunity(
        "bull", 5870.0, 5875.0, 5869.0
    )
    print(f"Result: {result4}")
    
    # Test case 5: too-wild (total_change >= 10)
    # Execution: 5870, Target: 5875, SPX_56_30: 5890
    # Expected: 5, Realized: 15, Total: 10 → Should be >= 10
    print("\nTest 5: too-wild case")
    result5 = engine.analyze_extra_butterfly_opportunity(
        "bull", 5870.0, 5875.0, 5890.0
    )
    print(f"Result: {result5}") 