#!/usr/bin/env python3
"""
Test script to verify the pricing changes:
1. Max debit changed from 2.40 to 2.50
2. Spread prices rounded up to nearest 5 cents
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from options_pricer import price_bull_call_spread, price_bear_put_spread, round_up_to_nickel, find_optimal_strikes

def test_round_up_to_nickel():
    """Test the round up to nickel functionality"""
    print("🔍 Testing Round Up to Nickel Function")
    print("=" * 40)
    
    test_cases = [
        (2.42, 2.45),    # 2.42 → 2.45
        (2.47, 2.50),    # 2.47 → 2.50  
        (2.50, 2.50),    # 2.50 → 2.50 (no change)
        (2.51, 2.55),    # 2.51 → 2.55
        (1.83, 1.85),    # 1.83 → 1.85
        (1.00, 1.00),    # 1.00 → 1.00 (no change)
        (0.99, 1.00),    # 0.99 → 1.00
    ]
    
    all_passed = True
    for original, expected in test_cases:
        result = round_up_to_nickel(original)
        passed = abs(result - expected) < 0.001
        status = "✅" if passed else "❌"
        print(f"  {status} ${original:.2f} → ${result:.2f} (expected ${expected:.2f})")
        if not passed:
            all_passed = False
    
    print(f"\nRound up to nickel test: {'✅ PASSED' if all_passed else '❌ FAILED'}")
    return all_passed

def test_max_debit_change():
    """Test that max debit is now 2.50 instead of 2.40"""
    print("\n🔍 Testing Max Debit Change (2.40 → 2.50)")
    print("=" * 40)
    
    # Test a scenario where the spread would be priced between 2.40 and 2.50
    # This should now be acceptable (not trigger strike movement)
    S = 6007.5  # SPX price
    
    # Try to find strikes for both bull and bear
    bull_long, bull_short, bull_price = find_optimal_strikes(S, 'bull', 6005)
    bear_long, bear_short, bear_price = find_optimal_strikes(S, 'bear', 6010)
    
    print(f"  Bull spread: {bull_long}/{bull_short} @ ${bull_price:.2f}")
    print(f"  Bear spread: {bear_long}/{bear_short} @ ${bear_price:.2f}")
    
    # Test that prices between 2.40 and 2.50 are acceptable
    max_debit_test_passed = True
    if bull_price > 2.50:
        print(f"  ❌ Bull price ${bull_price:.2f} exceeds new max debit of $2.50")
        max_debit_test_passed = False
    if bear_price > 2.50:
        print(f"  ❌ Bear price ${bear_price:.2f} exceeds new max debit of $2.50")
        max_debit_test_passed = False
    
    if max_debit_test_passed:
        print(f"  ✅ All prices within new max debit of $2.50")
    
    return max_debit_test_passed

def test_spread_pricing_rounding():
    """Test that spread prices are rounded up to nearest 5 cents"""
    print("\n🔍 Testing Spread Price Rounding")
    print("=" * 40)
    
    # Test various SPX prices to see spread pricing
    spx_prices = [6005.0, 6007.5, 6010.0, 6012.5, 6015.0]
    
    print("SPX Price | Bull 6005/6010 | Bear 6010/6005 | Rounded?")
    print("-" * 50)
    
    all_rounded = True
    for S in spx_prices:
        bull_price = price_bull_call_spread(S, 6005, 6010)
        bear_price = price_bear_put_spread(S, 6010, 6005)
        
        # Check if prices are rounded to nearest nickel
        bull_rounded = abs(bull_price * 20 - round(bull_price * 20)) < 0.001
        bear_rounded = abs(bear_price * 20 - round(bear_price * 20)) < 0.001
        
        status = "✅" if (bull_rounded and bear_rounded) else "❌"
        print(f"{S:8.1f} | ${bull_price:11.2f} | ${bear_price:11.2f} | {status}")
        
        if not (bull_rounded and bear_rounded):
            all_rounded = False
    
    print(f"\nSpread pricing rounding test: {'✅ PASSED' if all_rounded else '❌ FAILED'}")
    return all_rounded

def main():
    """Run all pricing tests"""
    print("🧪 Testing Pricing Changes")
    print("=" * 50)
    print("1. Max debit: 2.40 → 2.50")
    print("2. Round quoted prices up to nearest 5 cents")
    print("=" * 50)
    
    # Run tests
    test1_passed = test_round_up_to_nickel()
    test2_passed = test_max_debit_change() 
    test3_passed = test_spread_pricing_rounding()
    
    # Summary
    print("\n🏁 SUMMARY")
    print("=" * 20)
    print(f"Round up to nickel:  {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Max debit change:    {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    print(f"Spread rounding:     {'✅ PASSED' if test3_passed else '❌ FAILED'}")
    
    all_passed = test1_passed and test2_passed and test3_passed
    print(f"\nOverall: {'🎉 ALL TESTS PASSED' if all_passed else '⚠️  SOME TESTS FAILED'}")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 