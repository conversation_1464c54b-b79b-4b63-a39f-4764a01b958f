#!/usr/bin/env python3
"""
Backtest Trade Report Generator - Generates live-mode-style reports for backtest trades
"""

import os
import datetime as dt
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class BacktestDecisionEvent:
    """Single decision event in the backtest process (simulated)"""
    timestamp: dt.datetime
    event_type: str
    description: str
    data: Dict[str, Any] = field(default_factory=dict)
    
    def __str__(self) -> str:
        return f"{self.timestamp.strftime('%H:%M:%S')} - {self.event_type}: {self.description}"


class BacktestTradeReportGenerator:
    """Generates live-mode-style markdown trade reports for backtest trades"""
    
    def __init__(self, output_dir: str = "backtest_trade_reports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
    
    def generate_report(self, result, backtester) -> str:
        """
        Generate comprehensive markdown report for a backtest trade
        
        Args:
            result: BacktestResult object
            backtester: HistoricalBacktester instance
            
        Returns:
            Filepath of the generated report
        """
        # Generate filename with enhanced decision label
        decision_label = result.decision.replace('_', ' ').title()
        filename = f"{result.trading_date}_{result.decision}_backtest.md"
        filepath = self.output_dir / filename
        
        # Generate report content
        markdown = self._generate_live_style_markdown(result, backtester)
        
        # Save to file
        with open(filepath, 'w') as f:
            f.write(markdown)
        
        print(f"📊 Backtest trade report saved: {filepath}")
        return str(filepath)
    
    def _generate_live_style_markdown(self, result, backtester) -> str:
        """Generate live-mode-style markdown content for backtest trade"""
        
        # Simulate session times (market hours)
        session_start = dt.datetime.strptime(f"{result.trading_date} 15:45:00", "%Y-%m-%d %H:%M:%S")
        session_end = dt.datetime.strptime(f"{result.trading_date} 16:00:00", "%Y-%m-%d %H:%M:%S")
        session_duration = (session_end - session_start).total_seconds()
        
        # Header with success indicator
        success_icon = "✅" if result.trade_successful else "❌"
        pnl_color = "🟢" if result.profit_loss > 0 else "🔴" if result.profit_loss < 0 else "⚪"
        mode = f"📈 BACKTEST {success_icon}"
        
        # Format decision label
        decision_label = result.decision.replace('_', ' ').title()
        
        markdown = f"""# SPX Trading Report - {mode}

## Session Summary
- **Date:** {result.trading_date}
- **Time:** 15:45:00 - 16:00:00 (Simulated)
- **Duration:** {session_duration:.0f} seconds
- **Final Decision:** **{decision_label}**
- **Decision Type:** {result.decision}
- **Trade Executed:** Yes
- **Result:** {success_icon} {"SUCCESS" if result.trade_successful else "FAILURE"}
- **P&L:** {pnl_color} **${result.profit_loss:,.2f}**

## Market Data Analysis

### Bar Data Summary
| Bar | Time | Open | High | Low | Close | Range | Close % |
|-----|------|------|------|-----|-------|-------|---------|
"""
        
        # Add bar data with close percentages and timestamps
        bar_times = ["15:50:00", "15:50:30", "15:51:00"]
        for i, (bar, close_pct, bar_time) in enumerate(zip(result.bars, result.close_percentages, bar_times), 1):
            o, h, l, c = bar
            span_range = h - l
            markdown += f"| {i} | {bar_time} | {o:.2f} | {h:.2f} | {l:.2f} | {c:.2f} | {span_range:.2f} | {close_pct:.1f}% |\n"
        
        # Span calculations
        markdown += f"\n### Span Calculations\n"
        for i, (span_key, span_value) in enumerate(result.spans.items(), 1):
            if i <= len(result.bars):
                bar = result.bars[i-1]
                high, low = max(bar), min(bar)
                markdown += f"- **{span_key.upper()}:** {low:.2f} - {high:.2f} = {span_value:.2f} points\n"
        
        # Decision timeline (simulated events based on backtest data)
        markdown += f"\n## Decision Timeline\n\n"
        events = self._create_simulated_events(result)
        
        for i, event in enumerate(events, 1):
            icon = self._get_event_icon(event.event_type)
            markdown += f"### {i}. {icon} {event.event_type.replace('_', ' ').title()}\n"
            markdown += f"**Time:** {event.timestamp.strftime('%H:%M:%S')}\n\n"
            markdown += f"**Description:** {event.description}\n\n"
            
            if event.data:
                markdown += "**Details:**\n"
                for key, value in event.data.items():
                    markdown += f"- **{key.replace('_', ' ').title()}:** {value}\n"
            markdown += "\n---\n\n"
        
        # Strategy logic breakdown
        markdown += "## Strategy Logic Breakdown\n\n"
        
        # Thresholds used
        markdown += "### Decision Thresholds\n"
        markdown += "- **Bear Signal:** Close < 12% of span\n"
        markdown += "- **Bull Signal:** Close > 88% of span\n"
        markdown += "- **Iron Butterfly:** Close between 12%-88% of span\n"
        markdown += "- **Narrow Threshold:** SPAN3 < 4.0 points\n\n"
        
        # Final analysis based on backtest reasoning
        markdown += "### Final Analysis\n"
        markdown += f"- **Decision:** {result.decision}\n"
        markdown += f"- **Reason:** {result.reasoning}\n"
        if len(result.close_percentages) > 2:
            markdown += f"- **Close Percentage:** {result.close_percentages[2]:.1f}% (Bar 3)\n"
        markdown += f"- **Bias:** {result.bias}\n"
        
        # Trade execution details with enhanced strike information
        markdown += f"\n## Trade Execution\n\n"
        markdown += f"- **Decision:** **{decision_label}**\n"
        markdown += f"- **Strategy:** {result.decision}\n"
        markdown += f"- **Status:** {'Executed' if result.trade_successful else 'Failed'}\n"
        
        # Primary strike (center or reference strike)
        if len(result.final_strikes) >= 2:
            if result.decision in ["bull", "narrow_bull", "BF-bull"]:
                primary_strike = result.final_strikes[0]  # Long strike for bulls
            elif result.decision in ["bear", "narrow_bear", "BF-bear"]:
                primary_strike = result.final_strikes[0]  # Long strike for bears
            elif result.decision == "iron_butterfly":
                primary_strike = result.final_strikes[1] if len(result.final_strikes) > 1 else result.final_strikes[0]  # Center strike
            else:
                primary_strike = result.final_strikes[0]
            markdown += f"- **Primary Strike:** {primary_strike:.0f}\n"
        
        # Enhanced strike details with Long/Short positions
        markdown += f"\n### Strike Details\n"
        markdown = self._add_enhanced_strike_details(markdown, result)
        
        markdown += f"- **Execution Price:** ${result.execution_price:.2f}\n"
        markdown += f"- **Spread Price:** ${result.spread_price:.2f}\n"
        markdown += f"- **Target Price:** ${result.target_price:.2f}\n"
        markdown += f"- **Closing Price:** ${result.closing_price:.2f}\n"
        
        # Risk management section
        markdown += f"\n## Risk Management\n\n"
        markdown += f"- **Account Balance:** ${result.current_balance:,.2f}\n"
        markdown += f"- **Max Risk Allowed:** ${result.max_risk_amount:,.2f}\n"
        markdown += f"- **Risk Per Contract:** ${result.risk_per_contract:.2f}\n"
        markdown += f"- **Contracts Traded:** {result.num_contracts}\n"
        markdown += f"- **Actual Risk Taken:** ${result.actual_risk_amount:,.2f}\n"
        markdown += f"- **New Balance:** ${result.new_balance:,.2f}\n"
        
        # Technical details
        markdown += f"\n## Technical Details\n\n"
        markdown += f"- **Algorithm Version:** Backtesting v1.0\n"
        markdown += f"- **Environment:** Historical Backtest\n"
        markdown += f"- **Processing Time:** {result.processing_time:.3f}s\n"
        markdown += f"- **Strikes Moved:** {'Yes' if result.strikes_moved else 'No'}\n"
        
        # Footer
        markdown += f"\n---\n*Report generated at {dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n"
        
        return markdown
    
    def _add_enhanced_strike_details(self, markdown: str, result) -> str:
        """Add enhanced strike details with Long/Short positions"""
        if len(result.final_strikes) == len(result.option_types) == len(result.actions):
            for i, (strike, option_type, action) in enumerate(zip(result.final_strikes, result.option_types, result.actions), 1):
                position_type = "**Long**" if action.lower() == "buy" else "**Short**"
                option_type_full = "Call" if option_type.lower() == "call" else "Put"
                markdown += f"- **Leg {i}:** {position_type} {strike:.0f} {option_type_full}\n"
        else:
            # Fallback for older data or incomplete information
            for i, strike in enumerate(result.final_strikes, 1):
                markdown += f"- **Strike {i}:** {strike:.0f}\n"
        
        return markdown
    
    def _create_simulated_events(self, result) -> List[BacktestDecisionEvent]:
        """Create simulated events based on backtest data to mimic live trading timeline"""
        events = []
        base_time = dt.datetime.strptime(f"{result.trading_date} 15:45:00", "%Y-%m-%d %H:%M:%S")
        
        # Session start
        events.append(BacktestDecisionEvent(
            timestamp=base_time,
            event_type="session_start",
            description=f"Trading session started - Default decision: iron_butterfly",
            data={}
        ))
        
        # Bar analysis events
        bar_times = [
            base_time + dt.timedelta(minutes=5),      # 15:50:00
            base_time + dt.timedelta(minutes=5, seconds=30),  # 15:50:30
            base_time + dt.timedelta(minutes=6)       # 15:51:00
        ]
        
        for i, (bar, close_pct, bar_time) in enumerate(zip(result.bars, result.close_percentages, bar_times), 1):
            o, h, l, c = bar
            span_key = f"span{i}"
            span_value = result.spans.get(span_key, h - l)
            
            # Determine decision for this bar based on close percentage
            if close_pct < 12:
                bar_decision = "bear"
            elif close_pct > 88:
                bar_decision = "bull"
            else:
                bar_decision = "iron_butterfly"
            
            events.append(BacktestDecisionEvent(
                timestamp=bar_time,
                event_type="bar_analysis",
                description=f"Bar {i} Analysis: {bar_decision} - Close at {close_pct:.1f}% of span",
                data={
                    'bar_number': i,
                    'bar_ohlc': f"O:{o:.2f} H:{h:.2f} L:{l:.2f} C:{c:.2f}",
                    'span_range': f"{l:.2f} - {h:.2f} ({span_value:.2f} pts)",
                    'close_percentage': close_pct,
                    'decision': bar_decision
                }
            ))
        
        # Final decision event
        final_time = base_time + dt.timedelta(minutes=6, seconds=30)
        events.append(BacktestDecisionEvent(
            timestamp=final_time,
            event_type="final_decision",
            description=f"Final decision: {result.decision}",
            data={
                'reasoning': result.reasoning,
                'bias': result.bias,
                'final_strikes': result.final_strikes
            }
        ))
        
        # Execution event
        execution_time = base_time + dt.timedelta(minutes=7)
        execution_status = "successful" if result.trade_successful else "failed"
        events.append(BacktestDecisionEvent(
            timestamp=execution_time,
            event_type="execution",
            description=f"Trade execution {execution_status}: {result.decision} @ ${result.execution_price:.2f}",
            data={
                'strikes': result.final_strikes,
                'spread_price': result.spread_price,
                'execution_price': result.execution_price,
                'success': result.trade_successful
            }
        ))
        
        return events
    
    def _get_event_icon(self, event_type: str) -> str:
        """Get emoji icon for event type"""
        icons = {
            'session_start': '🚀',
            'bar_analysis': '📊',
            'reversal': '🔄',
            'narrow_conversion': '⚡',
            'execution': '⚡',
            'final_decision': '🎯',
            'overshoot_analysis': '📈',
            'error': '❌',
            'retry': '🔄'
        }
        return icons.get(event_type, '📋') 