# Live-Style Backtest Trade Reports

This document explains how to generate trade reports in backtesting that match the format and detail level of live trading reports.

## Overview

The backtesting system now supports two report formats:

1. **Legacy Format**: Traditional backtest reports (original format)
2. **Live-Style Format**: Reports that match the comprehensive live trading format

## Key Features of Live-Style Reports

### ✅ Enhanced Decision Labels
- Shows friendly names like "Bull Spread", "Bear Spread", "Iron Butterfly"
- Instead of raw decision names like "bull", "bear", "iron_butterfly"

### ✅ Decision Timeline
- Simulated chronological events throughout the trading session
- Bar analysis events for each 30-second interval
- Final decision and execution events with timestamps

### ✅ Enhanced Strike Details
- **Long** and **Short** position labels for each leg
- Clear indication of Call/Put for each strike
- Example: "**Long** 6240 Call / **Short** 6245 Call"

### ✅ Live-Mode-Style Sections
- Session Summary with formatted decision labels
- Market Data Analysis with bar tables and span calculations
- Strategy Logic Breakdown with decision thresholds
- Trade Execution with enhanced details
- Risk Management section
- Technical Details

## Usage Examples

### Single Trade Report

```python
from historical_backtester import HistoricalBacktester

# Create backtester
backtester = HistoricalBacktester(
    start_date='2024-01-01',
    end_date='2024-01-05'
)

# Run backtest
results = backtester.run_backtest()

# Generate live-style report for a single trade
if results:
    trade_result = results[0]
    
    # Live-style format (default)
    live_path = backtester.generate_trade_report(
        trade_result, 
        output_dir="my_reports",
        use_live_style=True  # Default: True
    )
    
    # Legacy format for comparison
    legacy_path = backtester.generate_trade_report(
        trade_result, 
        output_dir="my_reports",
        use_live_style=False
    )
```

### Batch Report Generation

```python
# Generate reports for all losing trades (live-style)
losing_reports = backtester.generate_losing_trades_reports(
    results,
    output_dir="losing_trades_reports",
    use_live_style=True  # Default: True
)

# Generate reports for ALL trades (live-style)
all_reports = backtester.generate_all_trades_reports(
    results,
    output_dir="all_trades_reports",
    use_live_style=True  # Default: True
)
```

### Database Report Generation

```python
# Generate live-style reports and save to database
losing_report_ids = backtester.generate_losing_trades_reports_to_database(
    results,
    use_live_style=True  # Default: True
)

# Generate reports for all trades and save to database
all_report_ids = backtester.generate_all_trades_reports_to_database(
    results,
    use_live_style=True  # Default: True
)
```

## Command Line Usage

The existing command line interface now defaults to live-style formatting:

```bash
# Generate trade reports with live-style formatting (default)
python historical_backtester.py \
    --start-date 2024-01-01 \
    --end-date 2024-01-31 \
    --generate-trade-reports

# Generate reports to files instead of database
python historical_backtester.py \
    --start-date 2024-01-01 \
    --end-date 2024-01-31 \
    --generate-trade-reports \
    --generate-reports-to-files \
    --trade-reports-dir "my_custom_reports"

# Generate only losing trade reports
python historical_backtester.py \
    --start-date 2024-01-01 \
    --end-date 2024-01-31 \
    --generate-losing-reports
```

## Testing the New Functionality

Use the provided test script to see both formats side by side:

```bash
cd backtesting/
python test_live_style_reports.py
```

This will:
- Run a small backtest
- Generate both legacy and live-style reports
- Compare the formats
- Demonstrate batch generation

## Report Structure Comparison

### Legacy Format Sections:
- Trade Summary
- Market Analysis
- Decision Logic
- Strike Selection & Pricing
- Risk Management
- Trade Outcome Analysis

### Live-Style Format Sections:
- Session Summary (enhanced with decision labels)
- Market Data Analysis (enhanced bar tables)
- **Decision Timeline** (new - chronological events)
- Strategy Logic Breakdown (enhanced with thresholds)
- Trade Execution (enhanced with Long/Short details)
- Risk Management
- Technical Details

## Sample Output

### Decision Label Enhancement:
```markdown
## Session Summary
- **Final Decision:** **Bull Spread**
- **Decision Type:** bull_spread
- **Trade Executed:** Yes

## Trade Execution
- **Decision:** **Bull Spread**
- **Strategy:** bull_spread

### Strike Details
- **Leg 1:** **Long** 6240 Call
- **Leg 2:** **Short** 6245 Call
```

### Decision Timeline (New):
```markdown
## Decision Timeline

### 1. 🚀 Session Start
**Time:** 15:45:00
**Description:** Trading session started - Default decision: iron_butterfly

### 2. 📊 Bar Analysis
**Time:** 15:50:00
**Description:** Bar 1 Analysis: bull - Close at 92.3% of span
**Details:**
- **Bar Number:** 1
- **Bar Ohlc:** O:6240.50 H:6245.25 L:6238.75 C:6244.00
- **Span Range:** 6238.75 - 6245.25 (6.50 pts)
- **Close Percentage:** 92.3
```

## Backward Compatibility

- **Default**: Live-style formatting is enabled by default (`use_live_style=True`)
- **Legacy Support**: Set `use_live_style=False` to use original format
- **Existing Scripts**: Will automatically use live-style format unless explicitly changed

## Performance

- Live-style reports are slightly larger due to additional detail
- Generation time is similar between formats
- Memory usage is comparable

## Integration with Live Mode

The live-style backtest reports now match the format of actual live trading reports, making it easier to:
- Compare backtest results with live trading performance
- Use consistent analysis workflows
- Maintain unified reporting standards across environments 