#!/usr/bin/env python3
"""
Test script for VIX-based narrow conversion threshold functionality
Tests the integration of VIX data with butterfly conversion logic
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from historical_backtester import HistoricalBacktester
from decision_engine import DecisionR<PERSON>ult

def test_vix_threshold_logic():
    """Test the VIX-based threshold determination"""
    print("Testing VIX-Based Threshold Logic")
    print("=" * 50)
    
    # Test with VIX-based threshold enabled
    backtester = HistoricalBacktester(vix_based_threshold=True)
    
    # Note: These tests will require actual VIX data in the database
    # For demonstration, we'll test the logic with mock scenarios
    
    print("\n1. Testing VIX threshold getter...")
    
    # This will attempt to get VIX data from database
    # In a real scenario, you'd use actual trading dates with known VIX values
    test_date = "2024-01-15"  # Example date
    threshold = backtester.get_narrow_conversion_threshold(test_date)
    print(f"   Date: {test_date}")
    print(f"   Threshold: {threshold}")
    print(f"   Expected: 4.0 (default if no VIX data) or 4.0/6.0 based on VIX")
    
    # Test disabled mode
    print("\n2. Testing disabled VIX threshold...")
    backtester_disabled = HistoricalBacktester(vix_based_threshold=False)
    threshold_disabled = backtester_disabled.get_narrow_conversion_threshold(test_date)
    print(f"   Date: {test_date}")
    print(f"   Threshold: {threshold_disabled}")
    print(f"   Expected: 4.0 (always default when disabled)")
    assert threshold_disabled == 4.0, f"Expected 4.0, got {threshold_disabled}"
    print("   ✅ PASSED: Disabled mode returns 4.0")

def test_butterfly_conversion_with_vix():
    """Test butterfly conversion with VIX-based thresholds"""
    print("\n\nTesting Butterfly Conversion with VIX Thresholds")
    print("=" * 60)
    
    # Create backtester with both VIX threshold and butterfly conversion enabled
    backtester = HistoricalBacktester(
        butterfly_conversion_with_narrows=True,
        vix_based_threshold=True
    )
    
    test_date = "2024-01-15"
    
    # Test case 1: Iron butterfly with different span3 values
    print("\n1. Testing iron butterfly conversion with dynamic threshold...")
    
    # Test with span3 = 5.0 (should convert to bull/bear regardless of threshold)
    original_result = DecisionResult(
        decision="iron_butterfly",
        strikes=[5590, 5595, 5595, 5600],
        option_types=['P', 'P', 'C', 'C'],
        actions=['BUY', 'SELL', 'SELL', 'BUY'],
        target_price=5595.0,
        reasoning="Iron butterfly decision based on market conditions",
        bias=0,
        spans={'span1': 3.0, 'span2': 4.2, 'span3': 5.0},  # span3 = 5.0 (> both 4.0 and 6.0)
        close_percentages=[70.0, 50.0, 45.0]  # 1st bar close >= 66%
    )
    
    converted_result = backtester.apply_alternate_strategy_conversion(original_result, test_date)
    print(f"   Original: {original_result.decision}")
    print(f"   Converted: {converted_result.decision}")
    print(f"   SPAN3: {original_result.spans['span3']}")
    print(f"   Expected: bull (span3 5.0 > any threshold)")
    assert converted_result.decision == "bull", f"Expected bull, got {converted_result.decision}"
    print("   ✅ PASSED: High span3 converts to bull")
    
    # Test with span3 = 3.0 (should convert to narrow regardless of threshold)
    original_result_low = DecisionResult(
        decision="iron_butterfly",
        strikes=[5590, 5595, 5595, 5600],
        option_types=['P', 'P', 'C', 'C'],
        actions=['BUY', 'SELL', 'SELL', 'BUY'],
        target_price=5595.0,
        reasoning="Iron butterfly decision based on market conditions",
        bias=0,
        spans={'span1': 3.0, 'span2': 4.2, 'span3': 3.0},  # span3 = 3.0 (< both 4.0 and 6.0)
        close_percentages=[70.0, 50.0, 45.0]  # 1st bar close >= 66%
    )
    
    converted_result_low = backtester.apply_alternate_strategy_conversion(original_result_low, test_date)
    print(f"\n   Original: {original_result_low.decision}")
    print(f"   Converted: {converted_result_low.decision}")
    print(f"   SPAN3: {original_result_low.spans['span3']}")
    print(f"   Expected: narrow_bull (span3 3.0 < any threshold)")
    assert converted_result_low.decision == "narrow_bull", f"Expected narrow_bull, got {converted_result_low.decision}"
    print("   ✅ PASSED: Low span3 converts to narrow_bull")
    
    # Test with span3 = 5.0 (edge case that would depend on VIX)
    # This would only matter if VIX threshold was 6.0 and span3 was between 4.0 and 6.0
    original_result_edge = DecisionResult(
        decision="iron_butterfly",
        strikes=[5590, 5595, 5595, 5600],
        option_types=['P', 'P', 'C', 'C'],
        actions=['BUY', 'SELL', 'SELL', 'BUY'],
        target_price=5595.0,
        reasoning="Iron butterfly decision based on market conditions",
        bias=0,
        spans={'span1': 3.0, 'span2': 4.2, 'span3': 5.0},  # span3 = 5.0 (between 4.0 and 6.0)
        close_percentages=[70.0, 50.0, 45.0]  # 1st bar close >= 66%
    )
    
    converted_result_edge = backtester.apply_alternate_strategy_conversion(original_result_edge, test_date)
    print(f"\n   Original: {original_result_edge.decision}")
    print(f"   Converted: {converted_result_edge.decision}")
    print(f"   SPAN3: {original_result_edge.spans['span3']}")
    print(f"   Note: Result depends on VIX level for this date")
    print(f"   - If VIX > 16.5 (threshold=4.0): span3 5.0 >= 4.0 → bull")
    print(f"   - If VIX <= 16.5 (threshold=6.0): span3 5.0 < 6.0 → narrow_bull")
    
    # The actual result will depend on the VIX data for the test date
    if converted_result_edge.decision == "bull":
        print("   ✅ Result: VIX > 16.5, using threshold 4.0")
    elif converted_result_edge.decision == "narrow_bull":
        print("   ✅ Result: VIX <= 16.5, using threshold 6.0")
    else:
        print(f"   ❌ Unexpected result: {converted_result_edge.decision}")

def test_command_line_integration():
    """Test that command line arguments work properly"""
    print("\n\nTesting Command Line Integration")
    print("=" * 40)
    
    # Test argument parsing would go here
    # This is more of a manual test since we'd need to actually run the command
    print("To test command line integration, run:")
    print("python historical_backtester.py --butterfly-conversion-with-narrows --vix-based-threshold --start-date 2024-01-15 --end-date 2024-01-15 --verbose")
    print("")
    print("Expected behavior:")
    print("- Should show 'VIX-Based Threshold: ENABLED' in summary")
    print("- Should log VIX values and threshold decisions")
    print("- Should use dynamic thresholds in conversion logic")

if __name__ == "__main__":
    try:
        test_vix_threshold_logic()
        test_butterfly_conversion_with_vix()
        test_command_line_integration()
        
        print("\n" + "="*60)
        print("✅ ALL TESTS COMPLETED")
        print("="*60)
        print("\nNote: Some tests require actual VIX data in the IBDB_DEV.VIX_History table.")
        print("To fully test, ensure you have VIX data for the test dates used.")
        print("\nExample VIX data structure:")
        print("DATE                 | CLOSE")
        print("2024-01-15 00:00:00 | 15.2   (would use threshold 6.0)")
        print("2024-01-16 00:00:00 | 18.5   (would use threshold 4.0)")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 