#!/usr/bin/env python3
"""
Database Connector for SPX Historical Data
Handles connections to SQL Server database containing 10-second SPX data
"""

import os
import pyodbc
import pandas as pd
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging
from pathlib import Path

# Try to import dotenv, fall back gracefully if not available
try:
    import dotenv
    # Load .env file from the backtesting directory
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        dotenv.load_dotenv(env_path)
        print(f"Loaded environment variables from {env_path}")
except ImportError:
    print("python-dotenv not available, using system environment variables")

logger = logging.getLogger(__name__)


class DatabaseConnector:
    """Handle database connections and queries for SPX historical data"""
    
    def __init__(self):
        self.connection_string = self._build_connection_string()
        self.connection = None
    
    def _build_connection_string(self) -> str:
        """Build connection string from environment variables (matching main system)"""
        # Use same environment variable names as main trading system
        server = os.getenv('DB_IP') or '*************'
        database = os.getenv('DB_NAME') or 'ALGO_DATA'  # Changed from IBDB_DEV to ALGO_DATA
        username = os.getenv('DB_USER')
        password = os.getenv('DB_PASSWORD')
        
        # Always use SQL Server Authentication if we have credentials
        if username and password:
            conn_str = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={server};"
                f"DATABASE={database};"
                f"UID={username};"
                f"PWD={password};"
                f"TrustServerCertificate=yes;"
            )
            logger.info(f"Using SQL Server Authentication for server: {server}, database: {database}, user: {username}")
        else:
            # Only fall back to Windows Authentication if no credentials provided
            conn_str = f"DRIVER={{ODBC Driver 18 for SQL Server}};SERVER={server};DATABASE={database};Trusted_Connection=yes;TrustServerCertificate=yes;"
            logger.info(f"Using Windows Authentication for server: {server}, database: {database}")
        
        return conn_str
    
    def connect(self) -> bool:
        """Establish database connection"""
        try:
            self.connection = pyodbc.connect(self.connection_string)
            logger.info("Database connection established successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to database: {str(e)}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("Database connection closed")
    
    def execute_query(self, query: str, params: Optional[List] = None) -> pd.DataFrame:
        """Execute SQL query and return results as DataFrame"""
        if not self.connection:
            if not self.connect():
                raise Exception("Cannot establish database connection")
        
        try:
            if params:
                df = pd.read_sql(query, self.connection, params=params)
            else:
                df = pd.read_sql(query, self.connection)
            return df
        except Exception as e:
            logger.error(f"Query execution failed: {str(e)}")
            raise
    
    def get_trading_dates(self, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[str]:
        """Get list of available trading dates"""
        query = """
        SELECT DISTINCT CAST(date AS DATE) as trading_date 
        FROM [ALGO_DATA].[dbo].[SPX] 
        WHERE CAST(date AS TIME) = '15:59:50.000'
        """
        
        conditions = []
        params = []
        
        if start_date:
            conditions.append("CAST(date AS DATE) >= ?")
            params.append(start_date)
        
        if end_date:
            conditions.append("CAST(date AS DATE) <= ?")
            params.append(end_date)
        
        if conditions:
            query += " AND " + " AND ".join(conditions)
        
        query += " ORDER BY trading_date"
        
        df = self.execute_query(query, params if params else None)
        # Ensure the trading_date column is datetime type
        df['trading_date'] = pd.to_datetime(df['trading_date'])
        return df['trading_date'].dt.strftime('%Y-%m-%d').tolist()
    
    def get_daily_end_of_day_data(self, trading_date: str) -> Dict[str, Any]:
        """
        Get the specific time windows needed for backtesting for a single day
        
        Args:
            trading_date: Date in 'YYYY-MM-DD' format
            
        Returns:
            Dictionary with:
            - pre_bar: 15:49:50 data (for setting open of first bar)
            - bars_10sec: List of 9 10-second bars from 15:50:00 to 15:51:20
            - closing_price: 15:59:50 closing price
        """
        
        # Query for the pre-bar (15:49:50)
        pre_bar_query = """
        SELECT [id], [date], [open], [high], [low], [close]
        FROM [ALGO_DATA].[dbo].[SPX]
        WHERE CAST(date AS DATE) = ? 
        AND CAST(date AS TIME) = '15:49:50.000'
        """
        
        # Query for the 9 bars from 15:50:00 to 15:51:20 (every 10 seconds)
        # 15:50:00, 15:50:10, 15:50:20, 15:50:30, 15:50:40, 15:50:50, 15:51:00, 15:51:10, 15:51:20
        bars_query = """
        SELECT [id], [date], [open], [high], [low], [close]
        FROM [ALGO_DATA].[dbo].[SPX]
        WHERE CAST(date AS DATE) = ? 
        AND CAST(date AS TIME) BETWEEN '15:50:00.000' AND '15:51:20.000'
        AND DATEPART(SECOND, date) % 10 = 0
        ORDER BY date
        """
        
        # Query for closing price (15:59:50)
        closing_query = """
        SELECT [id], [date], [open], [high], [low], [close]
        FROM [ALGO_DATA].[dbo].[SPX]
        WHERE CAST(date AS DATE) = ? 
        AND CAST(date AS TIME) = '15:59:50.000'
        """
        
        try:
            pre_bar_df = self.execute_query(pre_bar_query, [trading_date])
            bars_df = self.execute_query(bars_query, [trading_date])
            closing_df = self.execute_query(closing_query, [trading_date])
            
            if pre_bar_df.empty:
                logger.warning(f"No pre-bar data found for {trading_date}")
                return None
            
            if len(bars_df) != 9:
                logger.warning(f"Expected 9 bars for {trading_date}, got {len(bars_df)}")
                return None
            
            if closing_df.empty:
                logger.warning(f"No closing price found for {trading_date}")
                return None
            
            return {
                'trading_date': trading_date,
                'pre_bar': pre_bar_df.iloc[0].to_dict(),
                'bars_10sec': bars_df.to_dict('records'),
                'closing_price': closing_df.iloc[0]['close']
            }
            
        except Exception as e:
            logger.error(f"Error fetching data for {trading_date}: {str(e)}")
            return None
    
    def get_narrow_fill_data(self, trading_date: str) -> List[Dict]:
        """
        Get 10-second bars from 15:51:30 to 16:00:00 for narrow fill checking
        
        Args:
            trading_date: Date in 'YYYY-MM-DD' format
            
        Returns:
            List of dictionaries with OHLC data for each 10-second bar
        """
        query = """
        SELECT [open], [high], [low], [close], [date]
        FROM [ALGO_DATA].[dbo].[SPX]
        WHERE CAST(date AS DATE) = ? 
        AND CAST(date AS TIME) BETWEEN '15:51:30.000' AND '16:00:00.000'
        AND DATEPART(SECOND, date) % 10 = 0
        ORDER BY date
        """
        
        try:
            df = self.execute_query(query, [trading_date])
            return df.to_dict('records')
        except Exception as e:
            logger.error(f"Error getting narrow fill data for {trading_date}: {str(e)}")
            return []
    
    def get_spx_price_at_time(self, trading_date: str, time_str: str) -> Optional[float]:
        """
        Get SPX price at a specific time on a trading date
        
        Args:
            trading_date: Date in 'YYYY-MM-DD' format
            time_str: Time in 'HH:MM:SS' format (e.g., '15:51:30')
            
        Returns:
            SPX price as float, or None if not found
        """
        query = """
        SELECT [close]
        FROM [ALGO_DATA].[dbo].[SPX]
        WHERE CAST(date AS DATE) = ? 
        AND CAST(date AS TIME) = ?
        """
        
        try:
            df = self.execute_query(query, [trading_date, time_str])
            
            if df.empty:
                logger.warning(f"No SPX data at {time_str} for {trading_date}")
                return None
            
            return float(df.iloc[0]['close'])
            
        except Exception as e:
            logger.error(f"Error getting SPX price at {time_str} for {trading_date}: {str(e)}")
            return None
    
    def get_extra_butterfly_data(self, trading_date: str) -> Dict[str, Any]:
        """
        Get data needed for extra-butterfly mode at 15:56:30
        
        Args:
            trading_date: Date in 'YYYY-MM-DD' format
            
        Returns:
            Dictionary with:
            - spx_51_30: SPX price at 15:51:30 (execution price)
            - spx_56_30: SPX price at 15:56:30 (new decision point)
        """
        # Query for SPX price at 15:51:30 (execution price)
        spx_51_30_query = """
        SELECT [close]
        FROM [ALGO_DATA].[dbo].[SPX]
        WHERE CAST(date AS DATE) = ? 
        AND CAST(date AS TIME) = '15:51:30.000'
        """
        
        # Query for SPX price at 15:56:30 (extra butterfly decision point)
        spx_56_30_query = """
        SELECT [open]
        FROM [ALGO_DATA].[dbo].[SPX]
        WHERE CAST(date AS DATE) = ? 
        AND CAST(date AS TIME) = '15:56:30.000'
        """
        
        try:
            spx_51_30_df = self.execute_query(spx_51_30_query, [trading_date])
            spx_56_30_df = self.execute_query(spx_56_30_query, [trading_date])
            
            if spx_51_30_df.empty:
                logger.warning(f"No SPX data at 15:51:30 for {trading_date}")
                return None
            
            if spx_56_30_df.empty:
                logger.warning(f"No SPX data at 15:56:30 for {trading_date}")
                return None
            
            return {
                'spx_51_30': float(spx_51_30_df.iloc[0]['close']),
                'spx_56_30': float(spx_56_30_df.iloc[0]['close'])
            }
            
        except Exception as e:
            logger.error(f"Error getting extra butterfly data for {trading_date}: {str(e)}")
            return None
    
    def get_vix_closing_price(self, trading_date: str) -> Optional[float]:
        """
        Get VIX closing price for a specific trading date
        
        Args:
            trading_date: Date in 'YYYY-MM-DD' format
            
        Returns:
            VIX closing price as float, or None if not found
        """
        query = """
        SELECT [CLOSE]
        FROM [IBDB_DEV].[dbo].[VIX_History]
        WHERE CAST([DATE] AS DATE) = ?
        """
        
        try:
            df = self.execute_query(query, [trading_date])
            
            if df.empty:
                logger.warning(f"No VIX data found for {trading_date}")
                return None
            
            return float(df.iloc[0]['CLOSE'])
            
        except Exception as e:
            logger.error(f"Error getting VIX closing price for {trading_date}: {str(e)}")
            return None
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()


if __name__ == "__main__":
    # Test the database connector
    logging.basicConfig(level=logging.INFO)
    
    with DatabaseConnector() as db:
        # Test getting trading dates
        dates = db.get_trading_dates(start_date='2025-01-01', end_date='2025-01-05')
        print(f"Found {len(dates)} trading dates: {dates}")
        
        # Test getting daily data
        if dates:
            daily_data = db.get_daily_end_of_day_data(dates[0])
            if daily_data:
                print(f"\nDaily data for {dates[0]}:")
                print(f"Pre-bar: {daily_data['pre_bar']['close']}")
                print(f"Number of 10-sec bars: {len(daily_data['bars_10sec'])}")
                print(f"Closing price: {daily_data['closing_price']}") 