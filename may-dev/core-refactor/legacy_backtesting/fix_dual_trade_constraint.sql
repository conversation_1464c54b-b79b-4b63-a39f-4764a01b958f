  -- For SQL Server (based on your schema)
USE [IBDB_DEV];

-- Step 1: Drop the existing unique constraint that prevents multiple trades per date
-- (This finds and drops any unique constraint on run_id + trading_date)
DECLARE @ConstraintName NVARCHAR(255);
SELECT @ConstraintName = kc.name
FROM sys.key_constraints kc
INNER JOIN sys.tables t ON kc.parent_object_id = t.object_id
INNER JOIN sys.index_columns ic ON kc.parent_object_id = ic.object_id AND kc.unique_index_id = ic.index_id
INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
WHERE t.name = 'backtest_trade_results' 
  AND kc.type = 'UQ'
  AND c.name IN ('run_id', 'trading_date')
  AND NOT EXISTS (
      SELECT 1 FROM sys.index_columns ic2
      INNER JOIN sys.columns c2 ON ic2.object_id = c2.object_id AND ic2.column_id = c2.column_id
      WHERE ic2.object_id = kc.parent_object_id 
        AND ic2.index_id = kc.unique_index_id
        AND c2.name = 'trade_sequence'
  );

IF @ConstraintName IS NOT NULL
BEGIN
    DECLARE @SQL NVARCHAR(MAX) = 'ALTER TABLE [IBDB_DEV].[dbo].[backtest_trade_results] DROP CONSTRAINT ' + QUOTENAME(@ConstraintName);
    EXEC sp_executesql @SQL;
    PRINT 'Dropped constraint: ' + @ConstraintName;
END

-- Step 2: Add new constraint that allows multiple trades per date using trade_sequence
ALTER TABLE [IBDB_DEV].[dbo].[backtest_trade_results] 
ADD CONSTRAINT UQ_backtest_trade_results_run_date_sequence 
UNIQUE (run_id, trading_date, trade_sequence);

PRINT 'Multiple trades per date are now allowed using trade_sequence field';