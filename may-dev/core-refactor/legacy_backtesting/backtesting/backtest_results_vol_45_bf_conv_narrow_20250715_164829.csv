trading_date,decision,execution_price,original_strikes,final_strikes,spread_price,strikes_moved,target_price,closing_price,current_balance,max_risk_amount,risk_per_contract,num_contracts,actual_risk_amount,trade_successful,profit_loss,new_balance,reasoning,bias,span1,span2,span3,close_pct_bar1,close_pct_bar2,close_pct_bar3,bar1_ohlc,bar2_ohlc,bar3_ohlc,processing_time,is_dual_trade,dual_trade_type,extra_butterfly_filled,extra_butterfly_case,extra_butterfly_strikes,extra_butterfly_profit_loss,extra_butterfly_trade_successful
2024-01-02,bull,4736.23,"[4735, 4740]","[4735, 4740]",2.15,False,4740,4742.830078125,100000.0,8000.0,215.0,37,7955.0,True,10545.0,110545.0,Bar 1: bull (close at 100.0% of SPAN1),0,5.210000000000036,5.720000000000255,6.680000000000291,100.0,100.0,95.80838323352332,"(4729.83, 4735.04, 4729.83, 4735.04)","(4735.04, 4735.55, 4734.18, 4735.55)","(4735.55, 4736.51, 4734.96, 4736.23)",0.30196,False,,False,,,0.0,False
2024-01-03,narrow_bear,4703.52,"[4700, 4705, 4705, 4710]","[4705, 4700]",2.05,True,4700,4704.81005859375,110545.0,7074.88,204.99999999999997,34,6969.999999999999,False,-6324.199218749999,104220.80078125,Bar 1: bear (close at 0.0% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=4.5 < 6.0),-1,3.630000000000109,4.530000000000655,4.530000000000655,0.0,16.11479028698383,19.205298013259924,"(4707.18, 4707.18, 4703.55, 4703.55)","(4703.55, 4703.73, 4702.65, 4703.38)","(4703.38, 4704.18, 4702.95, 4703.52)",0.366165,False,,False,,,0.0,False
2024-01-04,narrow_bear,4691.6,"[4685, 4690, 4690, 4695]","[4695, 4690]",2.05,True,4690,4688.68017578125,104220.80078125,6670.13125,204.99999999999997,0,0.0,False,0.0,104220.80078125,Bar 1: bear (close at 4.2% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=5.8 < 6.0),-1,3.3299999999999272,4.479999999999563,5.809999999999491,4.204204204214128,6.919642857152464,29.604130808957063,"(4695.69, 4695.69, 4692.36, 4692.5)","(4692.5, 4692.88, 4691.21, 4691.52)","(4691.52, 4691.99, 4689.88, 4691.6)",0.366877,False,,False,,,0.0,False
2024-01-05,narrow_bull,4694.33,"[4690, 4695, 4695, 4700]","[4690, 4695]",2.05,True,4695,4697.240234375,104220.80078125,8337.6640625,204.99999999999997,0,0.0,False,0.0,104220.80078125,Bar 1: bull (close at 100.0% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=3.2 < 6.0),-1,1.5299999999997453,2.9600000000000364,3.25,100.0,73.98648648647206,26.76923076922741,"(4694.31, 4695.28, 4693.75, 4695.28)","(4695.28, 4696.71, 4695.28, 4695.94)","(4695.94, 4696.09, 4693.46, 4694.33)",0.371648,False,,False,,,0.0,False
2024-01-08,bull,4761.22,"[4760, 4765]","[4760, 4765]",2.15,False,4765,4763.5400390625,104220.80078125,8337.6640625,215.0,38,8170.0,True,5282.1484375,109502.94921875,Bar 1: bull (close at 100.0% of SPAN1),0,3.8400000000001455,6.229999999999563,7.890000000000327,100.0,100.0,91.50823827629854,"(4754.0, 4757.84, 4754.0, 4757.84)","(4757.84, 4760.23, 4757.77, 4760.23)","(4760.23, 4761.89, 4759.83, 4761.22)",0.307246,False,,False,,,0.0,False
2024-01-09,bull,4753.93,"[4755, 4760]","[4755, 4760]",1.35,False,4760,4756.5,109502.94921875,8760.2359375,135.0,64,8640.0,True,960.0,110462.94921875,Bar 1: bull (close at 100.0% of SPAN1),0,2.469999999999345,5.4299999999993815,7.099999999999454,100.0,90.79189686924389,98.59154929578222,"(4748.86, 4749.4, 4746.93, 4749.4)","(4749.4, 4752.36, 4749.4, 4751.86)","(4751.86, 4754.03, 4751.46, 4753.93)",0.300383,False,,False,,,0.0,False
2024-01-10,narrow_bull,4771.51,"[4765, 4770, 4770, 4775]","[4770, 4775]",2.05,True,4775,4783.4501953125,110462.94921875,8837.0359375,204.99999999999997,0,0.0,False,0.0,110462.94921875,Bar 1: iron_butterfly (close at 26.9% of SPAN1) → Bar 2 breakout: bull → Narrow conversion: narrow_bull (SPAN3=2.7 < 6.0),1,2.7100000000000364,2.730000000000473,2.730000000000473,26.937269372710816,98.9010989010751,70.69597069596911,"(4772.26, 4772.29, 4769.58, 4770.31)","(4770.31, 4772.31, 4769.98, 4772.28)","(4772.28, 4772.28, 4770.61, 4771.51)",0.366815,False,,False,,,0.0,False
2024-01-11,narrow_bear,4773.32,"[4770, 4775, 4775, 4780]","[4775, 4770]",2.05,True,4770,4780.240234375,110462.94921875,7069.62875,204.99999999999997,34,6969.999999999999,False,-6969.999999999999,103492.94921875,Bar 1: iron_butterfly (close at 15.6% of SPAN1) → BF conversion: BF-bear (1st bar close 15.6% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=5.6 < 6.0),-1,1.0900000000001455,1.2399999999997817,5.5900000000001455,15.59633027523395,12.096774193521178,15.563506261178322,"(4777.54, 4778.04, 4776.95, 4777.12)","(4777.12, 4777.59, 4776.8, 4776.95)","(4776.95, 4776.95, 4772.45, 4773.32)",0.368881,False,,False,,,0.0,False
2024-01-12,narrow_bear,4780.7,"[4775, 4780, 4780, 4785]","[4785, 4780]",2.05,True,4780,4783.830078125,103492.94921875,6623.54875,204.99999999999997,32,6559.999999999999,False,-2816.249999999999,100676.69921875,Bar 1: bear (close at 7.3% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=3.2 < 6.0),0,1.5100000000002183,2.869999999999891,3.25,7.284768211958024,6.9686411149765055,0.0,"(4782.85, 4783.95, 4782.44, 4782.55)","(4782.55, 4782.87, 4781.08, 4781.28)","(4781.28, 4781.91, 4780.7, 4780.7)",0.362289,False,,False,,,0.0,False
2024-01-16,narrow_bull,4757.7,"[4755, 4760, 4760, 4765]","[4755, 4760]",2.05,True,4760,4765.97998046875,100676.69921875,8054.1359375,204.99999999999997,0,0.0,False,0.0,100676.69921875,Bar 1: iron_butterfly (close at 71.2% of SPAN1) → BF conversion: BF-bull (1st bar close 71.2% > 66%) → Narrow conversion: narrow_bull (SPAN3=2.2 < 6.0),-1,2.2200000000002547,2.2200000000002547,2.2200000000002547,71.17117117115973,15.315315315320113,18.468468468459793,"(4757.65, 4759.51, 4757.29, 4758.87)","(4758.87, 4758.91, 4757.39, 4757.63)","(4757.63, 4758.2, 4757.63, 4757.7)",0.366364,False,,False,,,0.0,False
2024-01-17,bull,4738.38,"[4740, 4745]","[4740, 4745]",1.2,False,4745,4739.2099609375,100676.69921875,8054.1359375,120.0,67,8040.0,False,-8040.0,92636.69921875,Bar 1: bull (close at 100.0% of SPAN1),1,3.4700000000002547,6.150000000000546,6.150000000000546,100.0,99.18699186991581,80.97560975609807,"(4733.41, 4736.87, 4733.4, 4736.87)","(4736.87, 4739.55, 4736.87, 4739.5)","(4739.5, 4739.5, 4737.98, 4738.38)",0.299764,False,,False,,,0.0,False
2024-01-18,narrow_bull,4782.35,"[4775, 4780, 4780, 4785]","[4780, 4785]",2.05,True,4785,4780.93994140625,92636.69921875,7410.9359375,204.99999999999997,36,7379.999999999999,False,-3996.210937499999,88640.48828125,Bar 1: bull (close at 92.8% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=3.5 < 6.0),1,1.519999999999527,1.8599999999996726,3.519999999999527,92.76315789475613,91.93548387098588,83.23863636365581,"(4779.42, 4780.94, 4779.42, 4780.83)","(4780.83, 4781.28, 4780.65, 4781.13)","(4781.13, 4782.94, 4781.13, 4782.35)",0.364643,False,,False,,,0.0,False
2024-01-19,narrow_bear,4836.45,"[4830, 4835, 4835, 4840]","[4840, 4835]",2.05,True,4835,4839.81005859375,88640.48828125,5672.99125,204.99999999999997,27,5534.999999999999,False,-5022.158203124999,83618.330078125,Bar 1: iron_butterfly (close at 59.6% of SPAN1) → Bar 3 breakout: bear → Narrow conversion: narrow_bear (SPAN3=1.7 < 6.0),-1,0.9399999999995998,0.9399999999995998,1.6899999999995998,59.574468085077555,23.404255319089238,8.284023668606572,"(4837.06, 4838.0, 4837.06, 4837.62)","(4837.62, 4837.94, 4837.26, 4837.28)","(4837.28, 4837.28, 4836.31, 4836.45)",0.36373,False,,False,,,0.0,False
2024-01-22,narrow_bull,4854.16,"[4850, 4855, 4855, 4860]","[4850, 4855]",2.05,True,4855,4850.43017578125,83618.330078125,6689.466406250001,204.99999999999997,32,6559.999999999999,False,-5183.437499999999,78434.892578125,Bar 1: bull (close at 100.0% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=4.1 < 6.0),1,2.030000000000655,2.1500000000005457,4.100000000000364,100.0,78.13953488371463,99.7560975609703,"(4850.81, 4852.1, 4850.07, 4852.1)","(4852.1, 4852.22, 4851.47, 4851.75)","(4851.75, 4854.17, 4851.75, 4854.16)",0.36806,False,,False,,,0.0,False
2024-01-23,narrow_bull,4864.19,"[4860, 4865, 4865, 4870]","[4860, 4865]",2.05,True,4865,4864.60009765625,78434.892578125,6274.79140625,204.99999999999997,0,0.0,False,0.0,78434.892578125,Bar 1: bull (close at 98.2% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=4.6 < 6.0),0,1.6499999999996362,3.9899999999997817,4.599999999999454,98.18181818183321,100.0,100.0,"(4859.59, 4861.24, 4859.59, 4861.21)","(4861.21, 4863.58, 4861.21, 4863.58)","(4863.58, 4864.19, 4863.05, 4864.19)",0.366063,False,,False,,,0.0,False
2024-01-24,narrow_bear,4868.89,"[4865, 4870, 4870, 4875]","[4870, 4865]",2.05,True,4865,4868.5498046875,78434.892578125,5019.833125,204.99999999999997,24,4919.999999999999,False,-1439.531249999999,76995.361328125,Bar 1: iron_butterfly (close at 38.5% of SPAN1) → Bar 2 breakout: bear → Narrow conversion: narrow_bear (SPAN3=3.2 < 6.0),-1,2.2100000000000364,3.2399999999997817,3.2399999999997817,38.46153846155429,4.629629629618713,25.000000000014033,"(4871.32, 4871.32, 4869.11, 4869.96)","(4869.96, 4870.0, 4868.08, 4868.23)","(4868.23, 4869.16, 4868.14, 4868.89)",0.366405,False,,False,,,0.0,False
2024-01-25,narrow_bull,4889.07,"[4885, 4890, 4890, 4895]","[4885, 4890]",2.05,True,4890,4894.16015625,76995.361328125,6159.62890625,204.99999999999997,0,0.0,False,0.0,76995.361328125,Bar 1: iron_butterfly (close at 73.3% of SPAN1) → Bar 2 breakout: bull → Narrow conversion: narrow_bull (SPAN3=3.9 < 6.0),1,2.730000000000473,3.8700000000008004,3.8700000000008004,73.26007326006057,96.12403100773864,74.41860465115022,"(4886.19, 4888.92, 4886.19, 4888.19)","(4888.19, 4890.06, 4888.19, 4889.91)","(4889.91, 4889.91, 4888.86, 4889.07)",0.362062,False,,False,,,0.0,False
2024-01-26,narrow_bull,4890.32,"[4885, 4890, 4890, 4895]","[4890, 4895]",2.05,True,4895,4890.97021484375,76995.361328125,6159.62890625,204.99999999999997,30,6149.999999999999,False,-3239.355468749999,73756.005859375,Bar 1: iron_butterfly (close at 82.2% of SPAN1) → Bar 2 breakout: bull → Narrow conversion: narrow_bull (SPAN3=1.3 < 6.0),1,0.7300000000004729,1.2800000000006548,1.2800000000006548,82.19178082191439,89.06249999998002,55.46874999997446,"(4889.83, 4890.34, 4889.61, 4890.21)","(4890.21, 4890.89, 4890.19, 4890.75)","(4890.75, 4890.79, 4889.88, 4890.32)",0.367198,False,,False,,,0.0,False
2024-01-29,narrow_bull,4921.33,"[4915, 4920, 4920, 4925]","[4920, 4925]",2.05,True,4925,4927.93017578125,73756.005859375,5900.48046875,204.99999999999997,0,0.0,False,0.0,73756.005859375,Bar 1: bull (close at 96.6% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=2.7 < 6.0),-1,1.7799999999997453,2.480000000000473,2.699999999999818,96.6292134831742,100.0,26.296296296299417,"(4920.97, 4922.4, 4920.62, 4922.34)","(4922.34, 4923.1, 4922.19, 4923.1)","(4923.1, 4923.32, 4921.27, 4921.33)",0.367649,False,,False,,,0.0,False
2024-01-30,narrow_bull,4927.48,"[4920, 4925, 4925, 4930]","[4925, 4930]",2.05,True,4930,4924.97021484375,73756.005859375,5900.48046875,204.99999999999997,28,5739.999999999999,False,-5739.999999999999,68016.005859375,Bar 1: bull (close at 91.5% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=1.7 < 6.0),0,1.4200000000000728,1.5599999999994907,1.6599999999998545,91.549295774656,100.0,45.78313253008285,"(4927.02, 4928.14, 4926.72, 4928.02)","(4928.02, 4928.28, 4927.66, 4928.28)","(4928.28, 4928.38, 4927.4, 4927.48)",0.370298,False,,False,,,0.0,False
2024-01-31,narrow_bear,4848.97,"[4845, 4850, 4850, 4855]","[4850, 4845]",2.05,True,4845,4845.64990234375,68016.005859375,4353.024375,204.99999999999997,21,4304.999999999999,True,4830.205078125001,72846.2109375,Bar 1: iron_butterfly (close at 23.5% of SPAN1) → BF conversion: BF-bear (1st bar close 23.5% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=2.6 < 6.0),-1,2.1700000000000728,2.1700000000000728,2.649999999999636,23.50230414747471,53.45622119814818,20.377358490567463,"(4851.08, 4851.08, 4848.91, 4849.42)","(4849.42, 4850.08, 4848.93, 4850.07)","(4850.07, 4850.7, 4848.43, 4848.97)",0.365902,False,,False,,,0.0,False
2024-02-01,narrow_bear,4898.52,"[4895, 4900, 4900, 4905]","[4900, 4895]",2.05,True,4895,4906.18994140625,72846.2109375,4662.1575,204.99999999999997,22,4509.999999999999,False,-4509.999999999999,68336.2109375,Bar 1: iron_butterfly (close at 18.9% of SPAN1) → BF conversion: BF-bear (1st bar close 18.9% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=1.7 < 6.0),-1,1.639999999999418,1.6899999999995998,1.6899999999995998,18.902439024365897,63.905325443797814,22.4852071006035,"(4898.66, 4899.78, 4898.14, 4898.45)","(4898.45, 4899.83, 4898.4, 4899.22)","(4899.22, 4899.27, 4898.37, 4898.52)",0.366593,False,,False,,,0.0,False
2024-02-02,narrow_bull,4966.35,"[4960, 4965, 4965, 4970]","[4965, 4970]",2.05,True,4970,4958.60986328125,68336.2109375,5466.896875,204.99999999999997,26,5329.999999999999,False,-5329.999999999999,63006.2109375,Bar 1: bear (close at 2.9% of SPAN1) → Bar 2 breakout: bull → Narrow conversion: narrow_bull (SPAN3=2.3 < 6.0),1,1.7399999999997817,2.2899999999999636,2.2899999999999636,2.8735632184016193,100.0,80.34934497817356,"(4965.3, 4966.25, 4964.51, 4964.56)","(4964.56, 4966.8, 4964.56, 4966.8)","(4966.8, 4966.8, 4965.78, 4966.35)",0.362406,False,,False,,,0.0,False
2024-02-05,narrow_bull,4948.85,"[4945, 4950, 4950, 4955]","[4945, 4950]",2.05,True,4950,4942.81005859375,63006.2109375,5040.496875,204.99999999999997,24,4919.999999999999,False,-4919.999999999999,58086.2109375,Bar 1: iron_butterfly (close at 80.7% of SPAN1) → Bar 2 breakout: bull → Narrow conversion: narrow_bull (SPAN3=5.4 < 6.0),1,4.0900000000001455,5.449999999999818,5.449999999999818,80.6845965770187,95.59633027523321,83.85321100918846,"(4944.28, 4948.37, 4944.28, 4947.58)","(4947.58, 4949.73, 4947.58, 4949.49)","(4949.49, 4949.56, 4948.4, 4948.85)",0.366613,False,,False,,,0.0,False
2024-02-06,narrow_bull,4948.69,"[4945, 4950, 4950, 4955]","[4945, 4950]",2.05,True,4950,4954.22998046875,58086.2109375,4646.896875,204.99999999999997,0,0.0,False,0.0,58086.2109375,Bar 1: iron_butterfly (close at 61.8% of SPAN1) → Bar 3 breakout: bull → Narrow conversion: narrow_bull (SPAN3=2.9 < 6.0),-1,1.360000000000582,1.360000000000582,2.880000000000109,61.7647058823372,24.26470588240408,94.09722222221993,"(4945.99, 4947.34, 4945.98, 4946.82)","(4946.82, 4946.82, 4946.14, 4946.31)","(4946.31, 4948.86, 4946.2, 4948.69)",0.370142,False,,False,,,0.0,False
2024-02-07,narrow_bull,4993.1,"[4990, 4995, 4995, 5000]","[4990, 4995]",2.05,True,4995,4995.06005859375,58086.2109375,4646.896875,204.99999999999997,22,4509.999999999999,True,6490.000000000001,64576.2109375,Bar 1: bull (close at 100.0% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=2.4 < 6.0),-1,1.1099999999996726,1.8800000000001091,2.4099999999998545,100.0,26.59574468084952,33.19502074689752,"(4993.46, 4994.4, 4993.29, 4994.4)","(4994.4, 4994.71, 4992.83, 4993.33)","(4993.33, 4993.47, 4992.3, 4993.1)",0.377165,False,,False,,,0.0,False
2024-02-08,narrow_bull,4997.6,"[4995, 5000, 5000, 5005]","[4995, 5000]",2.05,True,5000,4997.91015625,64576.2109375,5166.096875,204.99999999999997,25,5124.999999999999,True,2150.390625000001,66726.6015625,Bar 1: bull (close at 100.0% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=3.4 < 6.0),0,1.7200000000002547,2.2200000000002547,3.3900000000003274,100.0,100.0,93.8053097345128,"(4994.42, 4996.14, 4994.42, 4996.14)","(4996.14, 4996.64, 4995.98, 4996.64)","(4996.64, 4997.81, 4996.64, 4997.6)",0.37457,False,,False,,,0.0,False
2024-02-09,narrow_bull,5022.54,"[5020, 5025, 5025, 5030]","[5020, 5025]",2.05,True,5025,5026.60986328125,66726.6015625,5338.128125,204.99999999999997,0,0.0,False,0.0,66726.6015625,Bar 1: bull (close at 98.2% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=2.1 < 6.0),1,1.1199999999998909,2.1099999999996726,2.1099999999996726,98.21428571424656,98.10426540284503,80.09478672985128,"(5021.26, 5021.97, 5020.85, 5021.95)","(5021.95, 5022.96, 5021.95, 5022.92)","(5022.92, 5022.92, 5022.39, 5022.54)",0.377715,False,,False,,,0.0,False
2024-02-12,narrow_bear,5023.26,"[5020, 5025, 5025, 5030]","[5025, 5020]",2.05,True,5020,5021.83984375,66726.6015625,4270.5025000000005,204.99999999999997,20,4099.999999999999,True,2220.312500000001,68946.9140625,Bar 1: iron_butterfly (close at 48.5% of SPAN1) → BF conversion: BF-bear (1st bar close 48.5% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=2.8 < 6.0),-1,1.9600000000000364,1.9600000000000364,2.800000000000182,48.46938775509186,43.877551020437046,20.357142857163623,"(5023.53, 5025.49, 5023.53, 5024.48)","(5024.48, 5024.9, 5024.23, 5024.39)","(5024.39, 5024.39, 5022.69, 5023.26)",0.370136,False,,False,,,0.0,False
2024-02-13,narrow_bear,4939.11,"[4935, 4940, 4940, 4945]","[4940, 4935]",2.05,True,4935,4953.169921875,68946.9140625,4412.6025,204.99999999999997,21,4304.999999999999,False,-4304.999999999999,64641.9140625,Bar 1: bear (close at 4.7% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=3.8 < 6.0),0,1.4899999999997817,3.569999999999709,3.8299999999999272,4.6979865772234035,9.523809523814377,0.0,"(4942.15, 4942.94, 4941.45, 4941.52)","(4941.52, 4941.72, 4939.37, 4939.71)","(4939.71, 4939.97, 4939.11, 4939.11)",0.367414,False,,False,,,0.0,False
2024-02-14,narrow_bull,4997.04,"[4990, 4995, 4995, 5000]","[4995, 5000]",2.05,True,5000,5000.6201171875,64641.9140625,5171.353125000001,204.99999999999997,25,5124.999999999999,True,7375.000000000001,72016.9140625,Bar 1: bear (close at 0.0% of SPAN1) → Bar 3 breakout: bull → Narrow conversion: narrow_bull (SPAN3=2.0 < 6.0),0,1.5200000000004366,1.5200000000004366,1.980000000000473,0.0,63.15789473682636,95.45454545453919,"(4995.48, 4996.67, 4995.15, 4995.15)","(4995.15, 4996.41, 4995.15, 4996.11)","(4996.11, 4997.13, 4996.11, 4997.04)",0.366533,False,,False,,,0.0,False
2024-02-15,narrow_bull,5026.04,"[5020, 5025, 5025, 5030]","[5025, 5030]",2.05,True,5030,5029.72998046875,72016.9140625,5761.353125000001,204.99999999999997,28,5739.999999999999,True,7503.945312500001,79520.859375,Bar 1: bull (close at 100.0% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=3.8 < 6.0),1,1.199999999999818,2.230000000000473,3.7600000000002183,100.0,100.0,79.78723404254856,"(5023.1, 5024.24, 5023.04, 5024.24)","(5024.24, 5025.27, 5024.22, 5025.27)","(5025.27, 5026.8, 5025.27, 5026.04)",0.373333,False,,False,,,0.0,False
2024-02-16,bull,5004.75,"[5005, 5010]","[5005, 5010]",1.65,False,5010,5005.56982421875,79520.859375,6361.66875,165.0,38,6270.0,False,-4104.66796875,75416.19140625,Bar 1: bull (close at 100.0% of SPAN1),0,2.619999999999891,4.289999999999964,6.720000000000255,100.0,89.27738927738834,58.18452380951944,"(5000.84, 5003.46, 5000.84, 5003.46)","(5003.46, 5005.13, 5003.46, 5004.67)","(5004.67, 5007.56, 5004.49, 5004.75)",0.29832,False,,False,,,0.0,False
2024-02-20,narrow_bull,4971.67,"[4965, 4970, 4970, 4975]","[4970, 4975]",2.05,True,4975,4975.509765625,75416.19140625,6033.2953125,204.99999999999997,29,5944.999999999999,True,8555.0,83971.19140625,Bar 1: iron_butterfly (close at 77.0% of SPAN1) → Bar 3 breakout: bull → Narrow conversion: narrow_bull (SPAN3=2.4 < 6.0),1,1.519999999999527,1.5799999999999272,2.369999999999891,76.97368421055505,36.70886075949076,100.0,"(4969.8, 4970.82, 4969.3, 4970.47)","(4970.47, 4970.88, 4969.32, 4969.88)","(4969.88, 4971.67, 4969.88, 4971.67)",0.367362,False,,False,,,0.0,False
2024-02-21,narrow_bull,4973.54,"[4970, 4975, 4975, 4980]","[4970, 4975]",2.05,True,4975,4981.7998046875,83971.19140625,6717.6953125,204.99999999999997,0,0.0,False,0.0,83971.19140625,Bar 1: iron_butterfly (close at 87.9% of SPAN1) → BF conversion: BF-bull (1st bar close 87.9% > 66%) → Narrow conversion: narrow_bull (SPAN3=3.4 < 6.0),1,3.380000000000109,3.380000000000109,3.380000000000109,87.8698224852118,55.32544378697723,75.14792899407934,"(4971.0, 4974.38, 4971.0, 4973.97)","(4973.97, 4973.97, 4972.63, 4972.87)","(4972.87, 4973.71, 4972.66, 4973.54)",0.382469,False,,False,,,0.0,False
2024-02-22,narrow_bear,5085.74,"[5080, 5085, 5085, 5090]","[5090, 5085]",2.05,True,5085,5087.02978515625,83971.19140625,5374.15625,204.99999999999997,26,5329.999999999999,True,2392.558593750001,86363.75,Bar 1: bull (close at 92.6% of SPAN1) → Bar 3 breakout: bear → Narrow conversion: narrow_bear (SPAN3=3.4 < 6.0),0,2.4200000000000728,2.4200000000000728,3.4399999999996,92.56198347106258,43.38842975207233,11.33720930230998,"(5086.81, 5088.79, 5086.37, 5088.61)","(5088.61, 5088.61, 5087.22, 5087.42)","(5087.42, 5087.42, 5085.35, 5085.74)",0.375476,False,,False,,,0.0,False
2024-02-23,narrow_bull,5095.81,"[5090, 5095, 5095, 5100]","[5095, 5100]",2.05,True,5100,5088.7998046875,86363.75,6909.1,204.99999999999997,33,6764.999999999999,False,-6764.999999999999,79598.75,Bar 1: iron_butterfly (close at 85.7% of SPAN1) → BF conversion: BF-bull (1st bar close 85.7% > 66%) → Narrow conversion: narrow_bull (SPAN3=1.8 < 6.0),-1,1.6099999999996726,1.6099999999996726,1.7799999999997453,85.71428571430992,83.22981366462224,26.404494382040554,"(5095.34, 5096.95, 5095.34, 5096.72)","(5096.72, 5096.94, 5095.84, 5096.68)","(5096.68, 5097.12, 5095.57, 5095.81)",0.370276,False,,False,,,0.0,False
2024-02-26,narrow_bear,5073.31,"[5070, 5075, 5075, 5080]","[5075, 5070]",2.05,True,5070,5069.52978515625,79598.75,5094.32,204.99999999999997,24,4919.999999999999,True,7080.000000000001,86678.75,Bar 1: bear (close at 0.0% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=4.8 < 6.0),0,1.800000000000182,2.910000000000764,4.8400000000001455,0.0,1.0309278350737787,2.6859504132253145,"(5076.94, 5078.02, 5076.22, 5076.22)","(5076.22, 5076.37, 5075.11, 5075.14)","(5075.14, 5075.47, 5073.18, 5073.31)",0.364387,False,,False,,,0.0,False
2024-02-27,narrow_bear,5076.25,"[5070, 5075, 5075, 5080]","[5080, 5075]",2.05,True,5075,5078.18017578125,86678.75,5547.4400000000005,204.99999999999997,27,5534.999999999999,False,-621.4746093749991,86057.275390625,Bar 1: iron_butterfly (close at 54.3% of SPAN1) → BF conversion: BF-bear (1st bar close 54.3% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=3.6 < 6.0),-1,1.5099999999993088,1.5099999999993088,3.5599999999994907,54.30463576159499,6.622516556258283,18.539325842695195,"(5077.67, 5079.15, 5077.64, 5078.46)","(5078.46, 5078.7, 5077.64, 5077.74)","(5077.74, 5077.74, 5075.59, 5076.25)",0.364572,False,,False,,,0.0,False
2024-02-28,narrow_bear,5066.73,"[5060, 5065, 5065, 5070]","[5070, 5065]",2.05,True,5065,5069.759765625,86057.275390625,5507.665625000001,204.99999999999997,26,5329.999999999999,False,-4705.390624999999,81351.884765625,Bar 1: iron_butterfly (close at 39.4% of SPAN1) → Bar 2 breakout: bear → Narrow conversion: narrow_bear (SPAN3=1.4 < 6.0),0,0.7100000000000364,0.9600000000000364,1.3500000000003638,39.43661971827197,0.0,36.29629629627035,"(5067.58, 5067.59, 5066.88, 5067.16)","(5067.16, 5067.26, 5066.63, 5066.63)","(5066.63, 5066.99, 5066.24, 5066.73)",0.367435,False,,False,,,0.0,False
2024-02-29,narrow_bull,5098.1,"[5095, 5100, 5100, 5105]","[5095, 5100]",2.05,True,5100,5096.27001953125,81351.884765625,6508.15078125,204.99999999999997,31,6354.999999999999,False,-2417.939453124999,78933.9453125,Bar 1: iron_butterfly (close at 82.4% of SPAN1) → BF conversion: BF-bull (1st bar close 82.4% > 66%) → Narrow conversion: narrow_bull (SPAN3=3.1 < 6.0),1,2.9600000000000364,3.1099999999996726,3.1099999999996726,82.4324324324179,63.66559485529814,79.42122186496832,"(5095.63, 5098.59, 5095.63, 5098.07)","(5098.07, 5098.74, 5096.93, 5097.61)","(5097.61, 5098.1, 5097.18, 5098.1)",0.366197,False,,False,,,0.0,False
2024-03-01,narrow_bear,5136.13,"[5130, 5135, 5135, 5140]","[5140, 5135]",2.05,True,5135,5137.080078125,78933.9453125,5051.7725,204.99999999999997,24,4919.999999999999,True,2087.812500000001,81021.7578125,Bar 1: bear (close at 9.7% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=2.1 < 6.0),0,2.0600000000004,2.0600000000004,2.0600000000004,9.708737864066954,50.485436893192315,42.718446601938744,"(5137.31, 5137.31, 5135.25, 5135.45)","(5135.45, 5136.6, 5135.43, 5136.29)","(5136.29, 5136.32, 5135.78, 5136.13)",0.362477,False,,False,,,0.0,False
2024-03-04,narrow_bear,5131.99,"[5125, 5130, 5130, 5135]","[5135, 5130]",2.05,True,5130,5130.9501953125,81021.7578125,5185.3925,204.99999999999997,0,0.0,False,0.0,81021.7578125,Bar 1: iron_butterfly (close at 20.2% of SPAN1) → BF conversion: BF-bear (1st bar close 20.2% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=2.8 < 6.0),-1,2.030000000000655,2.6400000000003274,2.8100000000004,20.197044334961685,15.909090909091692,44.1281138789895,"(5131.53, 5133.56, 5131.53, 5131.94)","(5131.94, 5132.17, 5130.92, 5131.34)","(5131.34, 5132.4, 5130.75, 5131.99)",0.364597,False,,False,,,0.0,False
2024-03-05,narrow_bear,5071.43,"[5065, 5070, 5070, 5075]","[5075, 5070]",2.05,True,5070,5078.64990234375,81021.7578125,5185.3925,204.99999999999997,25,5124.999999999999,False,-5124.999999999999,75896.7578125,Bar 1: bear (close at 2.9% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=4.0 < 6.0),0,2.380000000000109,3.6900000000005093,4.029999999999745,2.941176470575872,2.168021680214531,66.00496277915688,"(5070.81, 5072.8, 5070.42, 5070.49)","(5070.49, 5070.49, 5069.11, 5069.19)","(5069.19, 5071.43, 5068.77, 5071.43)",0.369738,False,,False,,,0.0,False
2024-03-06,BF-bear,5105.4,"[5105, 5100]","[5105, 5100]",1.6,False,5100,5104.759765625,75896.7578125,4857.3925,160.0,30,4800.0,False,-4079.296875,71817.4609375,Bar 1: iron_butterfly (close at 14.1% of SPAN1) → BF conversion: BF-bear (1st bar close 14.1% ≤ 66%),-1,6.6599999999998545,7.199999999999818,7.199999999999818,14.11411411412207,31.1111111111215,60.694444444444464,"(5108.23, 5108.23, 5101.57, 5102.51)","(5102.51, 5103.54, 5101.03, 5103.27)","(5103.27, 5105.8, 5103.16, 5105.4)",0.305972,False,,False,,,0.0,False
2024-03-07,narrow_bear,5154.42,"[5150, 5155, 5155, 5160]","[5155, 5150]",2.05,True,5150,5157.35986328125,71817.4609375,4596.3175,204.99999999999997,22,4509.999999999999,False,-4509.999999999999,67307.4609375,Bar 1: bear (close at 8.3% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=2.9 < 6.0),-1,0.8400000000001455,2.4600000000000364,2.850000000000364,8.333333333297242,19.512195121933186,6.3157894736936155,"(5156.26, 5157.09, 5156.25, 5156.32)","(5156.32, 5156.47, 5154.63, 5155.11)","(5155.11, 5155.4, 5154.24, 5154.42)",0.363236,False,,False,,,0.0,False
2024-03-08,narrow_bear,5127.6,"[5125, 5130, 5130, 5135]","[5130, 5125]",2.05,True,5125,5123.68994140625,67307.4609375,4307.6775,204.99999999999997,21,4304.999999999999,True,6195.000000000001,73502.4609375,Bar 1: bear (close at 0.0% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=4.5 < 6.0),-1,3.6599999999998545,4.520000000000437,4.520000000000437,0.0,13.9380530973462,28.31858407080821,"(5128.86, 5130.84, 5127.18, 5127.18)","(5127.18, 5127.4, 5126.32, 5126.95)","(5126.95, 5128.54, 5126.95, 5127.6)",0.36832,False,,False,,,0.0,False
2024-03-11,narrow_bull,5116.34,"[5110, 5115, 5115, 5120]","[5115, 5120]",2.05,True,5120,5117.93994140625,73502.4609375,5880.196875000001,204.99999999999997,28,5739.999999999999,True,2491.835937500001,75994.296875,Bar 1: bull (close at 100.0% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=2.9 < 6.0),1,1.3699999999998909,2.2200000000002547,2.9000000000005457,100.0,53.15315315316017,85.51724137931056,"(5114.35, 5115.23, 5113.86, 5115.23)","(5115.23, 5116.08, 5115.04, 5115.04)","(5115.04, 5116.76, 5115.01, 5116.34)",0.370908,False,,False,,,0.0,False
2024-03-12,narrow_bear,5174.3,"[5170, 5175, 5175, 5180]","[5175, 5170]",2.05,True,5170,5175.27001953125,75994.296875,4863.635,204.99999999999997,23,4714.999999999999,False,-4714.999999999999,71279.296875,Bar 1: iron_butterfly (close at 34.4% of SPAN1) → BF conversion: BF-bear (1st bar close 34.4% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=2.2 < 6.0),1,2.209999999999127,2.209999999999127,2.209999999999127,34.38914027147552,60.63348416288531,80.54298642535966,"(5174.73, 5174.73, 5172.52, 5173.28)","(5173.28, 5174.23, 5172.75, 5173.86)","(5173.86, 5174.42, 5173.5, 5174.3)",0.36129,False,,False,,,0.0,False
2024-03-13,narrow_bear,5163.79,"[5160, 5165, 5165, 5170]","[5165, 5160]",2.05,True,5160,5165.31005859375,71279.296875,4561.875,204.99999999999997,22,4509.999999999999,False,-4509.999999999999,66769.296875,Bar 1: iron_butterfly (close at 20.3% of SPAN1) → Bar 2 breakout: bear → Narrow conversion: narrow_bear (SPAN3=5.3 < 6.0),-1,2.3200000000006185,2.7600000000002183,5.3100000000004,20.25862068966075,9.782608695634265,12.617702448211343,"(5168.43, 5168.43, 5166.11, 5166.58)","(5166.58, 5166.73, 5165.67, 5165.94)","(5165.94, 5165.94, 5163.12, 5163.79)",0.361367,False,,False,,,0.0,False
2024-03-14,narrow_bull,5142.56,"[5140, 5145, 5145, 5150]","[5140, 5145]",2.05,True,5145,5150.47998046875,66769.296875,5341.54375,204.99999999999997,0,0.0,False,0.0,66769.296875,Bar 1: iron_butterfly (close at 34.6% of SPAN1) → Bar 3 breakout: bull → Narrow conversion: narrow_bull (SPAN3=5.2 < 6.0),0,3.7299999999995634,3.7299999999995634,5.220000000000255,34.584450402147844,32.97587131366508,94.44444444444541,"(5138.6, 5141.36, 5137.63, 5138.92)","(5138.92, 5139.72, 5137.9, 5138.86)","(5138.86, 5142.85, 5138.86, 5142.56)",0.365658,False,,False,,,0.0,False
2024-03-15,narrow_bear,5116.27,"[5110, 5115, 5115, 5120]","[5120, 5115]",2.05,True,5115,5117.08984375,66769.296875,4273.235,204.99999999999997,20,4099.999999999999,True,1720.312500000001,68489.609375,Bar 1: iron_butterfly (close at 17.3% of SPAN1) → BF conversion: BF-bear (1st bar close 17.3% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=4.2 < 6.0),-1,3.7100000000000364,3.7100000000000364,4.1599999999998545,17.250673854456096,9.164420485179035,27.644230769244853,"(5119.28, 5119.28, 5115.57, 5116.21)","(5116.21, 5117.05, 5115.71, 5115.91)","(5115.91, 5116.98, 5115.12, 5116.27)",0.368064,False,,False,,,0.0,False
2024-03-18,narrow_bear,5152.7,"[5150, 5155, 5155, 5160]","[5155, 5150]",2.05,True,5150,5149.419921875,68489.609375,4383.335,204.99999999999997,0,0.0,False,0.0,68489.609375,Bar 1: bear (close at 11.3% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=5.1 < 6.0),0,5.0600000000004,5.0600000000004,5.0600000000004,11.264822134380708,32.806324110666466,10.671936758891718,"(5157.18, 5157.22, 5152.16, 5152.73)","(5152.73, 5154.03, 5152.73, 5153.82)","(5153.82, 5154.11, 5152.32, 5152.7)",0.366497,False,,False,,,0.0,False
2024-03-19,narrow_bear,5175.97,"[5170, 5175, 5175, 5180]","[5180, 5175]",2.05,True,5175,5178.509765625,68489.609375,4383.335,204.99999999999997,21,4304.999999999999,False,-1175.507812499999,67314.1015625,Bar 1: bear (close at 5.4% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=2.8 < 6.0),1,0.9200000000000728,2.1500000000005457,2.75,5.4347826086161355,98.13953488372309,82.54545454547042,"(5175.85, 5175.85, 5174.93, 5174.98)","(5174.98, 5175.84, 5173.7, 5175.81)","(5175.81, 5176.45, 5174.69, 5175.97)",0.366876,False,,False,,,0.0,False
2024-03-20,narrow_bull,5216.38,"[5210, 5215, 5215, 5220]","[5215, 5220]",2.05,True,5220,5224.6201171875,67314.1015625,5385.128125,204.99999999999997,0,0.0,False,0.0,67314.1015625,Bar 1: bear (close at 1.6% of SPAN1) → Bar 3 breakout: bull → Narrow conversion: narrow_bull (SPAN3=4.8 < 6.0),1,1.2899999999999636,1.4200000000000728,4.779999999999745,1.5503875968626066,82.394366197184,100.0,"(5212.45, 5212.97, 5211.68, 5211.7)","(5211.7, 5213.02, 5211.6, 5212.77)","(5212.77, 5216.38, 5212.77, 5216.38)",0.371252,False,,False,,,0.0,False
2024-03-21,narrow_bear,5246.5,"[5240, 5245, 5245, 5250]","[5250, 5245]",2.05,True,5245,5241.52978515625,67314.1015625,4308.1025,204.99999999999997,0,0.0,False,0.0,67314.1015625,Bar 1: iron_butterfly (close at 61.9% of SPAN1) → BF conversion: BF-bear (1st bar close 61.9% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=2.4 < 6.0),1,1.550000000000182,1.550000000000182,2.430000000000291,61.93548387096281,83.22580645160079,79.42386831275967,"(5244.57, 5246.12, 5244.57, 5245.53)","(5245.53, 5245.94, 5244.6, 5245.86)","(5245.86, 5247.0, 5245.86, 5246.5)",0.365319,False,,False,,,0.0,False
2024-03-22,narrow_bull,5241.48,"[5235, 5240, 5240, 5245]","[5240, 5245]",2.05,True,5245,5234.18017578125,67314.1015625,5385.128125,204.99999999999997,26,5329.999999999999,False,-5329.999999999999,61984.1015625,Bar 1: iron_butterfly (close at 77.9% of SPAN1) → BF conversion: BF-bull (1st bar close 77.9% > 66%) → Narrow conversion: narrow_bull (SPAN3=2.7 < 6.0),-1,2.6700000000000728,2.6700000000000728,2.6700000000000728,77.90262172284159,24.719101123589382,63.29588014979603,"(5239.83, 5242.46, 5239.79, 5241.87)","(5241.87, 5241.87, 5239.96, 5240.45)","(5240.45, 5241.74, 5240.45, 5241.48)",0.364351,False,,False,,,0.0,False
2024-03-25,narrow_bear,5220.91,"[5215, 5220, 5220, 5225]","[5225, 5220]",2.05,True,5220,5218.18994140625,61984.1015625,3966.9825,204.99999999999997,0,0.0,False,0.0,61984.1015625,Bar 1: iron_butterfly (close at 13.1% of SPAN1) → BF conversion: BF-bear (1st bar close 13.1% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=2.8 < 6.0),-1,1.980000000000473,2.0,2.7700000000004366,13.131313131321019,22.499999999990905,13.357400722015617,"(5223.24, 5223.31, 5221.33, 5221.59)","(5221.59, 5222.02, 5221.31, 5221.76)","(5221.76, 5221.76, 5220.54, 5220.91)",0.363683,False,,False,,,0.0,False
2024-03-26,narrow_bull,5212.29,"[5205, 5210, 5210, 5215]","[5210, 5215]",2.05,True,5215,5203.580078125,61984.1015625,4958.728125000001,204.99999999999997,24,4919.999999999999,False,-4919.999999999999,57064.1015625,Bar 1: iron_butterfly (close at 70.0% of SPAN1) → Bar 2 breakout: bull → Narrow conversion: narrow_bull (SPAN3=3.4 < 6.0),0,2.0299999999997453,2.7600000000002183,3.3599999999996726,69.95073891626852,95.28985507246018,95.53571428572468,"(5211.05, 5211.11, 5209.08, 5210.5)","(5210.5, 5211.84, 5210.5, 5211.71)","(5211.71, 5212.44, 5211.43, 5212.29)",0.368717,False,,False,,,0.0,False
2024-03-27,narrow_bull,5246.18,"[5240, 5245, 5245, 5250]","[5245, 5250]",2.05,True,5250,5248.490234375,57064.1015625,4565.128125,204.99999999999997,22,4509.999999999999,True,3168.515625000001,60232.6171875,Bar 1: bull (close at 100.0% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=6.0 < 6.0),0,2.949999999999818,4.869999999999891,5.960000000000036,100.0,100.0,100.0,"(5240.65, 5243.17, 5240.22, 5243.17)","(5243.17, 5245.09, 5243.16, 5245.09)","(5245.09, 5246.18, 5244.56, 5246.18)",0.37015,False,,False,,,0.0,False
2024-03-28,narrow_bull,5258.76,"[5255, 5260, 5260, 5265]","[5255, 5260]",2.05,True,5260,5254.35009765625,60232.6171875,4818.609375,204.99999999999997,23,4714.999999999999,False,-4714.999999999999,55517.6171875,Bar 1: iron_butterfly (close at 79.1% of SPAN1) → Bar 3 breakout: bull → Narrow conversion: narrow_bull (SPAN3=4.0 < 6.0),-1,1.5799999999999272,3.380000000000109,4.019999999999527,79.11392405063656,13.609467455621937,93.28358208956321,"(5257.67, 5258.39, 5256.81, 5258.06)","(5258.06, 5258.06, 5255.01, 5255.47)","(5255.47, 5259.03, 5255.47, 5258.76)",0.365849,False,,False,,,0.0,False
2024-04-01,narrow_bull,5240.01,"[5235, 5240, 5240, 5245]","[5240, 5245]",2.05,True,5245,5243.77001953125,55517.6171875,4441.409375,204.99999999999997,21,4304.999999999999,True,3612.041015625001,59129.658203125,Bar 1: iron_butterfly (close at 14.0% of SPAN1) → Bar 2 breakout: bull → Narrow conversion: narrow_bull (SPAN3=3.6 < 6.0),1,1.1400000000003274,1.7399999999997817,3.600000000000364,14.03508771928145,100.0,77.77777777777497,"(5238.03, 5238.35, 5237.21, 5237.37)","(5237.37, 5238.95, 5237.37, 5238.95)","(5238.95, 5240.81, 5238.92, 5240.01)",0.367292,False,,False,,,0.0,False
2024-04-02,narrow_bear,5206.71,"[5200, 5205, 5205, 5210]","[5210, 5205]",2.05,True,5205,5205.81005859375,59129.658203125,3784.2981250000003,204.99999999999997,0,0.0,False,0.0,59129.658203125,Bar 1: bear (close at 8.0% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=3.3 < 6.0),0,2.4899999999997817,3.1500000000005457,3.2899999999999636,8.032128514049623,0.0,50.45592705166787,"(5208.21, 5208.34, 5205.85, 5206.05)","(5206.05, 5206.54, 5205.19, 5205.19)","(5205.19, 5206.72, 5205.05, 5206.71)",0.371332,False,,False,,,0.0,False
2024-04-03,narrow_bear,5213.55,"[5210, 5215, 5215, 5220]","[5215, 5210]",2.05,True,5210,5211.490234375,59129.658203125,3784.2981250000003,204.99999999999997,18,3689.9999999999995,True,2627.5781250000005,61757.236328125,Bar 1: bear (close at 4.5% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=2.3 < 6.0),0,1.980000000000473,2.130000000000109,2.3100000000004,4.545454545460809,37.08920187793067,0.8658008658196144,"(5215.84, 5215.84, 5213.86, 5213.95)","(5213.95, 5214.55, 5213.71, 5214.5)","(5214.5, 5215.09, 5213.53, 5213.55)",0.367973,False,,False,,,0.0,False
2024-04-04,bear,5155.06,"[5155, 5150]","[5155, 5150]",1.7,False,5150,5147.2099609375,61757.236328125,3952.463125,170.0,23,3910.0,True,7590.0,69347.236328125,Bar 1: iron_butterfly (close at 22.3% of SPAN1) → Bar 2 breakout: bear,-1,2.9200000000000728,4.619999999999891,6.479999999999563,22.260273972620872,0.0,18.209876543215593,"(5157.59, 5160.36, 5157.44, 5158.09)","(5158.09, 5158.16, 5155.74, 5155.74)","(5155.74, 5155.74, 5153.88, 5155.06)",0.303234,False,,False,,,0.0,False
2024-04-05,narrow_bull,5205.24,"[5200, 5205, 5205, 5210]","[5205, 5210]",2.05,True,5210,5204.33984375,69347.236328125,5547.778906250001,204.99999999999997,27,5534.999999999999,False,-5534.999999999999,63812.236328125,Bar 1: bull (close at 100.0% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=3.3 < 6.0),1,1.7799999999997453,3.3099999999994907,3.3099999999994907,100.0,79.75830815709438,80.06042296072641,"(5203.47, 5204.37, 5202.59, 5204.37)","(5204.37, 5205.9, 5204.37, 5205.23)","(5205.23, 5205.36, 5204.15, 5205.24)",0.375265,False,,False,,,0.0,False
2024-04-08,narrow_bear,5205.65,"[5200, 5205, 5205, 5210]","[5210, 5205]",2.05,True,5205,5202.39013671875,63812.236328125,4083.983125,204.99999999999997,0,0.0,False,0.0,63812.236328125,Bar 1: iron_butterfly (close at 17.9% of SPAN1) → BF conversion: BF-bear (1st bar close 17.9% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=1.2 < 6.0),-1,0.9499999999998181,1.1899999999995998,1.1899999999995998,17.894736842116348,22.68907563021999,1.6806722688683857,"(5206.82, 5206.82, 5205.87, 5206.04)","(5206.04, 5206.07, 5205.63, 5205.9)","(5205.9, 5206.15, 5205.65, 5205.65)",0.370651,False,,False,,,0.0,False
2024-04-09,narrow_bull,5195.12,"[5190, 5195, 5195, 5200]","[5195, 5200]",2.05,True,5200,5209.91015625,63812.236328125,5104.97890625,204.99999999999997,24,4919.999999999999,True,7080.000000000001,70892.236328125,Bar 1: iron_butterfly (close at 25.4% of SPAN1) → Bar 2 breakout: bull → Narrow conversion: narrow_bull (SPAN3=1.9 < 6.0),-1,1.180000000000291,1.8500000000003638,1.8500000000003638,25.42372881356847,100.0,24.32432432430971,"(5196.29, 5196.29, 5195.11, 5195.41)","(5195.41, 5196.52, 5194.67, 5196.52)","(5196.52, 5196.52, 5195.1, 5195.12)",0.375413,False,,False,,,0.0,False
2024-04-10,narrow_bear,5163.45,"[5160, 5165, 5165, 5170]","[5165, 5160]",2.05,True,5160,5160.64013671875,70892.236328125,4537.103125000001,204.99999999999997,22,4509.999999999999,True,5081.699218750001,75973.935546875,Bar 1: iron_butterfly (close at 35.3% of SPAN1) → BF conversion: BF-bear (1st bar close 35.3% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=4.0 < 6.0),1,1.019999999999527,3.9600000000000364,3.9600000000000364,35.29411764704309,22.979797979794096,84.09090909090648,"(5163.96, 5164.08, 5163.06, 5163.42)","(5163.42, 5163.42, 5160.12, 5161.03)","(5161.03, 5163.54, 5160.89, 5163.45)",0.372648,False,,False,,,0.0,False
2024-04-11,narrow_bear,5203.5,"[5200, 5205, 5205, 5210]","[5205, 5200]",2.05,True,5200,5199.06005859375,75973.935546875,4862.331875,204.99999999999997,23,4714.999999999999,True,6785.000000000001,82758.935546875,Bar 1: bear (close at 2.3% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=4.6 < 6.0),0,2.149999999999636,4.119999999999891,4.569999999999709,2.325581395357691,10.194174757283589,41.137855579873715,"(5206.18, 5206.19, 5204.04, 5204.09)","(5204.09, 5204.32, 5202.07, 5202.49)","(5202.49, 5204.58, 5201.62, 5203.5)",0.370575,False,,False,,,0.0,False
2024-04-12,bear,5122.48,"[5120, 5115]","[5120, 5115]",0.9,False,5115,5123.41015625,82758.935546875,5296.571875000001,90.0,58,5220.0,False,-5220.0,77538.935546875,Bar 1: iron_butterfly (close at 36.3% of SPAN1) → Bar 2 breakout: bear,0,1.6799999999993815,5.529999999999745,5.529999999999745,36.309523809517685,1.4466546112103242,61.482820976488114,"(5123.1, 5124.61, 5122.93, 5123.54)","(5123.54, 5123.61, 5119.08, 5119.16)","(5119.16, 5122.48, 5119.16, 5122.48)",0.305338,False,,False,,,0.0,False
2024-04-15,narrow_bear,5065.78,"[5060, 5065, 5065, 5070]","[5070, 5065]",2.05,True,5065,5061.81982421875,77538.935546875,4962.491875,204.99999999999997,0,0.0,False,0.0,77538.935546875,Bar 1: iron_butterfly (close at 47.8% of SPAN1) → BF conversion: BF-bear (1st bar close 47.8% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=3.3 < 4.0),-1,2.4700000000002547,3.300000000000182,3.300000000000182,47.77327935223358,55.454545454540195,28.484848484834792,"(5068.14, 5068.14, 5065.67, 5066.85)","(5066.85, 5066.92, 5064.84, 5066.67)","(5066.67, 5067.31, 5065.74, 5065.78)",0.367101,False,,False,,,0.0,False
2024-04-16,narrow_bull,5064.52,"[5060, 5065, 5065, 5070]","[5060, 5065]",2.05,True,5065,5051.41015625,77538.935546875,6203.114843750001,204.99999999999997,30,6149.999999999999,False,-6149.999999999999,71388.935546875,Bar 1: iron_butterfly (close at 64.9% of SPAN1) → Bar 2 breakout: bull → Narrow conversion: narrow_bull (SPAN3=2.2 < 4.0),0,1.1400000000003274,1.550000000000182,2.180000000000291,64.9122807017166,89.6774193548493,88.99082568808487,"(5063.01, 5063.72, 5062.58, 5063.32)","(5063.32, 5064.13, 5062.58, 5063.97)","(5063.97, 5064.76, 5063.4, 5064.52)",0.366208,False,,False,,,0.0,False
2024-04-17,bear,5026.75,"[5025, 5020]","[5025, 5020]",1.15,False,5020,5022.2099609375,71388.935546875,4568.891875,114.99999999999999,39,4484.999999999999,True,6396.152343750001,77785.087890625,Bar 1: bull (close at 100.0% of SPAN1) → Bar 2 breakout: bear,-1,1.2200000000002547,2.9400000000005093,6.449999999999818,100.0,1.0204081632874027,12.093023255810346,"(5031.9, 5032.42, 5031.2, 5032.42)","(5032.42, 5032.42, 5029.48, 5029.51)","(5029.51, 5029.51, 5025.97, 5026.75)",0.300644,False,,False,,,0.0,False
2024-04-18,bull,5022.71,"[5025, 5030]","[5025, 5030]",0.95,False,5030,5011.1201171875,77785.087890625,6222.80703125,95.0,65,6175.0,False,-6175.0,71610.087890625,Bar 1: bull (close at 100.0% of SPAN1),1,2.6400000000003274,3.6900000000005093,5.220000000000255,100.0,79.13279132790433,97.50957854405934,"(5017.71, 5020.26, 5017.62, 5020.26)","(5020.26, 5021.31, 5019.82, 5020.54)","(5020.54, 5022.84, 5020.44, 5022.71)",0.302497,False,,False,,,0.0,False
2024-04-19,bull,4962.42,"[4960, 4965]","[4960, 4965]",2.55,False,4965,4967.22998046875,71610.087890625,5728.80703125,254.99999999999997,22,5609.999999999999,True,5390.000000000001,77000.087890625,Bar 1: iron_butterfly (close at 85.2% of SPAN1) → Bar 2 breakout: bull,1,2.300000000000182,3.5700000000006185,5.0700000000006185,85.21739130434267,97.19887955181102,86.5877712031517,"(4960.33, 4960.33, 4958.03, 4959.99)","(4959.99, 4961.6, 4958.59, 4961.5)","(4961.5, 4963.1, 4961.16, 4962.42)",0.304713,False,,False,,,0.0,False
2024-04-22,bear,5014.06,"[5015, 5010]","[5015, 5010]",2.05,False,5010,5010.60009765625,77000.087890625,4928.005625,204.99999999999997,24,4919.999999999999,True,5639.765625000001,82639.853515625,Bar 1: bear (close at 0.0% of SPAN1),0,4.289999999999964,4.949999999999818,4.949999999999818,0.0,2.2222222222156893,41.61616161617123,"(5016.94, 5016.95, 5012.66, 5012.66)","(5012.66, 5013.99, 5012.0, 5012.11)","(5012.11, 5014.39, 5012.11, 5014.06)",0.307538,False,,False,,,0.0,False
2024-04-23,narrow_bear,5070.82,"[5065, 5070, 5070, 5075]","[5075, 5070]",2.05,True,5070,5070.5498046875,82639.853515625,5288.950625,204.99999999999997,0,0.0,False,0.0,82639.853515625,Bar 1: iron_butterfly (close at 16.3% of SPAN1) → BF conversion: BF-bear (1st bar close 16.3% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=2.6 < 6.0),-1,2.569999999999709,2.569999999999709,2.569999999999709,16.34241245133116,52.529182879362146,58.75486381320933,"(5071.77, 5071.88, 5069.31, 5069.73)","(5069.73, 5070.9, 5069.73, 5070.66)","(5070.66, 5070.89, 5070.2, 5070.82)",0.37005,False,,False,,,0.0,False
2024-04-24,narrow_bull,5073.23,"[5070, 5075, 5075, 5080]","[5070, 5075]",2.05,True,5075,5071.6298828125,82639.853515625,6611.18828125,204.99999999999997,0,0.0,False,0.0,82639.853515625,Bar 1: iron_butterfly (close at 53.0% of SPAN1) → Bar 2 breakout: bull → Narrow conversion: narrow_bull (SPAN3=1.8 < 6.0),1,1.5100000000002183,1.800000000000182,1.800000000000182,52.98013245033552,88.88888888890011,75.55555555552974,"(5073.38, 5073.38, 5071.87, 5072.67)","(5072.67, 5073.67, 5072.67, 5073.47)","(5073.47, 5073.47, 5072.52, 5073.23)",0.372673,False,,False,,,0.0,False
2024-04-25,narrow_bull,5046.84,"[5040, 5045, 5045, 5050]","[5045, 5050]",2.05,True,5050,5048.419921875,82639.853515625,6611.18828125,204.99999999999997,32,6559.999999999999,True,4383.750000000001,87023.603515625,Bar 1: iron_butterfly (close at 84.3% of SPAN1) → Bar 3 breakout: bull → Narrow conversion: narrow_bull (SPAN3=3.9 < 6.0),1,2.540000000000873,2.540000000000873,3.910000000000764,84.25196850392093,94.881889763777,98.72122762147897,"(5042.98, 5045.52, 5042.98, 5045.12)","(5045.12, 5045.5, 5043.76, 5045.39)","(5045.39, 5046.89, 5045.3, 5046.84)",0.381532,False,,False,,,0.0,False
2024-04-26,narrow_bull,5104.73,"[5100, 5105, 5105, 5110]","[5100, 5105]",2.05,True,5105,5099.9599609375,87023.603515625,6961.888281250001,204.99999999999997,33,6764.999999999999,False,-6764.999999999999,80258.603515625,Bar 1: iron_butterfly (close at 69.0% of SPAN1) → BF conversion: BF-bull (1st bar close 69.0% > 66%) → Narrow conversion: narrow_bull (SPAN3=1.2 < 6.0),1,0.8699999999998909,0.8699999999998909,1.2100000000000364,68.96551724142978,80.45977011493171,61.15702479336855,"(5104.66, 5104.86, 5103.99, 5104.59)","(5104.59, 5104.84, 5104.31, 5104.69)","(5104.69, 5105.2, 5104.49, 5104.73)",0.372809,False,,False,,,0.0,False
2024-04-29,bull,5108.35,"[5110, 5115]","[5110, 5115]",1.2,False,5115,5116.169921875,80258.603515625,6420.68828125,120.0,53,6360.0,True,20140.0,100398.603515625,Bar 1: iron_butterfly (close at 76.4% of SPAN1) → Bar 2 breakout: bull,1,2.0799999999999272,5.029999999999745,6.119999999999891,76.44230769231736,100.0,84.64052287582325,"(5103.17, 5105.25, 5103.17, 5104.76)","(5104.76, 5108.2, 5104.76, 5108.2)","(5108.2, 5109.29, 5108.2, 5108.35)",0.304264,False,,False,,,0.0,False
2024-04-30,bear,5059.67,"[5060, 5055]","[5060, 5055]",1.85,False,5055,5035.68994140625,100398.603515625,6425.510625,185.0,34,6290.0,True,10710.0,111108.603515625,Bar 1: bear (close at 5.0% of SPAN1),0,5.1599999999998545,5.619999999999891,6.3799999999992,5.038759689926852,3.024911032029823,6.269592476484112,"(5065.65, 5065.65, 5060.49, 5060.75)","(5060.75, 5061.04, 5060.03, 5060.2)","(5060.2, 5061.0, 5059.27, 5059.67)",0.297821,False,,False,,,0.0,False
2024-05-01,bear,5037.54,"[5040, 5035]","[5040, 5035]",2.55,False,5035,5018.39013671875,111108.603515625,7110.950625,254.99999999999997,27,6884.999999999999,True,6615.000000000001,117723.603515625,Bar 1: bear (close at 2.0% of SPAN1),0,5.600000000000364,8.19000000000051,11.480000000000473,1.9642857142797399,10.378510378514175,0.0,"(5048.7, 5049.02, 5043.42, 5043.53)","(5043.53, 5043.53, 5040.83, 5041.68)","(5041.68, 5043.39, 5037.54, 5037.54)",0.300579,False,,False,,,0.0,False
2024-05-02,narrow_bear,5056.7,"[5050, 5055, 5055, 5060]","[5060, 5055]",2.05,True,5055,5064.2001953125,117723.603515625,7534.310625,204.99999999999997,36,7379.999999999999,False,-7379.999999999999,110343.603515625,Bar 1: iron_butterfly (close at 13.7% of SPAN1) → BF conversion: BF-bear (1st bar close 13.7% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=3.1 < 6.0),-1,3.0600000000004,3.0600000000004,3.0600000000004,13.725490196079015,37.58169934641815,23.202614379083123,"(5058.61, 5059.05, 5055.99, 5056.41)","(5056.41, 5059.04, 5056.41, 5057.14)","(5057.14, 5057.33, 5056.25, 5056.7)",0.61019,False,,False,,,0.0,False
2024-05-03,narrow_bull,5129.0,"[5125, 5130, 5130, 5135]","[5125, 5130]",2.05,True,5130,5127.7900390625,110343.603515625,8827.48828125,204.99999999999997,0,0.0,False,0.0,110343.603515625,Bar 1: bull (close at 100.0% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=2.8 < 6.0),1,1.2400000000006912,2.110000000000582,2.8400000000001455,100.0,95.73459715639238,86.97183098592001,"(5126.64, 5127.77, 5126.53, 5127.77)","(5127.77, 5128.64, 5127.71, 5128.55)","(5128.55, 5129.37, 5128.53, 5129.0)",0.377386,False,,False,,,0.0,False
2024-05-06,narrow_bear,5169.69,"[5165, 5170, 5170, 5175]","[5170, 5165]",2.05,True,5165,5180.740234375,110343.603515625,7061.990625,204.99999999999997,34,6969.999999999999,False,-6969.999999999999,103373.603515625,Bar 1: bear (close at 0.0% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=2.2 < 6.0),0,1.6400000000003274,1.7800000000006548,2.25,0.0,11.797752808986466,2.2222222221898846,"(5171.89, 5171.89, 5170.25, 5170.25)","(5170.25, 5170.97, 5170.11, 5170.32)","(5170.32, 5170.57, 5169.64, 5169.69)",0.360899,False,,False,,,0.0,False
2024-05-07,narrow_bear,5190.07,"[5185, 5190, 5190, 5195]","[5195, 5190]",2.05,True,5190,5187.7001953125,103373.603515625,6615.910625,204.99999999999997,0,0.0,False,0.0,103373.603515625,Bar 1: iron_butterfly (close at 48.3% of SPAN1) → BF conversion: BF-bear (1st bar close 48.3% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=3.1 < 6.0),0,2.9600000000000364,2.9600000000000364,3.130000000000109,48.31081081082005,90.87837837836375,56.86900958465442,"(5188.37, 5191.25, 5188.29, 5189.72)","(5189.72, 5191.0, 5189.72, 5190.98)","(5190.98, 5191.42, 5190.07, 5190.07)",0.363537,False,,False,,,0.0,False
2024-05-08,narrow_bear,5190.78,"[5185, 5190, 5190, 5195]","[5195, 5190]",2.05,True,5190,5187.669921875,103373.603515625,6615.910625,204.99999999999997,0,0.0,False,0.0,103373.603515625,Bar 1: iron_butterfly (close at 55.8% of SPAN1) → BF conversion: BF-bear (1st bar close 55.8% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=1.2 < 6.0),1,0.8599999999996726,0.8699999999998909,1.25,55.813953488342584,87.35632183911652,75.99999999998545,"(5190.21, 5191.07, 5190.21, 5190.69)","(5190.69, 5191.08, 5190.62, 5190.97)","(5190.97, 5190.97, 5189.83, 5190.78)",0.370015,False,,False,,,0.0,False
2024-05-09,narrow_bull,5213.92,"[5210, 5215, 5215, 5220]","[5210, 5215]",2.05,True,5215,5214.080078125,103373.603515625,8269.88828125,204.99999999999997,0,0.0,False,0.0,103373.603515625,Bar 1: iron_butterfly (close at 84.8% of SPAN1) → Bar 2 breakout: bull → Narrow conversion: narrow_bull (SPAN3=4.0 < 6.0),1,2.2299999999995634,3.0599999999994907,4.039999999999964,84.75336322869003,100.0,84.1584158415759,"(5210.52, 5212.75, 5210.52, 5212.41)","(5212.41, 5213.58, 5212.15, 5213.58)","(5213.58, 5214.56, 5213.58, 5213.92)",0.368829,False,,False,,,0.0,False
2024-05-10,narrow_bull,5223.07,"[5220, 5225, 5225, 5230]","[5220, 5225]",2.05,True,5225,5222.68017578125,103373.603515625,8269.88828125,204.99999999999997,0,0.0,False,0.0,103373.603515625,Bar 1: bull (close at 100.0% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=2.6 < 6.0),0,1.4200000000000728,2.4799999999995634,2.5900000000001455,100.0,90.32258064516839,91.11969111967336,"(5220.75, 5222.13, 5220.71, 5222.13)","(5222.13, 5223.19, 5222.13, 5222.95)","(5222.95, 5223.3, 5221.88, 5223.07)",0.368978,False,,False,,,0.0,False
2024-05-13,narrow_bull,5221.16,"[5215, 5220, 5220, 5225]","[5220, 5225]",2.05,True,5225,5221.419921875,103373.603515625,8269.88828125,204.99999999999997,40,8199.999999999998,False,-2520.312499999998,100853.291015625,Bar 1: iron_butterfly (close at 77.1% of SPAN1) → Bar 2 breakout: bull → Narrow conversion: narrow_bull (SPAN3=1.3 < 6.0),1,0.6999999999998181,0.819999999999709,1.2899999999999636,77.14285714287199,92.68292682932781,93.023255813942,"(5220.16, 5220.66, 5219.96, 5220.5)","(5220.5, 5220.78, 5220.31, 5220.72)","(5220.72, 5221.25, 5220.18, 5221.16)",0.366122,False,,False,,,0.0,False
2024-05-14,narrow_bull,5247.47,"[5240, 5245, 5245, 5250]","[5245, 5250]",2.05,True,5250,5246.68017578125,100853.291015625,8068.263281250001,204.99999999999997,39,7994.999999999999,False,-1442.314453124999,99410.9765625,Bar 1: iron_butterfly (close at 79.7% of SPAN1) → Bar 2 breakout: bull → Narrow conversion: narrow_bull (SPAN3=3.3 < 6.0),1,1.4799999999995634,2.5599999999994907,3.2799999999997453,79.72972972971147,93.75000000000443,76.21951219512788,"(5244.97, 5246.45, 5244.97, 5246.15)","(5246.15, 5247.53, 5246.15, 5247.37)","(5247.37, 5248.25, 5247.18, 5247.47)",0.367913,False,,False,,,0.0,False
2024-05-15,narrow_bull,5304.74,"[5300, 5305, 5305, 5310]","[5300, 5305]",2.05,True,5305,5308.14990234375,99410.9765625,7952.878125,204.99999999999997,0,0.0,False,0.0,99410.9765625,Bar 1: bull (close at 91.1% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=3.2 < 6.0),0,1.680000000000291,2.9200000000000728,3.230000000000473,91.07142857145178,100.0,65.94427244581415,"(5304.29, 5304.29, 5302.61, 5304.14)","(5304.14, 5305.53, 5304.14, 5305.53)","(5305.53, 5305.84, 5304.53, 5304.74)",0.371781,False,,False,,,0.0,False
2024-05-16,narrow_bull,5305.06,"[5300, 5305, 5305, 5310]","[5305, 5310]",2.05,True,5310,5297.10009765625,99410.9765625,7952.878125,204.99999999999997,38,7789.999999999999,False,-7789.999999999999,91620.9765625,Bar 1: bull (close at 100.0% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=4.8 < 6.0),1,3.2799999999997453,4.770000000000437,4.770000000000437,100.0,74.63312368972902,87.84067085954142,"(5300.88, 5304.15, 5300.87, 5304.15)","(5304.15, 5305.64, 5304.15, 5304.43)","(5304.43, 5305.14, 5304.22, 5305.06)",0.375194,False,,False,,,0.0,False
2024-05-17,narrow_bull,5301.51,"[5295, 5300, 5300, 5305]","[5300, 5305]",2.05,True,5305,5303.27001953125,91620.9765625,7329.678125,204.99999999999997,35,7174.999999999999,True,4270.068359375001,95891.044921875,Bar 1: bull (close at 100.0% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=3.9 < 6.0),0,1.930000000000291,3.3400000000001455,3.9400000000005093,100.0,91.01796407185122,95.68527918781598,"(5299.24, 5299.67, 5297.74, 5299.67)","(5299.67, 5301.08, 5299.67, 5300.78)","(5300.78, 5301.68, 5300.77, 5301.51)",0.371871,False,,False,,,0.0,False
2024-05-20,narrow_bear,5306.11,"[5300, 5305, 5305, 5310]","[5310, 5305]",2.05,True,5305,5308.1298828125,95891.044921875,6137.0268750000005,204.99999999999997,29,5944.999999999999,False,-521.6601562499991,95369.384765625,Bar 1: bear (close at 1.3% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=1.3 < 6.0),-1,0.7899999999999636,1.2600000000002183,1.2600000000002183,1.2658227847226893,26.190476190465876,44.444444444396325,"(5306.46, 5306.81, 5306.02, 5306.03)","(5306.03, 5306.36, 5305.55, 5305.88)","(5305.88, 5306.55, 5305.88, 5306.11)",0.372021,False,,False,,,0.0,False
2024-05-21,narrow_bull,5318.41,"[5315, 5320, 5320, 5325]","[5315, 5320]",2.05,True,5320,5321.41015625,95369.384765625,7629.55078125,204.99999999999997,0,0.0,False,0.0,95369.384765625,Bar 1: iron_butterfly (close at 77.8% of SPAN1) → BF conversion: BF-bull (1st bar close 77.8% > 66%) → Narrow conversion: narrow_bull (SPAN3=1.2 < 6.0),-1,0.8100000000004002,0.8100000000004002,1.180000000000291,77.77777777775282,11.111111111123586,14.406779661019561,"(5318.61, 5319.42, 5318.61, 5319.24)","(5319.24, 5319.24, 5318.61, 5318.7)","(5318.7, 5318.8, 5318.24, 5318.41)",0.372702,False,,False,,,0.0,False
2024-05-22,narrow_bear,5292.16,"[5285, 5290, 5290, 5295]","[5295, 5290]",2.05,True,5290,5307.009765625,95369.384765625,6103.640625,204.99999999999997,29,5944.999999999999,False,-5944.999999999999,89424.384765625,Bar 1: iron_butterfly (close at 35.8% of SPAN1) → BF conversion: BF-bear (1st bar close 35.8% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=2.6 < 6.0),-1,2.569999999999709,2.569999999999709,2.569999999999709,35.79766536965669,35.79766536965669,15.564202334617958,"(5294.13, 5294.33, 5291.76, 5292.68)","(5292.68, 5293.33, 5292.35, 5292.68)","(5292.68, 5292.7, 5291.94, 5292.16)",0.370936,False,,False,,,0.0,False
2024-05-23,narrow_bull,5267.1,"[5260, 5265, 5265, 5270]","[5265, 5270]",2.05,True,5270,5267.83984375,89424.384765625,7153.950781250001,204.99999999999997,34,6969.999999999999,True,2685.468750000001,92109.853515625,Bar 1: bull (close at 89.8% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=5.5 < 6.0),-1,5.470000000000255,5.470000000000255,5.470000000000255,89.76234003655622,45.52102376599023,18.64716636198152,"(5266.19, 5271.55, 5266.08, 5270.99)","(5270.99, 5271.01, 5268.44, 5268.57)","(5268.57, 5268.79, 5267.05, 5267.1)",0.372425,False,,False,,,0.0,False
2024-05-24,narrow_bull,5299.07,"[5295, 5300, 5300, 5305]","[5295, 5300]",2.05,True,5300,5304.72021484375,92109.853515625,7368.78828125,204.99999999999997,0,0.0,False,0.0,92109.853515625,Bar 1: bull (close at 100.0% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=2.3 < 6.0),0,1.8900000000003274,2.2799999999997453,2.2799999999997453,100.0,96.92982456141593,43.42105263157423,"(5298.13, 5299.97, 5298.08, 5299.97)","(5299.97, 5300.36, 5299.6, 5300.29)","(5300.29, 5300.32, 5299.0, 5299.07)",0.373983,False,,False,,,0.0,False
2024-05-28,narrow_bull,5296.97,"[5290, 5295, 5295, 5300]","[5295, 5300]",2.05,True,5300,5306.0400390625,92109.853515625,7368.78828125,204.99999999999997,35,7174.999999999999,True,10325.0,102434.853515625,Bar 1: iron_butterfly (close at 82.1% of SPAN1) → BF conversion: BF-bull (1st bar close 82.1% > 66%) → Narrow conversion: narrow_bull (SPAN3=3.3 < 6.0),1,2.2399999999997817,3.2899999999999636,3.2899999999999636,82.14285714283103,66.86930091184932,89.66565349543619,"(5294.04, 5296.26, 5294.02, 5295.86)","(5295.86, 5297.31, 5295.86, 5296.22)","(5296.22, 5296.97, 5295.86, 5296.97)",0.365345,False,,False,,,0.0,False
2024-05-29,narrow_bear,5269.46,"[5265, 5270, 5270, 5275]","[5270, 5265]",2.05,True,5265,5266.9501953125,102434.853515625,6555.8306250000005,204.99999999999997,31,6354.999999999999,True,3099.394531250001,105534.248046875,Bar 1: bear (close at 1.4% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=4.8 < 6.0),0,0.7399999999997817,1.1199999999998909,4.8200000000006185,1.3513513512583424,9.821428571400295,2.0746887966877794,"(5273.32, 5273.8, 5273.06, 5273.07)","(5273.07, 5274.18, 5273.07, 5273.17)","(5273.17, 5273.17, 5269.36, 5269.46)",0.363726,False,,False,,,0.0,False
2024-05-30,narrow_bull,5227.5,"[5225, 5230, 5230, 5235]","[5225, 5230]",2.05,True,5230,5235.47998046875,105534.248046875,8442.73984375,204.99999999999997,0,0.0,False,0.0,105534.248046875,Bar 1: iron_butterfly (close at 39.8% of SPAN1) → Bar 2 breakout: bull → Narrow conversion: narrow_bull (SPAN3=5.4 < 6.0),0,3.389999999999418,3.7100000000000364,5.399999999999636,39.823008849548266,100.0,100.0,"(5224.1, 5225.49, 5222.1, 5223.45)","(5223.45, 5225.81, 5223.3, 5225.81)","(5225.81, 5227.5, 5225.71, 5227.5)",0.365625,False,,False,,,0.0,False
2024-05-31,narrow_bull,5255.48,"[5250, 5255, 5255, 5260]","[5255, 5260]",2.05,True,5260,5277.509765625,105534.248046875,8442.73984375,204.99999999999997,41,8404.999999999998,True,12095.000000000002,117629.248046875,Bar 1: bull (close at 92.5% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=5.8 < 6.0),-1,5.3400000000001455,5.779999999999745,5.779999999999745,92.50936329587014,59.861591695505,20.58823529411163,"(5254.29, 5259.63, 5254.29, 5259.23)","(5259.23, 5260.07, 5257.75, 5257.75)","(5257.75, 5257.75, 5254.32, 5255.48)",0.371694,False,,False,,,0.0,False
2024-06-03,narrow_bull,5271.49,"[5265, 5270, 5270, 5275]","[5270, 5275]",2.05,True,5275,5283.39990234375,117629.248046875,9410.33984375,204.99999999999997,45,9224.999999999998,True,13275.000000000002,130904.248046875,Bar 1: iron_butterfly (close at 70.5% of SPAN1) → BF conversion: BF-bull (1st bar close 70.5% > 66%) → Narrow conversion: narrow_bull (SPAN3=2.4 < 6.0),-1,1.930000000000291,1.930000000000291,2.3900000000003274,70.46632124349573,17.098445595848574,62.34309623429195,"(5271.57, 5272.39, 5270.46, 5271.82)","(5271.82, 5271.82, 5270.53, 5270.79)","(5270.79, 5271.49, 5270.0, 5271.49)",0.363592,False,,False,,,0.0,False
2024-06-04,narrow_bull,5292.63,"[5290, 5295, 5295, 5300]","[5290, 5295]",2.05,True,5295,5291.33984375,130904.248046875,10472.33984375,204.99999999999997,51,10454.999999999998,False,-3621.796874999998,127282.451171875,Bar 1: bull (close at 100.0% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=5.2 < 6.0),0,2.9700000000002547,3.0900000000001455,5.150000000000546,100.0,100.0,100.0,"(5287.57, 5290.45, 5287.48, 5290.45)","(5290.45, 5290.57, 5289.9, 5290.57)","(5290.57, 5292.63, 5290.09, 5292.63)",0.363022,False,,False,,,0.0,False
2024-06-05,bull,5346.37,"[5345, 5350]","[5345, 5350]",2.2,False,5350,5354.02978515625,127282.451171875,10182.59609375,220.00000000000003,46,10120.000000000002,True,12879.999999999998,140162.451171875,Bar 1: bull (close at 100.0% of SPAN1),1,3.7100000000000364,5.260000000000218,7.329999999999927,100.0,86.12167300379386,74.89768076398138,"(5342.93, 5344.59, 5340.88, 5344.59)","(5344.59, 5346.14, 5344.52, 5345.41)","(5345.41, 5348.21, 5345.27, 5346.37)",0.299171,False,,False,,,0.0,False
2024-06-06,narrow_bull,5353.06,"[5350, 5355, 5355, 5360]","[5350, 5355]",2.05,True,5355,5352.9599609375,140162.451171875,11212.99609375,204.99999999999997,54,11069.999999999998,True,4913.789062500002,145076.240234375,Bar 1: iron_butterfly (close at 70.9% of SPAN1) → BF conversion: BF-bull (1st bar close 70.9% > 66%) → Narrow conversion: narrow_bull (SPAN3=1.7 < 6.0),-1,1.7200000000002547,1.7200000000002547,1.7200000000002547,70.93023255814384,26.744186046509782,62.79069767445795,"(5351.98, 5353.7, 5351.98, 5353.2)","(5353.2, 5353.28, 5352.35, 5352.44)","(5352.44, 5353.13, 5352.21, 5353.06)",0.372528,False,,False,,,0.0,False
2024-06-07,narrow_bull,5348.64,"[5345, 5350, 5350, 5355]","[5345, 5350]",2.05,True,5350,5346.990234375,145076.240234375,11606.09921875,204.99999999999997,56,11479.999999999998,False,-334.6874999999982,144741.552734375,Bar 1: bull (close at 100.0% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=5.7 < 6.0),1,4.399999999999636,5.6799999999993815,5.6799999999993815,100.0,85.5633802816937,71.8309859154995,"(5347.47, 5348.96, 5344.56, 5348.96)","(5348.96, 5350.24, 5348.96, 5349.42)","(5349.42, 5349.42, 5348.1, 5348.64)",0.362912,False,,False,,,0.0,False
2024-06-10,narrow_bull,5356.06,"[5350, 5355, 5355, 5360]","[5355, 5360]",2.05,True,5360,5360.7900390625,144741.552734375,11579.32421875,204.99999999999997,56,11479.999999999998,True,16520.0,161261.552734375,Bar 1: bull (close at 89.3% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=3.0 < 6.0),1,2.9899999999997817,2.9899999999997817,2.9899999999997817,89.29765886288521,59.53177257524666,83.94648829432782,"(5354.97, 5356.54, 5353.55, 5356.22)","(5356.22, 5356.51, 5355.33, 5355.33)","(5355.33, 5356.41, 5355.32, 5356.06)",0.363825,False,,False,,,0.0,False
2024-06-11,narrow_bull,5366.45,"[5360, 5365, 5365, 5370]","[5365, 5370]",2.05,True,5370,5375.31982421875,161261.552734375,12900.92421875,204.99999999999997,62,12709.999999999998,True,18290.0,179551.552734375,Bar 1: bull (close at 94.7% of SPAN1) → Narrow conversion: narrow_bull (SPAN3=3.6 < 6.0),1,2.4700000000002547,2.7399999999997817,3.619999999999891,94.73684210525929,87.59124087590611,100.0,"(5363.45, 5365.3, 5362.83, 5365.17)","(5365.17, 5365.57, 5364.75, 5365.23)","(5365.23, 5366.45, 5364.43, 5366.45)",0.364562,False,,False,,,0.0,False
2024-06-12,bull,5421.97,"[5420, 5425]","[5420, 5425]",2.4,False,5425,5421.02978515625,179551.552734375,14364.124218750001,240.0,59,14160.0,False,-8084.267578125,171467.28515625,Bar 1: iron_butterfly (close at 40.6% of SPAN1) → Bar 3 breakout: bull,0,5.390000000000327,5.390000000000327,6.230000000000473,40.6307977736619,69.0166975881267,94.7030497592311,"(5421.4, 5421.46, 5416.07, 5418.26)","(5418.26, 5420.81, 5418.05, 5419.79)","(5419.79, 5422.3, 5419.29, 5421.97)",0.299931,False,,False,,,0.0,False
2024-06-13,narrow_bear,5434.22,"[5430, 5435, 5435, 5440]","[5435, 5430]",2.05,True,5430,5433.740234375,171467.28515625,10973.90625,204.99999999999997,53,10864.999999999998,False,-4188.242187499998,167279.04296875,Bar 1: iron_butterfly (close at 57.4% of SPAN1) → BF conversion: BF-bear (1st bar close 57.4% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=1.9 < 6.0),1,1.550000000000182,1.9099999999998545,1.9099999999998545,57.41935483872407,77.48691099479507,75.39267015710047,"(5434.03, 5434.33, 5432.78, 5433.67)","(5433.67, 5434.69, 5433.57, 5434.26)","(5434.26, 5434.46, 5433.41, 5434.22)",0.368651,False,,False,,,0.0,False
2024-06-14,narrow_bear,5424.63,"[5420, 5425, 5425, 5430]","[5425, 5420]",2.05,True,5420,5431.60009765625,167279.04296875,10705.85875,204.99999999999997,52,10659.999999999998,False,-10659.999999999998,156619.04296875,Bar 1: iron_butterfly (close at 60.2% of SPAN1) → Bar 3 breakout: bear → Narrow conversion: narrow_bear (SPAN3=1.8 < 6.0),0,1.3299999999999272,1.3299999999999272,1.7899999999999636,60.1503759398666,30.075187969899105,0.0,"(5425.09, 5426.42, 5425.09, 5425.89)","(5425.89, 5426.07, 5425.27, 5425.49)","(5425.49, 5425.55, 5424.63, 5424.63)",0.376027,False,,False,,,0.0,False
2024-06-17,narrow_bear,5477.92,"[5475, 5480, 5480, 5485]","[5480, 5475]",2.05,True,5475,5473.22998046875,156619.04296875,10023.61875,204.99999999999997,48,9839.999999999998,True,14160.000000000002,170779.04296875,Bar 1: iron_butterfly (close at 30.1% of SPAN1) → BF conversion: BF-bear (1st bar close 30.1% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=3.1 < 6.0),1,3.119999999999891,3.119999999999891,3.119999999999891,30.128205128193358,28.525641025623365,74.67948717948745,"(5478.71, 5478.71, 5475.59, 5476.53)","(5476.53, 5478.46, 5476.39, 5476.48)","(5476.48, 5478.04, 5476.31, 5477.92)",0.370122,False,,False,,,0.0,False
2024-06-18,narrow_bull,5484.65,"[5480, 5485, 5485, 5490]","[5480, 5485]",2.05,True,5485,5487.02978515625,170779.04296875,13662.323437500001,204.99999999999997,0,0.0,False,0.0,170779.04296875,Bar 1: iron_butterfly (close at 83.2% of SPAN1) → Bar 2 breakout: bull → Narrow conversion: narrow_bull (SPAN3=3.6 < 6.0),1,2.7899999999999636,2.9399999999996,3.639999999999418,83.15412186378992,92.1768707483131,80.49450549450138,"(5481.72, 5484.51, 5481.72, 5484.04)","(5484.04, 5484.66, 5484.04, 5484.43)","(5484.43, 5485.36, 5484.17, 5484.65)",0.370026,False,,False,,,0.0,False
2024-06-20,bull,5477.31,"[5475, 5480]","[5475, 5480]",2.5,False,5480,5473.169921875,170779.04296875,13662.323437500001,250.0,54,13500.0,False,-13500.0,157279.04296875,Bar 1: iron_butterfly (close at 77.2% of SPAN1) → Bar 2 breakout: bull,1,2.7200000000002547,5.300000000000182,6.289999999999964,77.20588235294731,94.15094339623623,91.89189189190283,"(5474.23, 5474.25, 5471.53, 5473.63)","(5473.63, 5476.83, 5473.63, 5476.52)","(5476.52, 5477.82, 5476.14, 5477.31)",0.308895,False,,False,,,0.0,False
2024-06-21,narrow_bear,5465.29,"[5460, 5465, 5465, 5470]","[5470, 5465]",2.05,True,5465,5464.6201171875,157279.04296875,10065.85875,204.99999999999997,49,10044.999999999998,True,14455.000000000002,171734.04296875,Bar 1: bear (close at 0.0% of SPAN1) → Narrow conversion: narrow_bear (SPAN3=3.4 < 6.0),0,3.4000000000005457,3.4400000000005093,3.4400000000005093,0.0,39.53488372091486,99.41860465115019,"(5463.53, 5465.31, 5461.91, 5461.91)","(5461.91, 5463.31, 5461.87, 5463.23)","(5463.23, 5465.29, 5463.23, 5465.29)",0.369283,False,,False,,,0.0,False
2024-06-24,narrow_bear,5459.12,"[5455, 5460, 5460, 5465]","[5460, 5455]",2.05,True,5455,5447.8701171875,171734.04296875,10990.97875,204.99999999999997,53,10864.999999999998,True,15635.000000000002,187369.04296875,Bar 1: iron_butterfly (close at 61.2% of SPAN1) → BF conversion: BF-bear (1st bar close 61.2% ≤ 66%) → Narrow conversion: narrow_bear (SPAN3=4.6 < 6.0),1,2.319999999999709,4.569999999999709,4.569999999999709,61.20689655173496,67.83369803062695,71.55361050327649,"(5456.71, 5458.17, 5455.85, 5457.27)","(5457.27, 5460.42, 5457.27, 5458.95)","(5458.95, 5460.07, 5458.95, 5459.12)",0.361841,False,,False,,,0.0,False
