#!/usr/bin/env python3
"""
Test script to demonstrate live-style backtest trade reports
"""

import sys
import os
import logging

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from historical_backtester import HistoricalBacktester


def test_live_style_reports():
    """Test the new live-style trade report generation"""
    
    print("🧪 Testing Live-Style Backtest Trade Reports")
    print("=" * 50)
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Run a small backtest (last 5 days)
    from datetime import datetime, timedelta
    end_date = datetime.now()
    start_date = end_date - timedelta(days=5)
    
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')
    
    print(f"📅 Running backtest for {start_date_str} to {end_date_str}")
    
    # Create backtester
    backtester = HistoricalBacktester(
        start_date=start_date_str,
        end_date=end_date_str,
        simple_mode=True,  # Use simple mode for quick testing
        skip_monday=False
    )
    
    # Run backtest
    results = backtester.run_backtest()
    
    if not results:
        print("❌ No results generated. Check database connection and date range.")
        return False
    
    print(f"✅ Generated {len(results)} trade results")
    
    # Test both report styles for the first trade
    if results:
        test_result = results[0]
        
        print(f"\n📊 Generating reports for {test_result.trading_date} ({test_result.decision})...")
        
        # Generate legacy-style report
        print("📝 Generating legacy-style report...")
        legacy_path = backtester.generate_trade_report(
            test_result, 
            output_dir="test_reports_legacy",
            use_live_style=False
        )
        print(f"   Legacy report: {legacy_path}")
        
        # Generate live-style report
        print("📝 Generating live-style report...")
        live_path = backtester.generate_trade_report(
            test_result, 
            output_dir="test_reports_live_style",
            use_live_style=True
        )
        print(f"   Live-style report: {live_path}")
        
        # Compare file sizes and structure
        try:
            import os
            legacy_size = os.path.getsize(legacy_path)
            live_size = os.path.getsize(live_path)
            
            print(f"\n📈 Report Comparison:")
            print(f"   Legacy format: {legacy_size:,} bytes")
            print(f"   Live-style format: {live_size:,} bytes")
            print(f"   Size difference: {live_size - legacy_size:+,} bytes")
            
            # Show key features of live-style report
            print(f"\n🌟 Live-Style Report Features:")
            print(f"   ✅ Decision labels ('Bull Spread' vs 'bull')")
            print(f"   ✅ Decision timeline with simulated events")
            print(f"   ✅ Enhanced strike details with Long/Short positions")
            print(f"   ✅ Live-mode-style sections and formatting")
            print(f"   ✅ Strategy logic breakdown with thresholds")
            
        except Exception as e:
            print(f"⚠️  Could not compare files: {e}")
        
        print(f"\n🎯 Test completed successfully!")
        print(f"   Both report styles available in test_reports_* directories")
        return True
    
    return False


def demonstrate_batch_generation():
    """Demonstrate batch report generation with live-style formatting"""
    
    print("\n🔄 Testing Batch Report Generation")
    print("=" * 40)
    
    # Run a slightly larger backtest
    from datetime import datetime, timedelta
    end_date = datetime.now()
    start_date = end_date - timedelta(days=10)
    
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')
    
    print(f"📅 Running backtest for {start_date_str} to {end_date_str}")
    
    # Create backtester
    backtester = HistoricalBacktester(
        start_date=start_date_str,
        end_date=end_date_str,
        simple_mode=True,
        skip_monday=False
    )
    
    # Run backtest
    results = backtester.run_backtest()
    
    if not results:
        print("❌ No results for batch testing")
        return False
    
    print(f"✅ Generated {len(results)} results for batch testing")
    
    # Generate reports for losing trades only (faster)
    losing_trades = [r for r in results if not r.trade_successful]
    
    if losing_trades:
        print(f"\n📊 Generating live-style reports for {len(losing_trades)} losing trades...")
        
        report_paths = backtester.generate_losing_trades_reports(
            results, 
            output_dir="test_batch_reports", 
            use_live_style=True
        )
        
        print(f"✅ Generated {len(report_paths)} batch reports")
        print(f"   Available in test_batch_reports/ directory")
    else:
        print("🎉 No losing trades found - all trades were profitable!")
    
    return True


if __name__ == "__main__":
    success = test_live_style_reports()
    
    if success:
        demonstrate_batch_generation()
        
        print(f"\n🎊 All tests completed successfully!")
        print(f"📁 Check the following directories for generated reports:")
        print(f"   • test_reports_legacy/ - Traditional backtest format")
        print(f"   • test_reports_live_style/ - New live-mode-style format")
        print(f"   • test_batch_reports/ - Batch generated reports")
    else:
        print(f"\n❌ Tests failed. Check database connection and configuration.") 