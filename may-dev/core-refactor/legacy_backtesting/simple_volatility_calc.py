#!/usr/bin/env python3
"""
Simple volatility calculation from available SPX data
Works with whatever data format we have in the database
"""

from database_connector import DatabaseConnector
import numpy as np
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def simple_volatility_calc(start_date='2025-01-01', end_date='2025-06-18'):
    """Calculate volatility from any available SPX data"""
    
    db = DatabaseConnector()
    
    try:
        with db:
            # Get trading dates
            trading_dates = db.get_trading_dates(start_date, end_date)
            logger.info(f"Found {len(trading_dates)} trading dates")
            
            all_returns = []
            successful_days = 0
            
            for i, date in enumerate(trading_dates):
                try:
                    # Get whatever data is available for this day
                    daily_data = db.get_daily_end_of_day_data(date)
                    
                    if not daily_data or len(daily_data) < 2:
                        continue
                    
                    # Extract close prices and convert to float
                    closes = []
                    for row in daily_data:
                        try:
                            close_price = float(row[5])  # close is column 5, convert to float
                            closes.append(close_price)
                        except (ValueError, TypeError):
                            continue  # Skip invalid values
                    
                    if len(closes) < 2:
                        continue
                    
                    # Calculate returns from consecutive prices
                    day_returns = []
                    for j in range(1, len(closes)):
                        if closes[j-1] > 0:  # Avoid division by zero
                            ret = (closes[j] - closes[j-1]) / closes[j-1]
                            day_returns.append(ret)
                    
                    if day_returns:
                        all_returns.extend(day_returns)
                        successful_days += 1
                        
                        if (i + 1) % 20 == 0:
                            logger.info(f"Processed {i+1}/{len(trading_dates)} dates, {successful_days} successful")
                
                except Exception as e:
                    logger.warning(f"Error processing {date}: {e}")
                    continue
            
            if not all_returns:
                logger.error("No returns calculated")
                return None
            
            logger.info(f"Successfully processed {successful_days} days with {len(all_returns)} total returns")
            
            # Calculate basic statistics
            returns_array = np.array(all_returns)
            
            # Basic stats
            mean_return = np.mean(returns_array)
            std_return = np.std(returns_array)
            
            # Estimate frequency (rough approximation)
            avg_observations_per_day = len(all_returns) / successful_days
            logger.info(f"Average observations per day: {avg_observations_per_day:.1f}")
            
            # Conservative annualization assumptions:
            # If we have ~60-120 observations per day, assume 10-second intervals
            # If we have ~20-40 observations, assume ~30-second intervals  
            # If we have <20 observations, assume minute+ intervals
            
            if avg_observations_per_day > 100:
                # Assume 10-second data
                intervals_per_year = 252 * 6.5 * 60 * 6  # 252 days * 6.5 hours * 360 ten-second intervals
                frequency_desc = "~10-second intervals"
            elif avg_observations_per_day > 40:
                # Assume 30-second to 1-minute data  
                intervals_per_year = 252 * 6.5 * 60 * 2  # Assume 30-second intervals
                frequency_desc = "~30-second intervals"
            else:
                # Assume 1-3 minute data
                intervals_per_year = 252 * 6.5 * 60  # Assume 1-minute intervals
                frequency_desc = "~1-minute intervals"
            
            # Annualized volatility
            annualized_vol = std_return * np.sqrt(intervals_per_year)
            
            # Additional estimates for different scenarios
            # Conservative estimate (lower frequency assumption)
            conservative_vol = std_return * np.sqrt(252 * 6.5 * 60)  # Assume 1-minute
            
            # Aggressive estimate (higher frequency assumption) 
            aggressive_vol = std_return * np.sqrt(252 * 6.5 * 60 * 6)  # Assume 10-second
            
            results = {
                'successful_days': successful_days,
                'total_returns': len(all_returns),
                'avg_obs_per_day': avg_observations_per_day,
                'frequency_desc': frequency_desc,
                'mean_return': mean_return,
                'std_return': std_return,
                'annualized_vol': annualized_vol,
                'conservative_vol': conservative_vol,
                'aggressive_vol': aggressive_vol,
                'date_range': f"{start_date} to {end_date}"
            }
            
            return results
            
    except Exception as e:
        logger.error(f"Error in volatility calculation: {e}")
        return None

def print_simple_volatility_results(results):
    """Print the volatility calculation results"""
    
    if not results:
        print("No volatility results available")
        return
    
    print("SIMPLE SPX VOLATILITY ANALYSIS")
    print("=" * 60)
    print(f"Date Range: {results['date_range']}")
    print(f"Successful days: {results['successful_days']}")
    print(f"Total returns: {results['total_returns']:,}")
    print(f"Avg observations/day: {results['avg_obs_per_day']:.1f}")
    print(f"Estimated frequency: {results['frequency_desc']}")
    print()
    
    print("RETURN STATISTICS:")
    print("-" * 30)
    print(f"Mean return: {results['mean_return']:.6f}")
    print(f"Return std dev: {results['std_return']:.6f}")
    print()
    
    print("ANNUALIZED VOLATILITY ESTIMATES:")
    print("-" * 30)
    print(f"Conservative (1-min): {results['conservative_vol']:.1%}")
    print(f"Best estimate:        {results['annualized_vol']:.1%}")
    print(f"Aggressive (10-sec):  {results['aggressive_vol']:.1%}")
    print()
    
    print("RECOMMENDED VOLATILITY FOR OPTIONS PRICING:")
    print("-" * 30)
    
    # Test different volatility levels
    from options_pricer import price_iron_butterfly
    
    vol_tests = [
        ("Current Model", 0.20),
        ("Conservative", results['conservative_vol']),
        ("Best Estimate", results['annualized_vol']), 
        ("Aggressive", results['aggressive_vol']),
        ("Manual 40%", 0.40),
        ("Manual 50%", 0.50)
    ]
    
    print("Vol Scenario        | Volatility | IB Credit | Above $3.40?")
    print("-" * 55)
    
    for name, vol in vol_tests:
        credit = abs(price_iron_butterfly(S=6000, center_strike=6000, T=8.5, sigma=vol))
        meets_min = "YES" if credit >= 3.40 else "NO"
        print(f"{name:18} | {vol:9.1%} | ${credit:8.2f} | {meets_min}")
    
    print()
    print("RECOMMENDATION:")
    print("-" * 30)
    best_vol = results['annualized_vol']
    
    if best_vol < 0.30:
        print(f"Calculated volatility ({best_vol:.1%}) seems low for end-of-day options.")
        print("Consider using 40-50% for more realistic pricing.")
        recommended = 0.40
    elif best_vol > 0.80:
        print(f"Calculated volatility ({best_vol:.1%}) seems very high.")
        print("Consider using 50-60% for more conservative pricing.")
        recommended = 0.55
    else:
        print(f"Calculated volatility ({best_vol:.1%}) seems reasonable.")
        recommended = best_vol
    
    print(f"Recommended volatility: {recommended:.1%}")

if __name__ == "__main__":
    print("Calculating simple volatility from available SPX data...")
    
    results = simple_volatility_calc()
    
    if results:
        print_simple_volatility_results(results)
    else:
        print("Failed to calculate volatility from available data") 