# SPX Historical Backtesting System

This system processes historical 10-second SPX data to backtest the trading strategy using the decision engine.

## Overview

The system:
1. Connects to SQL Server database containing 10-second SPX data
2. For each trading day, extracts the last 3 minutes of trading data (15:49:50 to 15:51:20)
3. Uses the 15:49:50 bar close to normalize the first combined bar
4. Combines 9 ten-second bars (15:50:00 to 15:51:20) into three 30-second bars
5. Adjusts bar opens to create normalized linked bars
6. Runs the decision engine to get trading decisions
7. Compares results against end-of-day closing price
8. Generates comprehensive reports

## Files

- `database_connector.py` - Handles SQL Server connections and data queries
- `bar_processor.py` - Combines and normalizes bar data
- `decision_engine.py` - Standalone decision engine (extracted from main system)
- `historical_backtester.py` - Main backtesting runner
- `test_decision_engine.py` - Validates decision engine against actual system
- `test_system.py` - End-to-end system validation
- `requirements.txt` - Python dependencies

## Setup

### 1. Install Dependencies

```bash
pip install -r backtesting/requirements.txt
```

### 2. Database Configuration

Set environment variables for database connection:

```bash
# Option 1: SQL Server Authentication
export DB_SERVER="your-server"
export DB_NAME="IBDB_DEV"
export DB_USER="username"
export DB_PASSWORD="password"

# Option 2: Windows Authentication (if running on Windows)
export DB_SERVER="your-server"
export DB_NAME="IBDB_DEV"
# No username/password needed
```

### 3. Test the System

```bash
python backtesting/test_system.py
```

## Usage

### Basic Backtest

```bash
python backtesting/historical_backtester.py --start-date 2025-01-01 --end-date 2025-01-31
```

### With Verbose Logging

```bash
python backtesting/historical_backtester.py --start-date 2025-01-01 --end-date 2025-01-31 --verbose
```

### Custom Output Directory

```bash
python backtesting/historical_backtester.py --start-date 2025-01-01 --end-date 2025-01-31 --output-dir ./results
```

## Data Requirements

The system expects SPX data in this format:

```sql
SELECT [id], [date], [open], [high], [low], [close]
FROM [IBDB_DEV].[dbo].[SPX]
```

For each trading day, it needs:
- One bar at 15:49:50 (pre-bar for setting first bar open)
- Nine bars from 15:50:00 to 15:51:20 (every 10 seconds)
- One bar at 15:59:50 (end-of-day closing price)

## Bar Processing Logic

1. **Combine 9 ten-second bars into 3 thirty-second bars:**
   - Bar 1: 15:50:00, 15:50:10, 15:50:20 (3 ten-second bars)
   - Bar 2: 15:50:30, 15:50:40, 15:50:50 (3 ten-second bars)
   - Bar 3: 15:51:00, 15:51:10, 15:51:20 (3 ten-second bars)

2. **Adjust opens for normalization:**
   - Bar 1 open = close of 15:49:50 bar (used only for normalization)
   - Bar 2 open = close of Bar 1
   - Bar 3 open = close of Bar 2
   - High/low adjusted if new open extends range

## Decision Engine

The standalone decision engine implements the same logic as the main trading system:

- **Bull/Bear Breakouts:** Based on close percentages and range breakouts
- **Iron Butterfly:** For neutral/contained price action with bias direction
- **Narrow Conversions:** When SPAN3 < 4.0 points

## Trade Outcome Logic

- **Bull/Narrow Bull:** Win if closing price >= target price
- **Bear/Narrow Bear:** Win if closing price <= target price  
- **Iron Butterfly:** Win if closing price within $5 of target

## Output Files

The system generates:

1. **CSV Results:** Detailed trade-by-trade results
   - `backtest_results_YYYYMMDD_HHMMSS.csv`

2. **JSON Summary:** High-level statistics
   - `backtest_summary_YYYYMMDD_HHMMSS.json`

## Example Output

```
HISTORICAL BACKTEST SUMMARY
============================================================
Date Range: 2025-01-01 to 2025-01-31
Total Trades: 23
Winning Trades: 15
Losing Trades: 8
Win Rate: 65.2%
Total P&L: 12.5
Average P&L: 0.54

Decision Breakdown:
  bull: 8 trades (75.0% win rate)
  bear: 6 trades (50.0% win rate)
  iron_butterfly: 7 trades (71.4% win rate)
  narrow_bull: 2 trades (50.0% win rate)
============================================================
```

## Troubleshooting

### Database Connection Issues

1. Check environment variables
2. Verify SQL Server driver installation
3. Test network connectivity to database
4. Ensure proper permissions

### Missing Data

The system will skip days with insufficient data and log warnings. Check:
- Data completeness in the source database
- Correct time zones and market hours
- Weekend/holiday handling

### Performance

For large date ranges:
- Run in smaller chunks
- Use `--verbose` flag to monitor progress
- Consider running on a machine closer to the database

## Validation

Compare results with the main trading system using:

```bash
python backtesting/test_decision_engine.py
```

This validates that the standalone decision engine produces identical results to the main system. 