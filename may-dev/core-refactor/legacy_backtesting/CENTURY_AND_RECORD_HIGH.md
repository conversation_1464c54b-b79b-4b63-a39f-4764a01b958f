# Century Mark and Record High Adjustment Specification

## Overview
New backtesting mode called `record-high-check` that adjusts target prices based on SPX proximity to record highs and century marks, then re-evaluates trading decisions based on the adjusted targets.

## Mode Description
- **Base Mode**: Same as `butterfly-conversion-latest` 
- **Additional Feature**: Target price adjustments for psychological price levels
- **Decision Re-evaluation**: Bulls/bears determined by adjusted target vs execution price

## Core Logic

### 1. Record High Adjustment (Bulls Only)
**Trigger Conditions:**
- Initial decision is `bull` or `narrow_bull`
- SPX is at record high OR within $10 of record high
- This is the **first record high in more than 3 months**
  - If another record high occurred within 3 months, no adjustment applies
  - Rationale: "People do not hesitate if record highs are within recent memory"

**Adjustment:**
- Move target price **down by $5** (one strike down)
- Example: Original target $6215 → Adjusted target $6210

### 2. Century Mark Adjustment (Bulls and Bears)
**Trigger Conditions:**
- Initial decision is `bull`, `narrow_bull`, `bear`, or `narrow_bear`
- SPX is within **$14** of nearest century mark (6200, 6300, 6400, etc.)

**Adjustment:**
- Apply **$5 pull effect** towards the century mark
- If SPX > century mark: target moves **down $5**
- If SPX < century mark: target moves **up $5**
- Example: SPX at 6214, century mark 6200 → target moves down $5

### 3. Decision Re-evaluation
After target price adjustments:
- Compare **adjusted target** to **execution price**
- If adjusted target < execution price → `bear` (or `narrow_bear`)
- If adjusted target > execution price → `bull` (or `narrow_bull`)
- Preserve narrow designation if applicable

## Implementation Requirements

### Database Schema
New table: `record_high_adjustments`
```sql
CREATE TABLE record_high_adjustments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    trading_date DATE NOT NULL,
    original_decision VARCHAR(20) NOT NULL,
    original_target_price DECIMAL(10,2) NOT NULL,
    adjusted_target_price DECIMAL(10,2) NOT NULL,
    adjustment_type ENUM('record_high', 'century_mark') NOT NULL,
    adjustment_reason TEXT,
    execution_price DECIMAL(10,2) NOT NULL,
    final_decision VARCHAR(20) NOT NULL,
    spx_price DECIMAL(10,2),
    nearest_century_mark DECIMAL(10,2),
    record_high_price DECIMAL(10,2),
    days_since_last_record_high INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Code Implementation

#### New Parameters
- Add `record_high_check: bool = False` to `HistoricalBacktester.__init__()`
- Add `--record-high-check` command line argument
- Update filename suffix: `_record_high_check`

#### Helper Functions Needed
```python
def calculate_record_highs_from_historical_data() -> Dict[str, float]:
    """Calculate record highs from historical intraday data"""
    # Scan through historical data to identify each new intraday record high
    # Return dict mapping date -> record_high_price

def is_near_record_high(current_price: float, current_date: str, tolerance: float = 10.0) -> bool:
    """Check if SPX is at record high or within tolerance"""
    # Compare current_price to latest record high

def is_first_record_high_in_3_months(current_date: str) -> bool:
    """Check if this is first record high in >3 months"""
    # Check if current price is new record high AND >3 months since last record high

def get_nearest_century_mark(price: float) -> float:
    """Get nearest century mark (6200, 6300, etc.)"""
    # return round(price / 100) * 100

def get_days_since_last_record_high(current_date: str) -> int:
    """Get number of days since last record high"""

def log_record_high_adjustment(trading_date: str, original_decision: str, 
                              original_target: float, adjusted_target: float,
                              adjustment_type: str, adjustment_reason: str,
                              execution_price: float, final_decision: str,
                              **kwargs):
    """Log adjustment to database for analysis"""
```

#### Core Logic Integration
Modify `apply_alternate_strategy_conversion()` method:

```python
if self.record_high_check:
    adjusted_target = decision_result.target_price
    adjustments_made = []
    
    # 1. Record High Adjustment (bulls only)
    if decision_result.decision in ["bull", "narrow_bull"]:
        if (is_near_record_high(execution_price) and 
            is_first_record_high_in_3_months(trading_date)):
            adjusted_target -= 5
            adjustments_made.append("record_high")
            
    # 2. Century Mark Adjustment (all strategies)
    nearest_century = get_nearest_century_mark(execution_price)
    if abs(execution_price - nearest_century) <= 14:
        if execution_price > nearest_century:
            adjusted_target -= 5  # Pull down towards century
        else:
            adjusted_target += 5  # Pull up towards century
        adjustments_made.append("century_mark")
    
    # 3. Re-evaluate decision based on adjusted target
    if adjustments_made:
        original_decision = decision_result.decision
        
        # Determine new decision based on adjusted target vs execution
        if adjusted_target < execution_price:
            new_decision = "narrow_bear" if "narrow" in original_decision else "bear"
        elif adjusted_target > execution_price:
            new_decision = "narrow_bull" if "narrow" in original_decision else "bull"
        else:
            new_decision = original_decision  # No change
            
        # Log adjustment to database
        for adj_type in adjustments_made:
            log_record_high_adjustment(
                trading_date=trading_date,
                original_decision=original_decision,
                original_target=decision_result.target_price,
                adjusted_target=adjusted_target,
                adjustment_type=adj_type,
                adjustment_reason=f"{adj_type} adjustment applied",
                execution_price=execution_price,
                final_decision=new_decision,
                spx_price=execution_price,
                nearest_century_mark=nearest_century
            )
        
        # Update decision result
        decision_result.decision = new_decision
        decision_result.target_price = adjusted_target
        decision_result.reasoning += f" → RECORD-HIGH-CHECK: {original_decision}→{new_decision} (target {decision_result.target_price}→{adjusted_target})"
```

## Data Requirements

### Record High Data Source
**RESOLVED:**
- **Source**: Will be calculated from historical data within the system
- **Definition**: **Intraday high** (not daily close)
- **Implementation**: Need to track historical intraday highs and identify record highs

### Example Data Structure Needed
```python
# Historical record highs with dates
RECORD_HIGHS = [
    {"date": "2024-01-15", "price": 4850.0},
    {"date": "2024-03-22", "price": 5200.0},
    {"date": "2024-07-10", "price": 5600.0},
    # ... more records
]
```

## Questions for Clarification

### Record High Logic
1. **Data Source**: ✅ **RESOLVED** - Calculate from historical intraday data
2. **Definition**: ✅ **RESOLVED** - **Intraday high**
3. **Tolerance**: Confirm $10 tolerance for "near record high"
4. **Time Period**: Exactly 90+ days or calendar months for "3 months"?

### Century Mark Logic
5. **Marks**: Confirm every $100 (6200, 6300, etc.)
6. **Threshold**: Confirm $14 distance threshold
7. **Pull Effect**: Always $5 regardless of distance within threshold?

### Decision Re-evaluation
8. **Narrow Preservation**: Do narrow designations persist after re-evaluation?
9. **SPAN3 Interaction**: Do narrow conversions still apply after adjustments?
10. **Dual Trades**: How does this interact with secondary iron butterflies?

### Integration
11. **Base Mode**: Confirm this extends `butterfly_conversion_latest` exactly
12. **Priority**: Which adjustments take precedence if both apply?
13. **Logging**: What level of detail needed in adjustment logs?

## Testing Strategy
- Create test cases for each adjustment type
- Test combination scenarios (both adjustments apply)
- Test decision re-evaluation logic
- Verify database logging functionality
- Test integration with existing dual trade logic

## Files to Modify
1. `historical_backtester.py` - Core logic and parameters
2. `database_output.py` - New table creation and logging
3. Command line argument parsing
4. Filename generation logic
5. Print summary display

## Success Criteria
- Adjustments properly triggered based on conditions
- Target prices correctly modified
- Decisions re-evaluated appropriately
- All adjustments logged to database for analysis
- Backward compatibility with existing modes maintained 