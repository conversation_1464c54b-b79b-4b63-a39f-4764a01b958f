{"test_name": "event_driven_with_delays", "timestamp": "2025-06-24T14:40:22.584676", "framework_state": {"events_log": ["EVENT: Bar received at 2025-06-23 15:50:30.200000", "EVENT: Processing first bar at 2025-06-23 15:50:30.200000", "EVENT: Bar received at 2025-06-23 15:51:00.500000", "EVENT: Processing second bar at 2025-06-23 15:51:00.500000", "EVENT: Bar received at 2025-06-23 15:51:30.150000", "EVENT: Processing third bar at 2025-06-23 15:51:30.150000", "EVENT: Scheduling execution for 2025-06-23 15:51:31.150000", "EVENT: Execution triggered at 2025-06-23 15:51:31.150000"], "execution_triggered": true, "execution_time": "2025-06-23T15:51:31.150000", "bars_processed": [{"bar_number": 1, "timestamp": "2025-06-23T15:50:30.200000", "method": "event_driven"}, {"bar_number": 2, "timestamp": "2025-06-23T15:51:00.500000", "method": "event_driven"}, {"bar_number": 3, "timestamp": "2025-06-23T15:51:30.150000", "method": "event_driven"}], "total_bars": 3, "execution_success": true}, "additional_data": {"start_time": "2025-06-23T15:50:00", "delays_ms": [200, 500, 150], "success": true}}