{"test_name": "configurable_reference_minute_55", "timestamp": "2025-06-26T10:34:16.840667", "framework_state": {"events_log": ["EVENT: Bar received at 2025-06-23 15:55:30", "EVENT: Processing first bar at 2025-06-23 15:55:30", "EVENT: Bar received at 2025-06-23 15:56:00", "EVENT: Processing second bar at 2025-06-23 15:56:00", "EVENT: Bar received at 2025-06-23 15:56:30", "EVENT: Processing third bar at 2025-06-23 15:56:30", "EVENT: Sc<PERSON>uling execution for 2025-06-23 15:56:31", "EVENT: Execution triggered at 2025-06-23 15:56:31"], "execution_triggered": true, "execution_time": "2025-06-23T15:56:31", "bars_processed": [{"bar_number": 1, "timestamp": "2025-06-23T15:55:30", "method": "event_driven"}, {"bar_number": 2, "timestamp": "2025-06-23T15:56:00", "method": "event_driven"}, {"bar_number": 3, "timestamp": "2025-06-23T15:56:30", "method": "event_driven"}], "total_bars": 3, "execution_success": true}, "additional_data": {"reference_minute": 55, "start_time": "2025-06-23T15:55:00", "success": true}}