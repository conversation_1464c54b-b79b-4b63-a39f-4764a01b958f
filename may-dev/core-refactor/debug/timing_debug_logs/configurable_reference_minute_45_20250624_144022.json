{"test_name": "configurable_reference_minute_45", "timestamp": "2025-06-24T14:40:22.583637", "framework_state": {"events_log": ["EVENT: Bar received at 2025-06-23 15:45:30", "EVENT: Processing first bar at 2025-06-23 15:45:30", "EVENT: Bar received at 2025-06-23 15:46:00", "EVENT: Processing second bar at 2025-06-23 15:46:00", "EVENT: Bar received at 2025-06-23 15:46:30", "EVENT: Processing third bar at 2025-06-23 15:46:30", "EVENT: Sc<PERSON>uling execution for 2025-06-23 15:46:31", "EVENT: Execution triggered at 2025-06-23 15:46:31"], "execution_triggered": true, "execution_time": "2025-06-23T15:46:31", "bars_processed": [{"bar_number": 1, "timestamp": "2025-06-23T15:45:30", "method": "event_driven"}, {"bar_number": 2, "timestamp": "2025-06-23T15:46:00", "method": "event_driven"}, {"bar_number": 3, "timestamp": "2025-06-23T15:46:30", "method": "event_driven"}], "total_bars": 3, "execution_success": true}, "additional_data": {"reference_minute": 45, "start_time": "2025-06-23T15:45:00", "success": true}}