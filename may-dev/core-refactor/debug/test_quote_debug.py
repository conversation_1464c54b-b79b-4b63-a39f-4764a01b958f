import sys
sys.path.append('core-refactor')
from quote_engine import OptionQuoteEngine, ComboQuoteRequest
from combo_orders import ComboOrderFactory
from ib_insync import IB
import time

# Connect to IB
ib = IB()
try:
    ib.connect('127.0.0.1', 4015, clientId=1, timeout=30)
    print("Connected to IB")
    
    # Wait for connection to stabilize and market data to be ready
    print("Waiting 3 seconds for market data feeds to stabilize...")
    time.sleep(3)
    
    # Initialize quote engine
    quote_engine = OptionQuoteEngine(ib)
    
    # Test getting wide iron butterfly quote
    center_strike = 6020.0
    print(f'Getting wide iron butterfly quote for strike {center_strike}...')
    
    # Method 1: Using quote engine
    try:
        quote_result = quote_engine.get_wide_butterfly_quote(center_strike)
        print(f'Quote engine result: {quote_result}')
        print(f'Type: {type(quote_result)}')
    except Exception as e:
        print(f'Quote engine error: {e}')
        import traceback
        traceback.print_exc()
    
    # Method 2: Using combo definition directly
    try:
        combo_def = ComboOrderFactory.create_wide_iron_butterfly(center_strike)
        quote_result = quote_engine.get_combo_quote(combo_def)
        print(f'Direct combo quote result: {quote_result}')
        print(f'Bid: {quote_result.bid}, Ask: {quote_result.ask}')
        print(f'Valid: {quote_result.is_valid}')
        print(f'Error: {quote_result.error_message}')
    except Exception as e:
        print(f'Direct combo error: {e}')
        import traceback
        traceback.print_exc()
    
    ib.disconnect()
    print("Disconnected from IB")
    
except Exception as e:
    print(f"Connection error: {e}")
    import traceback
    traceback.print_exc() 