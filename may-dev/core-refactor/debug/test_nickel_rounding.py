#!/usr/bin/env python3
"""
Comprehensive Test Script for Nickel Rounding Implementation
Tests all the major order types and scenarios
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from combo_orders import OptionPricingUtils, ComboOrderFactory
from config import TradingConfig, set_global_config

def test_basic_nickel_rounding():
    """Test basic nickel rounding functions"""
    print("🧪 Testing Basic Nickel Rounding Functions...")
    
    test_cases = [
        # (input_price, round_up, expected_output)
        (2.17, True, 2.20),   # Round up
        (2.17, False, 2.15),  # Round down
        (2.22, True, 2.25),   # Round up
        (2.22, False, 2.20),  # Round down
        (2.25, True, 2.25),   # Already nickel
        (2.25, False, 2.25),  # Already nickel
        (1.05, True, 1.05),   # Already nickel
        (0.25, True, 0.25),   # Already nickel
    ]
    
    all_passed = True
    for input_price, round_up, expected in test_cases:
        result = OptionPricingUtils.round_to_nickel(input_price, round_up)
        passed = abs(result - expected) < 0.001
        status = "✅ PASS" if passed else "❌ FAIL"
        direction = "UP" if round_up else "DOWN"
        print(f"  {status} ${input_price:.2f} round {direction} → ${result:.2f} (expected ${expected:.2f})")
        if not passed:
            all_passed = False
    
    return all_passed

def test_price_validation():
    """Test price validation function"""
    print("\n🧪 Testing Price Validation...")
    
    valid_prices = [0.25, 1.05, 2.20, 2.25, 3.40, 6.50]
    invalid_prices = [2.17, 2.22, 3.33, 6.77, 1.03]
    
    all_passed = True
    
    print("  Valid prices:")
    for price in valid_prices:
        is_valid = OptionPricingUtils.validate_nickel_price(price)
        status = "✅ PASS" if is_valid else "❌ FAIL"
        print(f"    {status} ${price:.2f}")
        if not is_valid:
            all_passed = False
    
    print("  Invalid prices:")
    for price in invalid_prices:
        is_valid = OptionPricingUtils.validate_nickel_price(price)
        status = "✅ PASS" if not is_valid else "❌ FAIL"
        print(f"    {status} ${price:.2f} (should be invalid)")
        if is_valid:
            all_passed = False
    
    return all_passed

def test_buffer_calculation():
    """Test the calculate_limit_price_with_buffer function"""
    print("\n🧪 Testing Buffer Calculation with Nickel Rounding...")
    
    test_cases = [
        # (mid_price, is_credit, buffer, expected_approx, description)
        (2.17, False, 0.05, 2.25, "Bull spread debit - round up"),
        (1.33, False, 0.05, 1.40, "Bear spread debit - round up"),
        (-4.22, True, 0.05, -4.15, "Iron butterfly credit - round down"),
        (-6.77, True, 0.05, -6.70, "Wide iron butterfly credit - round down"),
        (2.20, False, 0.05, 2.25, "Already close to nickel - debit"),
        (-3.40, True, 0.05, -3.35, "Already close to nickel - credit"),
    ]
    
    all_passed = True
    for mid_price, is_credit, buffer, expected, description in test_cases:
        result = OptionPricingUtils.calculate_limit_price_with_buffer(
            mid_price, is_credit, buffer
        )
        passed = abs(result - expected) < 0.01
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"  {status} {description}")
        print(f"        Mid: ${mid_price:.2f} → Result: ${result:.2f} (expected ~${expected:.2f})")
        
        # Also validate the result is a nickel
        is_nickel = OptionPricingUtils.validate_nickel_price(result)
        if not is_nickel:
            print(f"        ❌ Result is not a nickel multiple!")
            all_passed = False
        
        if not passed:
            all_passed = False
    
    return all_passed

def test_config_prices():
    """Test that all config prices are nickel multiples"""
    print("\n🧪 Testing Configuration Prices...")
    
    config = TradingConfig()
    
    prices_to_test = [
        (config.pricing.spread_price_limit, "spread_price_limit"),
        (config.pricing.narrow_spread_limit, "narrow_spread_limit"),
        (config.pricing.narrow_iron_butterfly_credit_min, "narrow_iron_butterfly_credit_min"),
        (config.pricing.wide_iron_butterfly_credit_min, "wide_iron_butterfly_credit_min"),
        (config.pricing.overshoot_reversal_limit, "overshoot_reversal_limit"),
        (config.pricing.narrow_iron_butterfly_test_credit, "narrow_iron_butterfly_test_credit"),
        (config.pricing.wide_iron_butterfly_test_credit, "wide_iron_butterfly_test_credit"),
        (config.pricing.wide_iron_butterfly_fallback_min, "wide_iron_butterfly_fallback_min"),
        (config.live_test.test_order_price, "test_order_price"),
    ]
    
    all_passed = True
    for price, name in prices_to_test:
        is_valid = OptionPricingUtils.validate_nickel_price(price)
        status = "✅ PASS" if is_valid else "❌ FAIL"
        print(f"  {status} {name}: ${price:.2f}")
        if not is_valid:
            all_passed = False
    
    return all_passed

def test_real_scenarios():
    """Test with realistic market scenarios"""
    print("\n🧪 Testing Real Market Scenarios...")
    
    # Set up config
    config = TradingConfig()
    set_global_config(config)
    
    scenarios = [
        {
            "name": "SPX @ 6000 - Bull Spread",
            "mid_price": 2.17,
            "strategy": "bull_spread",
            "is_credit": False,
            "expected_min": 2.20,
            "expected_max": 2.30
        },
        {
            "name": "SPX @ 6000 - Iron Butterfly",
            "mid_price": -4.22,
            "strategy": "iron_butterfly", 
            "is_credit": True,
            "expected_min": -4.20,
            "expected_max": -4.10
        },
        {
            "name": "SPX @ 6000 - Wide Iron Butterfly",
            "mid_price": -6.77,
            "strategy": "wide_iron_butterfly",
            "is_credit": True,
            "expected_min": -6.75,
            "expected_max": -6.65
        },
    ]
    
    all_passed = True
    for scenario in scenarios:
        print(f"\n  📊 {scenario['name']}")
        
        # Calculate the limit price
        limit_price = OptionPricingUtils.calculate_limit_price_with_buffer(
            scenario['mid_price'], 
            scenario['is_credit'], 
            buffer=0.05
        )
        
        # Validate it's a nickel
        is_nickel = OptionPricingUtils.validate_nickel_price(limit_price)
        
        # Check if it's in expected range
        in_range = (scenario['expected_min'] <= limit_price <= scenario['expected_max'])
        
        status = "✅ PASS" if is_nickel and in_range else "❌ FAIL"
        print(f"    {status} Mid: ${scenario['mid_price']:.2f} → Limit: ${limit_price:.2f}")
        print(f"         Nickel: {is_nickel}, In Range: {in_range}")
        
        if scenario['is_credit']:
            print(f"         Credit Received: ${abs(limit_price):.2f}")
        
        if not (is_nickel and in_range):
            all_passed = False
    
    return all_passed

def run_all_tests():
    """Run all tests and report results"""
    print("🚀 RUNNING COMPREHENSIVE NICKEL ROUNDING TESTS\n")
    print("=" * 60)
    
    tests = [
        ("Basic Nickel Rounding", test_basic_nickel_rounding),
        ("Price Validation", test_price_validation),
        ("Buffer Calculation", test_buffer_calculation),
        ("Config Prices", test_config_prices),
        ("Real Scenarios", test_real_scenarios),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            passed = test_func()
            results.append((test_name, passed, None))
        except Exception as e:
            results.append((test_name, False, str(e)))
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for _, passed, _ in results if passed)
    
    for test_name, passed, error in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if error:
            print(f"      Error: {error}")
    
    print(f"\n📊 Overall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! Nickel rounding is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1) 