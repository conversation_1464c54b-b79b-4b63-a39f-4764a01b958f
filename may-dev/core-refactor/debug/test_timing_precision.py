#!/usr/bin/env python3
"""
Debug Script: Test Main Loop Timing Precision
Tests the timing accuracy of the main trading loop to identify bottlenecks
"""

import time
import datetime as dt
import logging
from typing import Dict, List
import statistics

# Mock classes to simulate the heavy operations
class MockIBConnection:
    def sleep(self, duration):
        time.sleep(0.001)  # Simulate IB processing

class MockQuoteManager:
    def update_quotes(self):
        time.sleep(0.05)  # Simulate quote updates

class MockConsoleUI:
    def update_strategy_state(self, state):
        time.sleep(0.01)  # Simulate state update
    
    def display_all(self, db_connected, db_type):
        time.sleep(0.1)  # Simulate console display

class MockMarketDataUpdater:
    def update_market_data(self):
        time.sleep(0.02)  # Simulate market data update

class TimingDebugger:
    """Debug the main loop timing precision"""
    
    def __init__(self):
        self.loop_times = []
        self.execution_checks = []
        self.missed_windows = []
        
        # Target timing windows to test
        self.target_seconds = [30, 31]  # Test both bar analysis and execution
        
        # Mock components
        self.ib_data = MockIBConnection()
        self.live_quote_manager = MockQuoteManager()
        self.console_ui = MockConsoleUI()
        self.market_updater = MockMarketDataUpdater()
    
    def time_operation(self, operation_name: str, operation_func):
        """Time how long an operation takes"""
        start = time.perf_counter()
        operation_func()
        end = time.perf_counter()
        duration = (end - start) * 1000  # Convert to milliseconds
        return duration
    
    def simulate_main_loop_cycle(self) -> Dict[str, float]:
        """Simulate one cycle of the main loop and measure timings"""
        cycle_start = time.perf_counter()
        timings = {}
        
        # Time each operation in the main loop
        timings['ib_sleep'] = self.time_operation("IB Sleep", lambda: self.ib_data.sleep(0))
        timings['market_data'] = self.time_operation("Market Data", self.market_updater.update_market_data)
        timings['quote_updates'] = self.time_operation("Quote Updates", self.live_quote_manager.update_quotes)
        
        # Timing check (this is instantaneous)
        timing_start = time.perf_counter()
        current_time = dt.datetime.now()
        self.check_algorithm_timing(current_time)
        timings['timing_check'] = (time.perf_counter() - timing_start) * 1000
        
        timings['ui_state_update'] = self.time_operation("UI State Update", lambda: self.console_ui.update_strategy_state(None))
        timings['ui_display'] = self.time_operation("UI Display", lambda: self.console_ui.display_all(True, "Mock"))
        
        # The sleep
        sleep_start = time.perf_counter()
        time.sleep(1.0)
        timings['sleep'] = (time.perf_counter() - sleep_start) * 1000
        
        cycle_end = time.perf_counter()
        timings['total_cycle'] = (cycle_end - cycle_start) * 1000
        
        return timings
    
    def check_algorithm_timing(self, current_time: dt.datetime):
        """Simulate the timing check logic"""
        current_minute = current_time.minute
        current_second = current_time.second
        
        # Check if we hit target timing windows
        if current_second in self.target_seconds:
            self.execution_checks.append({
                'time': current_time,
                'minute': current_minute,
                'second': current_second,
                'hit_target': True
            })
            print(f"✅ HIT TARGET: {current_time.strftime('%H:%M:%S.%f')[:-3]}")
        
        # Track near misses (within 1 second of target)
        for target_second in self.target_seconds:
            if abs(current_second - target_second) == 1:
                self.missed_windows.append({
                    'time': current_time,
                    'target_second': target_second,
                    'actual_second': current_second,
                    'drift': current_second - target_second
                })
                print(f"⚠️ NEAR MISS: Target {target_second}s, got {current_second}s at {current_time.strftime('%H:%M:%S.%f')[:-3]}")
    
    def run_timing_test(self, duration_minutes: int = 2):
        """Run the timing test for specified duration"""
        print(f"🔬 Starting timing test for {duration_minutes} minutes...")
        print(f"📋 Watching for target seconds: {self.target_seconds}")
        print(f"⏰ Start time: {dt.datetime.now().strftime('%H:%M:%S')}")
        print("-" * 60)
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        cycle_count = 0
        
        while time.time() < end_time:
            cycle_timings = self.simulate_main_loop_cycle()
            self.loop_times.append(cycle_timings)
            cycle_count += 1
            
            # Log every 10 cycles
            if cycle_count % 10 == 0:
                avg_total = statistics.mean([t['total_cycle'] for t in self.loop_times[-10:]])
                print(f"📊 Cycle {cycle_count}: Avg loop time: {avg_total:.1f}ms")
        
        print("-" * 60)
        print(f"✅ Test completed after {cycle_count} cycles")
        self.analyze_results()
    
    def analyze_results(self):
        """Analyze the timing test results"""
        if not self.loop_times:
            print("❌ No timing data collected")
            return
        
        print("\n📊 TIMING ANALYSIS RESULTS")
        print("=" * 50)
        
        # Overall loop statistics
        total_times = [t['total_cycle'] for t in self.loop_times]
        print(f"🔄 Loop Timing Statistics:")
        print(f"   Total cycles: {len(total_times)}")
        print(f"   Average cycle time: {statistics.mean(total_times):.1f}ms")
        print(f"   Min cycle time: {min(total_times):.1f}ms")
        print(f"   Max cycle time: {max(total_times):.1f}ms")
        print(f"   Std deviation: {statistics.stdev(total_times):.1f}ms")
        
        # Check for cycles that took significantly longer than 1000ms
        slow_cycles = [t for t in total_times if t > 1100]  # More than 100ms over expected
        if slow_cycles:
            print(f"⚠️  Slow cycles (>1100ms): {len(slow_cycles)} ({len(slow_cycles)/len(total_times)*100:.1f}%)")
            print(f"   Slowest cycle: {max(slow_cycles):.1f}ms")
        
        # Component timing breakdown
        print(f"\n⚙️ Component Timing Breakdown (averages):")
        components = ['ib_sleep', 'market_data', 'quote_updates', 'timing_check', 'ui_state_update', 'ui_display', 'sleep']
        for component in components:
            avg_time = statistics.mean([t[component] for t in self.loop_times])
            print(f"   {component.replace('_', ' ').title()}: {avg_time:.1f}ms")
        
        # Execution timing hits
        print(f"\n🎯 Execution Timing Hits:")
        print(f"   Target windows checked: {len(self.execution_checks)}")
        print(f"   Successful hits: {len([c for c in self.execution_checks if c['hit_target']])}")
        
        if self.execution_checks:
            print(f"   Hit times:")
            for check in self.execution_checks[-5:]:  # Show last 5
                print(f"      {check['time'].strftime('%H:%M:%S')} (second {check['second']})")
        
        # Near misses
        if self.missed_windows:
            print(f"\n⚠️ Near Misses ({len(self.missed_windows)}):")
            for miss in self.missed_windows[-5:]:  # Show last 5
                print(f"      Target {miss['target_second']}s → {miss['actual_second']}s (drift: {miss['drift']:+d}s)")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        
        avg_total = statistics.mean(total_times)
        if avg_total > 1050:
            print(f"   ⚠️ Average cycle time ({avg_total:.1f}ms) > 1050ms - timing drift likely")
            print(f"      Consider optimizing slow components")
        
        # Find slowest component
        component_avgs = {comp: statistics.mean([t[comp] for t in self.loop_times]) for comp in components}
        slowest_component = max(component_avgs.keys(), key=lambda k: component_avgs[k])
        print(f"   🐌 Slowest component: {slowest_component.replace('_', ' ').title()} ({component_avgs[slowest_component]:.1f}ms)")
        
        if component_avgs['ui_display'] > 50:
            print(f"   💡 UI display is slow - consider reducing update frequency")
        
        if len(slow_cycles) > len(total_times) * 0.1:  # More than 10% slow cycles
            print(f"   ⚠️ Too many slow cycles - investigate system load")
        
        print(f"\n🔧 TIMING PRECISION ASSESSMENT:")
        expected_fps = 1000 / statistics.mean(total_times)
        print(f"   Effective loop frequency: {expected_fps:.1f} Hz")
        print(f"   Timing precision: ±{statistics.stdev(total_times)/2:.0f}ms")
        
        if statistics.stdev(total_times) > 50:
            print(f"   ❌ High timing variance - execution windows may be missed")
        else:
            print(f"   ✅ Acceptable timing variance")


def main():
    """Run the timing debug test"""
    print("🔬 SPX Trading Loop Timing Debugger")
    print("=" * 50)
    
    debugger = TimingDebugger()
    
    # Test for 2 minutes to catch timing windows
    debugger.run_timing_test(duration_minutes=2)


if __name__ == "__main__":
    main() 