#!/usr/bin/env python3
"""
Test script to verify iron butterfly target price fix
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backtesting'))

from historical_backtester import HistoricalBacktester
from decision_engine import DecisionR<PERSON>ult

def test_ibf_target_price_fix():
    """Test the iron butterfly target price fix for dual trades"""
    
    print("=== Testing Iron Butterfly Target Price Fix ===\n")
    
    # Create backtester with butterfly_conversion_latest enabled
    backtester = HistoricalBacktester(butterfly_conversion_latest=True)
    
    # Test Case 1: 6/6 - narrow bear with target 6005, execution 6002.69
    # Target is worse (higher) than execution, should adjust to appropriate IBF strike
    print("Test 1: 6/6 case - narrow bear target 6005, execution 6002.69")
    print("Expected: IBF target should align with IBF strike below execution")
    
    primary_decision = DecisionResult(
        decision="narrow_bear",
        strikes=[6005, 6000],  # Bear spread: Buy 6005P, Sell 6000P (target = short strike = 6000)
        option_types=['P', 'P'],
        actions=['BUY', 'SELL'],
        target_price=6000.0,  # Target is short strike (6000)
        reasoning="narrow_bear from conversion",
        bias=0,
        spans={'span1': 5.0, 'span2': 6.0, 'span3': 3.5},
        close_percentages=[25.0, 30.0, 28.0]
    )
    
    execution_price = 6002.69
    secondary_result = backtester.create_secondary_iron_butterfly(primary_decision, -1, execution_price)
    
    print(f"  Primary target: {primary_decision.target_price}")
    print(f"  Execution price: {execution_price}")
    print(f"  IBF strikes: {secondary_result.strikes}")
    print(f"  IBF target: {secondary_result.target_price}")
    
    # Verify target aligns with an IBF strike
    assert secondary_result.target_price in secondary_result.strikes, f"IBF target {secondary_result.target_price} should be one of the IBF strikes {secondary_result.strikes}"
    
    # For narrow bear with target worse than execution, should choose IBF strike below execution
    ibf_strikes_below_execution = [s for s in secondary_result.strikes if s < execution_price]
    if ibf_strikes_below_execution:
        expected_target = max(ibf_strikes_below_execution)
        assert secondary_result.target_price == expected_target, f"Expected {expected_target}, got {secondary_result.target_price}"
    
    print("  ✅ PASSED: IBF target aligns with IBF strike and is below execution\n")
    
    # Test Case 2: 5/9 - narrow bull with target 5660, execution 5662.50
    # Target is worse (lower) than execution, should adjust to appropriate IBF strike
    print("Test 2: 5/9 case - narrow bull target 5660, execution 5662.50")
    print("Expected: IBF target should align with IBF strike above execution")
    
    primary_decision_2 = DecisionResult(
        decision="narrow_bull",
        strikes=[5655, 5660],  # Bull spread: Buy 5655C, Sell 5660C (target = short strike = 5660)
        option_types=['C', 'C'],
        actions=['BUY', 'SELL'],
        target_price=5660.0,  # Target is short strike (5660)
        reasoning="narrow_bull from conversion",
        bias=0,
        spans={'span1': 5.0, 'span2': 6.0, 'span3': 3.5},
        close_percentages=[85.0, 80.0, 82.0]
    )
    
    execution_price_2 = 5662.50
    secondary_result_2 = backtester.create_secondary_iron_butterfly(primary_decision_2, 1, execution_price_2)
    
    print(f"  Primary target: {primary_decision_2.target_price}")
    print(f"  Execution price: {execution_price_2}")
    print(f"  IBF strikes: {secondary_result_2.strikes}")
    print(f"  IBF target: {secondary_result_2.target_price}")
    
    # Verify target aligns with an IBF strike
    assert secondary_result_2.target_price in secondary_result_2.strikes, f"IBF target {secondary_result_2.target_price} should be one of the IBF strikes {secondary_result_2.strikes}"
    
    # For narrow bull with target worse than execution, should choose IBF strike above execution
    ibf_strikes_above_execution = [s for s in secondary_result_2.strikes if s > execution_price_2]
    if ibf_strikes_above_execution:
        expected_target_2 = min(ibf_strikes_above_execution)
        assert secondary_result_2.target_price == expected_target_2, f"Expected {expected_target_2}, got {secondary_result_2.target_price}"
    
    print("  ✅ PASSED: IBF target aligns with IBF strike and is above execution\n")
    
    # Test Case 3: Good narrow bear case - target already good, should align with closest IBF strike
    print("Test 3: Good narrow bear case - target 5985, execution 5988.50")
    print("Expected: IBF target should align with closest IBF strike to narrow target")
    
    primary_decision_3 = DecisionResult(
        decision="narrow_bear",
        strikes=[5990, 5985],  # Bear spread: Buy 5990P, Sell 5985P (target = short strike = 5985)
        option_types=['P', 'P'],
        actions=['BUY', 'SELL'],
        target_price=5985.0,  # Target is short strike (5985) - good for bear (below execution)
        reasoning="narrow_bear from conversion",
        bias=0,
        spans={'span1': 5.0, 'span2': 6.0, 'span3': 3.5},
        close_percentages=[25.0, 30.0, 28.0]
    )
    
    execution_price_3 = 5988.50
    secondary_result_3 = backtester.create_secondary_iron_butterfly(primary_decision_3, -1, execution_price_3)
    
    print(f"  Primary target: {primary_decision_3.target_price}")
    print(f"  Execution price: {execution_price_3}")
    print(f"  IBF strikes: {secondary_result_3.strikes}")
    print(f"  IBF target: {secondary_result_3.target_price}")
    
    # Verify target aligns with an IBF strike
    assert secondary_result_3.target_price in secondary_result_3.strikes, f"IBF target {secondary_result_3.target_price} should be one of the IBF strikes {secondary_result_3.strikes}"
    
    # Should be the IBF strike closest to the narrow target (5985)
    closest_strike = min(secondary_result_3.strikes, key=lambda x: abs(x - primary_decision_3.target_price))
    assert secondary_result_3.target_price == closest_strike, f"Expected {closest_strike} (closest to {primary_decision_3.target_price}), got {secondary_result_3.target_price}"
    
    print("  ✅ PASSED: IBF target aligns with closest IBF strike to narrow target\n")
    
    # Test Case 4: Good narrow bull case - target already good, should align with closest IBF strike
    print("Test 4: Good narrow bull case - target 5665, execution 5662.50")
    print("Expected: IBF target should align with closest IBF strike to narrow target")
    
    primary_decision_4 = DecisionResult(
        decision="narrow_bull",
        strikes=[5660, 5665],  # Bull spread: Buy 5660C, Sell 5665C (target = short strike = 5665)
        option_types=['C', 'C'],
        actions=['BUY', 'SELL'],
        target_price=5665.0,  # Target is short strike (5665) - good for bull (above execution)
        reasoning="narrow_bull from conversion",
        bias=0,
        spans={'span1': 5.0, 'span2': 6.0, 'span3': 3.5},
        close_percentages=[85.0, 80.0, 82.0]
    )
    
    execution_price_4 = 5662.50
    secondary_result_4 = backtester.create_secondary_iron_butterfly(primary_decision_4, 1, execution_price_4)
    
    print(f"  Primary target: {primary_decision_4.target_price}")
    print(f"  Execution price: {execution_price_4}")
    print(f"  IBF strikes: {secondary_result_4.strikes}")
    print(f"  IBF target: {secondary_result_4.target_price}")
    
    # Verify target aligns with an IBF strike
    assert secondary_result_4.target_price in secondary_result_4.strikes, f"IBF target {secondary_result_4.target_price} should be one of the IBF strikes {secondary_result_4.strikes}"
    
    # Should be the IBF strike closest to the narrow target (5665)
    closest_strike_4 = min(secondary_result_4.strikes, key=lambda x: abs(x - primary_decision_4.target_price))
    assert secondary_result_4.target_price == closest_strike_4, f"Expected {closest_strike_4} (closest to {primary_decision_4.target_price}), got {secondary_result_4.target_price}"
    
    print("  ✅ PASSED: IBF target aligns with closest IBF strike to narrow target\n")
    
    # Test Case 5: Print IBF structure for verification
    print("Test 5: IBF structure verification")
    test_execution_price = 6000.0
    test_bias = 0
    
    from decision_engine import determine_iron_butterfly_strike, get_strategy_strikes_and_types
    center_strike = determine_iron_butterfly_strike(test_execution_price, test_bias)
    strategy_info = get_strategy_strikes_and_types("iron_butterfly", center_strike, test_execution_price)
    
    print(f"  Execution price: {test_execution_price}")
    print(f"  Bias: {test_bias}")
    print(f"  IBF center strike: {center_strike}")
    print(f"  IBF strikes: {strategy_info['strikes']}")
    print(f"  IBF structure: [put_wing, center_put, center_call, call_wing]")
    print(f"  Available target options: {strategy_info['strikes'][0]} (put wing), {strategy_info['strikes'][1]} (center), {strategy_info['strikes'][3]} (call wing)")
    
    print("\n🎉 All tests passed! Iron butterfly target price fix is working correctly.")
    print("✅ IBF target prices now align with actual IBF strikes")
    print("✅ Target price adjustments respect profit direction for narrow strategies")

if __name__ == "__main__":
    test_ibf_target_price_fix() 