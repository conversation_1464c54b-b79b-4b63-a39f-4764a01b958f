#!/usr/bin/env python3
"""
Test script to verify strategy-specific profit/loss breakdown functionality
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backtesting'))

from historical_backtester import HistoricalBacktester
from database_output import BacktestDatabaseOutput
import j<PERSON>

def test_strategy_pnl_breakdown():
    """Test the new strategy-specific P&L breakdown functionality"""
    
    print("=== Testing Strategy-Specific P&L Breakdown ===\n")
    
    # Create backtester with a small date range for testing
    backtester = HistoricalBacktester(
        start_date="2025-01-02",
        end_date="2025-01-10",  # Small range for quick test
        simple_mode=True,
        simple_spread_cost=2.20,
        simple_butterfly_credit=3.50,
        simple_itm_threshold=2.50,
        starting_balance=10000.0,
        max_risk_per_trade_pc=0.05
    )
    
    print("Running backtest...")
    results = backtester.run_backtest()
    
    if not results:
        print("No results generated. Check date range and data availability.")
        return
    
    print(f"Generated {len(results)} results")
    
    # Generate summary statistics
    print("\nGenerating summary statistics...")
    summary = backtester.generate_summary_stats()
    
    # Test 1: Verify decision_profit_loss_stats exists
    print("\n--- Test 1: Checking decision_profit_loss_stats ---")
    if 'decision_profit_loss_stats' in summary:
        print("✓ decision_profit_loss_stats found in summary")
        pnl_stats = summary['decision_profit_loss_stats']
        
        for decision, stats in pnl_stats.items():
            print(f"\n{decision}:")
            print(f"  Total Trades: {stats['total_trades']}")
            print(f"  Winning Trades: {stats['winning_trades']}")
            print(f"  Losing Trades: {stats['losing_trades']}")
            print(f"  Avg Win Amount: ${stats['avg_win_amount']:.2f}")
            print(f"  Avg Loss Amount: ${stats['avg_loss_amount']:.2f}")
            print(f"  Avg Win %: {stats['avg_win_pct']:.1f}%")
            print(f"  Avg Loss %: {stats['avg_loss_pct']:.1f}%")
    else:
        print("✗ decision_profit_loss_stats NOT found in summary")
        return
    
    # Test 2: Test print_summary output
    print("\n--- Test 2: Testing Enhanced Print Summary ---")
    print("Expected output should show Average Win/Loss for each strategy:")
    backtester.print_summary()
    
    # Test 3: Test database output enhancement
    print("\n--- Test 3: Testing Enhanced Database Output ---")
    try:
        db_output = BacktestDatabaseOutput()
        
        # Test the enhanced decision_win_rates JSON creation
        decision_win_rates = summary.get('decision_win_rates', {})
        decision_pnl_stats = summary.get('decision_profit_loss_stats', {})
        
        enhanced_decision_win_rates = {}
        for decision in decision_win_rates.keys():
            enhanced_decision_win_rates[decision] = {
                'win_rate_pct': decision_win_rates[decision],
                'avg_win_amount': decision_pnl_stats.get(decision, {}).get('avg_win_amount', 0),
                'avg_loss_amount': decision_pnl_stats.get(decision, {}).get('avg_loss_amount', 0),
                'avg_win_pct': decision_pnl_stats.get(decision, {}).get('avg_win_pct', 0),
                'avg_loss_pct': decision_pnl_stats.get(decision, {}).get('avg_loss_pct', 0),
                'total_trades': decision_pnl_stats.get(decision, {}).get('total_trades', 0),
                'winning_trades': decision_pnl_stats.get(decision, {}).get('winning_trades', 0),
                'losing_trades': decision_pnl_stats.get(decision, {}).get('losing_trades', 0)
            }
        
        enhanced_json = json.dumps(enhanced_decision_win_rates, indent=2)
        print("✓ Enhanced decision_win_rates JSON created successfully:")
        print(enhanced_json)
        
        # Optionally save to database if connection is available
        try:
            run_id = db_output.save_backtest_results(backtester, results, "Strategy P&L Breakdown Test")
            print(f"✓ Successfully saved to database with run_id: {run_id}")
        except Exception as e:
            print(f"Database save skipped (connection issue): {e}")
        
    except Exception as e:
        print(f"✗ Database output test failed: {e}")
    
    # Test 4: Verify calculations are correct
    print("\n--- Test 4: Verifying Calculation Accuracy ---")
    actual_trades = [r for r in results if r.num_contracts > 0]
    
    for decision in summary['decision_counts'].keys():
        decision_results = [r for r in actual_trades if r.decision == decision]
        if not decision_results:
            continue
            
        winning_results = [r for r in decision_results if r.trade_successful]
        losing_results = [r for r in decision_results if not r.trade_successful]
        
        # Manual calculation
        manual_avg_win = sum(r.profit_loss for r in winning_results) / len(winning_results) if winning_results else 0
        manual_avg_loss = sum(r.profit_loss for r in losing_results) / len(losing_results) if losing_results else 0
        
        # Compare with our calculated values
        stats = summary['decision_profit_loss_stats'][decision]
        calculated_avg_win = stats['avg_win_amount']
        calculated_avg_loss = stats['avg_loss_amount']
        
        print(f"\n{decision} Verification:")
        print(f"  Manual Avg Win: ${manual_avg_win:.2f} vs Calculated: ${calculated_avg_win:.2f}")
        print(f"  Manual Avg Loss: ${manual_avg_loss:.2f} vs Calculated: ${calculated_avg_loss:.2f}")
        
        if abs(manual_avg_win - calculated_avg_win) < 0.01 and abs(manual_avg_loss - calculated_avg_loss) < 0.01:
            print(f"  ✓ {decision} calculations are accurate")
        else:
            print(f"  ✗ {decision} calculations are INCORRECT")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    test_strategy_pnl_breakdown() 