#!/usr/bin/env python3
import sys
sys.path.append('core-refactor')
from quote_engine import OptionQuoteEngine
from combo_orders import ComboOrderFactory
from ib_insync import IB
import time

# Connect to IB
ib = IB()
try:
    ib.connect('127.0.0.1', 4015, clientId=1, timeout=30)
    print("Connected to IB")
    
    # Wait for connection to stabilize
    print("Waiting 3 seconds for market data feeds to stabilize...")
    time.sleep(3)
    
    # Initialize quote engine
    quote_engine = OptionQuoteEngine(ib)
    
    # Test the new simplified approach
    center_strike = 6015.0
    print(f'Testing simplified approach for strike {center_strike}...\n')
    
    # Create combo definition and get quote directly
    combo_def = ComboOrderFactory.create_wide_iron_butterfly(center_strike)
    quote_result = quote_engine.get_combo_quote(combo_def)
    
    print(f"Quote result valid: {quote_result.is_valid}")
    print(f"Quote result bid: {quote_result.bid}")
    print(f"Quote result ask: {quote_result.ask}")
    print(f"Quote result error: {quote_result.error_message}")
    
    # Extract credit the same way the live script will
    wide_bf_quote = 0.0
    if quote_result.is_valid and quote_result.bid is not None:
        wide_bf_quote = abs(quote_result.bid)
    
    print(f"\nExtracted credit: ${wide_bf_quote:.2f}")
    print(f"Meets $6.00 threshold: {wide_bf_quote >= 6.00}")
    
    ib.disconnect()
    print("Disconnected from IB")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc() 