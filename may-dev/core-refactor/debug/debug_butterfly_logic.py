#!/usr/bin/env python3
import sys
sys.path.append('core-refactor')
from quote_engine import OptionQuoteEngine
from combo_orders import ComboOrderFactory
from ib_insync import IB
import datetime as dt
import time

# Connect to IB
ib = IB()
try:
    ib.connect('127.0.0.1', 4015, clientId=1, timeout=30)
    print("Connected to IB")
    
    # Wait for connection to stabilize 
    print("Waiting 3 seconds for market data feeds to stabilize...")
    time.sleep(3)
    
    # Initialize quote engine
    quote_engine = OptionQuoteEngine(ib)
    
    # Test the exact logic from get_butterfly_quote
    center_strike = 6010.0
    print(f'Testing logic for strike {center_strike}...\n')
    
    # Create combo
    combo_def = ComboOrderFactory.create_wide_iron_butterfly(center_strike)
    expiry_date = dt.datetime.now().strftime("%Y%m%d")
    quote_result = quote_engine.get_combo_quote(combo_def, expiry_date)
    
    print(f"1. quote_result.is_valid: {quote_result.is_valid}")
    print(f"2. quote_result.bid: {quote_result.bid} (type: {type(quote_result.bid)})")
    print(f"3. quote_result.ask: {quote_result.ask} (type: {type(quote_result.ask)})")
    print(f"4. quote_result.error_message: {quote_result.error_message}")
    
    if quote_result.is_valid:
        print("\n✅ Quote is valid, checking conditions...")
        
        if quote_result.bid is not None:
            print(f"   Bid is not None: ✅ ({quote_result.bid})")
            is_nan = quote_result.bid != quote_result.bid
            print(f"   Bid is NaN check (bid != bid): {is_nan}")
            not_nan = not is_nan
            print(f"   NOT NaN: {not_nan}")
            
            if not_nan:
                credit = abs(quote_result.bid)
                print(f"   ✅ Would return credit: {credit}")
            else:
                print(f"   ❌ Bid is NaN, checking ask...")
        else:
            print("   Bid is None")
            
        if quote_result.ask is not None:
            print(f"   Ask is not None: ✅ ({quote_result.ask})")
            is_nan = quote_result.ask != quote_result.ask  
            print(f"   Ask is NaN check (ask != ask): {is_nan}")
            not_nan = not is_nan
            print(f"   NOT NaN: {not_nan}")
            
            if not_nan:
                credit = abs(quote_result.ask)
                print(f"   ✅ Would return credit: {credit}")
            else:
                print(f"   ❌ Ask is NaN")
        else:
            print("   Ask is None")
            
    # Now test the actual method
    print(f"\n--- Actual get_wide_butterfly_quote() result ---")
    actual_result = quote_engine.get_wide_butterfly_quote(center_strike)
    print(f"Result: {actual_result}")
    
    ib.disconnect()
    print("Disconnected from IB")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc() 