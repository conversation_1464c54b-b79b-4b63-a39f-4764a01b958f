#!/usr/bin/env python3
"""
Verification script for <PERSON> account setup
"""

from ib_insync import IB
import sys

def verify_jerry_setup():
    """Verify <PERSON> account setup"""
    
    try:
        print("🔍 Verifying <PERSON> Account Setup...")
        ib = IB()
        ib.connect('127.0.0.1', 4017, clientId=400)
        
        print("✅ Connected successfully!")
        
        # Get account info
        accounts = ib.managedAccounts()
        print(f"📋 Available accounts: {accounts}")
        
        # Get account summary
        account_summary = ib.accountSummary()
        
        # Get account number
        account_number = None
        if account_summary:
            account_number = account_summary[0].account
            print(f"📊 Account Number: {account_number}")
        
        # Get balance
        balance = None
        for item in account_summary:
            if item.tag == 'NetLiquidation' and item.currency == 'USD':
                balance = float(item.value)
                break
        
        if balance:
            print(f"💰 Balance: ${balance:,.2f}")
            
            # Verify this is the correct account
            if account_number == 'U15269296':
                print(f"✅ Correct account (U15269296) - Ready for trading!")
                print(f"✅ This account will be used for live trading")
                print(f"✅ Trades will be recorded to database as 'jerryzhang'")
            else:
                print(f"⚠️  Wrong account - expected U15269296, got {account_number}")
        else:
            print(f"❌ Could not get balance")
        
        ib.disconnect()
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    print(f"\n💡 Setup Summary:")
    print(f"   • Account ID: U15269296")
    print(f"   • Account Name: jerryzhang")
    print(f"   • Port: 4017")
    print(f"   • Script: ./live-jerryzhang.sh")
    print(f"   • Database: Trades recorded as 'jerryzhang'")

if __name__ == "__main__":
    verify_jerry_setup() 