#!/usr/bin/env python3
import sys
sys.path.append('core-refactor')
from ib_insync import IB, Option
import time

def test_market_data_permissions():
    """Test if we have basic market data permissions"""
    
    ib = IB()
    try:
        ib.connect('127.0.0.1', 4015, clientId=1, timeout=30)
        print("✅ Connected to IB")
        
        # Wait for connection
        time.sleep(3)
        
        # Test 1: Basic SPX index data
        print("\n=== Test 1: SPX Index Data ===")
        try:
            from ib_insync import Index
            spx_index = Index('SPX', 'CBOE')
            qualified_index = ib.qualifyContracts(spx_index)
            if qualified_index:
                print("✅ SPX index contract qualified")
                ticker = ib.reqMktData(qualified_index[0])
                time.sleep(2)
                print(f"SPX Index Price: {ticker.last if ticker.last else 'No data'}")
                ib.cancelMktData(qualified_index[0])
            else:
                print("❌ Failed to qualify SPX index")
        except Exception as e:
            print(f"❌ SPX index error: {e}")
        
        # Test 2: Single SPX option contract
        print("\n=== Test 2: Single SPX Option Contract ===")
        try:
            import datetime as dt
            expiry = dt.datetime.now().strftime("%Y%m%d")
            print(f"Using expiry: {expiry}")
            
            # Create a simple ATM call option
            option_contract = Option(
                symbol='SPX',
                lastTradeDateOrContractMonth=expiry,
                strike=6020.0,
                right='C',
                exchange='SMART',
                tradingClass='SPXW'
            )
            
            qualified_options = ib.qualifyContracts(option_contract)
            if qualified_options:
                print("✅ SPX option contract qualified")
                print(f"ConId: {qualified_options[0].conId}")
                print(f"LocalSymbol: {qualified_options[0].localSymbol}")
                
                # Test market data for this option
                ticker = ib.reqMktData(qualified_options[0])
                time.sleep(3)
                print(f"Option Bid: {ticker.bid if ticker.bid else 'No bid'}")
                print(f"Option Ask: {ticker.ask if ticker.ask else 'No ask'}")
                print(f"Option Last: {ticker.last if ticker.last else 'No last'}")
                ib.cancelMktData(qualified_options[0])
                
                if ticker.bid or ticker.ask or ticker.last:
                    print("✅ Option market data received")
                else:
                    print("❌ No option market data - likely permission issue")
                    
            else:
                print("❌ Failed to qualify SPX option contract")
                
        except Exception as e:
            print(f"❌ Option contract error: {e}")
            import traceback
            traceback.print_exc()
        
        # Test 3: Check account permissions
        print("\n=== Test 3: Account Information ===")
        try:
            account_summary = ib.accountSummary()
            print("✅ Account summary received")
            
            # Look for market data permissions
            for item in account_summary:
                if 'MarketData' in item.tag or 'Permission' in item.tag:
                    print(f"  {item.tag}: {item.value}")
                    
        except Exception as e:
            print(f"❌ Account info error: {e}")
        
        # Test 4: Portfolio positions
        print("\n=== Test 4: Portfolio Positions ===")
        try:
            positions = ib.positions()
            if positions:
                print(f"✅ Found {len(positions)} positions")
                for pos in positions[:3]:  # Show first 3
                    print(f"  {pos.contract.symbol}: {pos.position} shares")
            else:
                print("No positions found")
        except Exception as e:
            print(f"❌ Positions error: {e}")
        
        print(f"\n=== Summary ===")
        print(f"If SPX option market data fails → Check IB market data subscriptions")
        print(f"If contract qualification fails → Check symbol/exchange settings")
        print(f"If connection works but quotes fail → Timing or permission issue")
        
        ib.disconnect()
        print("\n✅ Disconnected from IB")
        
    except Exception as e:
        print(f"❌ Connection error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_market_data_permissions() 