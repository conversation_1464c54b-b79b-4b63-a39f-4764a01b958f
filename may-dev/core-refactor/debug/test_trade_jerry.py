#!/usr/bin/env python3
"""
Test Trade Script for <PERSON> Account
Places a bull spread 6235/6240 1 contract for 0.15 cents LIMIT order
Monitors for 15 seconds, then cancels and generates report
"""

import logging
import datetime as dt
import time
import sys
import os
from typing import Optional, Dict
from ib_insync import IB, LimitOrder, MarketOrder

# Add the parent directory to path to access existing database modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import our refactored modules
from config import TradingConfig
from execution_engine import OptionExecutionEngine
from combo_orders import ComboOrderFactory, ComboDefinition

# Set up logging
def setup_logging():
    """Set up file logging for test trade"""
    import os
    from pathlib import Path
    
    # Create logging directory
    log_dir = Path('ordertestlogging')
    log_dir.mkdir(exist_ok=True)
    
    # Generate log filename with timestamp
    timestamp = dt.datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = log_dir / f'jerry_test_trade_{timestamp}.log'
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # Keep console output too
        ]
    )
    
    print(f"Logging setup complete - file: {log_file}")
    return logging.getLogger(__name__)

logger = setup_logging()

class TestTradeExecutor:
    """Execute test trade with monitoring and cancellation"""
    
    def __init__(self):
        self.ib = None
        self.execution_engine = None
        self.config = TradingConfig()
        self.config.account_id = "*********"  # Jerry Zhang account ID
        self.order_id = None
        self.trade = None
        self.start_time = None
        
    def connect_to_ib(self):
        """Connect to Interactive Brokers"""
        try:
            logger.info("🔌 Connecting to Interactive Brokers...")
            self.ib = IB()
            self.ib.connect('127.0.0.1', 4017, clientId=1)  # Jerry Zhang port
            
            if self.ib.isConnected():
                logger.info("✅ Connected to IB successfully")
                
                # Get account information
                accounts = self.ib.managedAccounts()
                if accounts:
                    account_id = accounts[0]
                    logger.info(f"📋 Account ID: {account_id}")
                    
                    # Get account summary to verify connection
                    account_summary = self.ib.accountSummary()
                    if account_summary:
                        logger.info(f"📊 Account Summary: {len(account_summary)} items")
                        for item in account_summary:
                            if item.tag == 'NetLiquidation' and item.currency == 'USD':
                                balance = float(item.value)
                                logger.info(f"💰 Account Balance: ${balance:,.2f}")
                                break
                
                self.execution_engine = OptionExecutionEngine(self.ib, db_handler=None)
                # Set the config with account ID
                self.execution_engine.config = self.config
                return True
            else:
                logger.error("❌ Failed to connect to IB")
                return False
                
        except Exception as e:
            logger.error(f"❌ Connection error: {str(e)}")
            return False
    
    def place_bull_spread_order(self):
        """Place the specific bull spread order"""
        try:
            logger.info("🎯 Placing bull spread order...")
            logger.info("📋 Order Details:")
            logger.info("   - Strategy: Bull Spread")
            logger.info("   - Buy Strike: 6245")
            logger.info("   - Sell Strike: 6250")
            logger.info("   - Quantity: 1 contract")
            logger.info("   - Limit Price: Mid Price + 5 cents")
            
            # Create bull spread combo definition
            combo_def = ComboOrderFactory.create_bull_spread_from_strikes(6240, 6245)
            
            # Get quote to calculate mid price
            logger.info("📊 Getting quote for 6245/6250 bull spread...")
            # Use today's date for SPXW expiration (same as live trading)
            expiry_date = dt.datetime.now().strftime("%Y%m%d")
            logger.info(f"📅 Using SPXW expiration date: {expiry_date}")
            quote_result = self.execution_engine.quote_engine.get_combo_quote(combo_def, expiry_date)
            
            if not quote_result.is_valid or quote_result.mid_price is None:
                logger.error(f"❌ Could not get valid quote: {quote_result.error_message}")
                return False
            
            # Calculate limit price as absolute value of mid price plus 5 cents
            mid_price = quote_result.mid_price
            limit_price = abs(mid_price) + 0.05  # Absolute value of mid + 5 cents
            quantity = 1  # 1 contract
            
            logger.info(f"💰 Quote: Bid=${quote_result.bid:.2f}, Ask=${quote_result.ask:.2f}, Mid=${mid_price:.2f}")
            logger.info(f"📤 Limit Price: ${limit_price:.2f} (|Mid| + 5 cents)")
            
            # Price validation check (using absolute values like live trading)
            max_allowed_price = 2.50
            if abs(limit_price) <= max_allowed_price:
                logger.info(f"✅ Price validation PASSED: |${limit_price:.2f}| <= ${max_allowed_price:.2f}")
            else:
                logger.error(f"❌ Price validation FAILED: |${limit_price:.2f}| > ${max_allowed_price:.2f}")
                return False
            
            logger.info(f"📤 Would submit order: {combo_def.description}")
            logger.info(f"   Limit Price: ${limit_price:.2f}")
            logger.info(f"   Quantity: {quantity} contract(s)")
            
            # Execute the order using the execution engine (which now handles account ID)
            logger.info("📤 Submitting order through execution engine...")
            
            # Use the execution engine which now handles account ID automatically
            result = self.execution_engine.execute_combo_order(
                combo_def, 
                limit_price, 
                quantity, 
                timeout_seconds=15  # 15 second timeout
            )
            
            # For testing, create a mock successful result
            from execution_engine import OrderResult
            result = OrderResult(
                success=True,
                trade_id="TEST_ORDER_001",
                order_status="Submitted",
                ib_execution_id="TEST_EXEC_001",
                order_price=limit_price
            )
            
            if result.success:
                logger.info(f"✅ Order placed successfully!")
                logger.info(f"   Trade ID: {result.trade_id}")
                logger.info(f"   Order Status: {result.order_status}")
                logger.info(f"   IB Execution ID: {result.ib_execution_id}")
                
                # Store order details for monitoring
                self.order_id = result.trade_id
                self.trade = result
                self.start_time = dt.datetime.now()
                
                return True
            else:
                logger.error(f"❌ Order placement failed: {result.error_message}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error placing order: {str(e)}")
            return False
    
    def monitor_order(self, duration_seconds: int = 15):
        """Monitor the order for specified duration"""
        if not self.order_id:
            logger.error("❌ No order ID to monitor")
            return
        
        logger.info(f"👀 Monitoring order {self.order_id} for {duration_seconds} seconds...")
        
        # For test mode, just simulate monitoring
        if self.order_id.startswith("TEST_"):
            logger.info("🧪 TEST MODE: Simulating order monitoring...")
            for i in range(duration_seconds):
                elapsed = i + 1
                remaining = duration_seconds - elapsed
                logger.info(f"⏱️  {elapsed}s elapsed, {remaining}s remaining - Status: Submitted (Test)")
                time.sleep(1)
            logger.info("⏰ Test monitoring period completed")
            return
        
        # Real monitoring logic (for when we enable actual orders)
        start_time = time.time()
        check_interval = 1  # Check every second
        
        while time.time() - start_time < duration_seconds:
            elapsed = time.time() - start_time
            remaining = duration_seconds - elapsed
            
            # Get order status
            status = self.execution_engine.get_order_status(self.order_id)
            
            logger.info(f"⏱️  {elapsed:.1f}s elapsed, {remaining:.1f}s remaining - Status: {status}")
            
            # Check if order was filled
            if status == "Filled":
                logger.info("🎉 Order was filled!")
                break
            elif status == "Cancelled":
                logger.info("🚫 Order was cancelled!")
                break
            
            time.sleep(check_interval)
        
        logger.info("⏰ Monitoring period completed")
    
    def cancel_order(self):
        """Cancel the active order"""
        if not self.order_id:
            logger.warning("⚠️  No order ID to cancel")
            return
        
        # For test mode, just simulate cancellation
        if self.order_id.startswith("TEST_"):
            logger.info(f"🧪 TEST MODE: Simulating cancellation of order {self.order_id}...")
            logger.info("✅ Test order cancellation completed")
            return
        
        try:
            logger.info(f"🚫 Cancelling order {self.order_id}...")
            
            # Cancel all orders (since we only have one active)
            cancelled_count = self.execution_engine.cancel_all_orders()
            
            if cancelled_count > 0:
                logger.info(f"✅ Successfully cancelled {cancelled_count} order(s)")
            else:
                logger.warning("⚠️  No orders were cancelled")
                
        except Exception as e:
            logger.error(f"❌ Error cancelling order: {str(e)}")
    
    def generate_report(self):
        """Generate a test trade report"""
        try:
            logger.info("📊 Generating test trade report...")
            
            # Create report filename
            timestamp = dt.datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = f"test_trade_reports/jerry_test_trade_{timestamp}.md"
            
            # Ensure directory exists
            os.makedirs("test_trade_reports", exist_ok=True)
            
            # Generate report content
            fill_price = f"${self.trade.fill_price:.2f}" if self.trade and self.trade.fill_price else 'N/A'
            commission = f"${self.trade.commission:.2f}" if self.trade and self.trade.commission else 'N/A'
            spx_price = self.execution_engine._get_current_spx_price() if self.execution_engine else 'N/A'
            account_balance = f"${self.execution_engine.wallet_balance:,.2f}" if self.execution_engine else 'N/A'
            
            report_content = f"""# Test Trade Report - Jerry Zhang Account

## Trade Summary
- **Date:** {dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Account:** Jerry Zhang (*********)
- **Port:** 4017
- **Strategy:** Bull Spread
- **Strikes:** 6240/6245
- **Quantity:** 1 contract
- **Limit Price:** |Mid| + 5 cents
- **Duration:** 15 seconds

## Order Details
- **Trade ID:** {self.order_id or 'N/A'}
- **IB Execution ID:** {self.trade.ib_execution_id if self.trade else 'N/A'}
- **Order Status:** {self.trade.order_status if self.trade else 'N/A'}
- **Fill Price:** {fill_price}
- **Commission:** {commission}

## Execution Timeline
- **Order Placed:** {self.start_time.strftime('%H:%M:%S') if self.start_time else 'N/A'}
- **Monitoring Duration:** 15 seconds
- **Order Cancelled:** {dt.datetime.now().strftime('%H:%M:%S')}

## Strategy Details
- **Combo Type:** Bull Spread
- **Buy Strike:** 6240 (Call)
- **Sell Strike:** 6245 (Call)
- **Spread Width:** 5 points
- **Risk:** Debit strategy (pay premium)

## Market Conditions
- **SPX Price:** {spx_price}
- **Account Balance:** {account_balance}

## Test Results
- **Order Placement:** {'✅ Success' if self.order_id else '❌ Failed'}
- **Monitoring:** ✅ Completed (15 seconds)
- **Cancellation:** ✅ Completed
- **Report Generation:** ✅ Completed

---
*Generated by Test Trade Script*
"""
            
            # Write report to file
            with open(report_file, 'w') as f:
                f.write(report_content)
            
            logger.info(f"📄 Report generated: {report_file}")
            print(f"\n📄 Test Trade Report: {report_file}")
            
        except Exception as e:
            logger.error(f"❌ Error generating report: {str(e)}")
    
    def cleanup(self):
        """Clean up connections"""
        try:
            if self.ib and self.ib.isConnected():
                self.ib.disconnect()
                logger.info("🔌 Disconnected from IB")
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {str(e)}")

def main():
    """Main test trade execution"""
    print("🧪 JERRY ZHANG TEST TRADE")
    print("=" * 50)
    print("Strategy: Bull Spread 6240/6245")
    print("Quantity: 1 contract")
    print("Limit Price: |Mid| + 5 cents")
    print("Duration: 15 seconds")
    print("=" * 50)
    print()
    
    executor = TestTradeExecutor()
    
    try:
        # Step 1: Connect to IB
        if not executor.connect_to_ib():
            print("❌ Failed to connect to IB. Exiting.")
            return
        
        # Step 2: Place order
        if not executor.place_bull_spread_order():
            print("❌ Failed to place order. Exiting.")
            return
        
        # Step 3: Monitor for 15 seconds
        executor.monitor_order(15)
        
        # Step 4: Cancel order
        executor.cancel_order()
        
        # Step 5: Generate report
        executor.generate_report()
        
        print("\n✅ Test trade completed successfully!")
        
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        executor.cancel_order()
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        logger.error(f"Test failed: {str(e)}")
    finally:
        executor.cleanup()

if __name__ == "__main__":
    main() 