{"timestamp": "2025-06-24T17:47:38.353590", "summary": {"total_tests": 8, "passed_tests": 3, "failed_tests": 5, "success_rate": 37.5, "recommendation": "DO NOT TRADE"}, "detailed_results": [{"name": "Trading Configuration Validation", "success": true, "duration": 0.1, "stdout": "Configuration checks completed", "stderr": ""}, {"name": "Event-Driven Architecture (Timing Reliability)", "success": true, "duration": 1.418813943862915, "stdout": "🔬 EVENT-DRIVEN IMPLEMENTATION VERIFICATION\n============================================================\n\n📊 VERIFICATION RESULTS\n==============================\nTests run: 11\nFailures: 0\nErrors: 0\n\n✅ SUCCESS RATE: 100.0%\n\n🎉 EVENT-DRIVEN IMPLEMENTATION VERIFIED!\n💡 KEY IMPROVEMENTS:\n   ✅ Bar events trigger processing automatically\n   ✅ Execution scheduling is deterministic\n   ✅ UI updates reduced from 1Hz to 0.2Hz (5x improvement)\n   ✅ Main loop timing is no longer dependent on system performance\n   ✅ Timer cleanup prevents resource leaks\n\n💡 NEXT STEPS:\n- Implementation verified - ready for testing\n- Run live test with both accounts\n- Monitor timing precision improvements\n", "stderr": "test_bar_processing_tracking (__main__.TestEventDrivenImplementation.test_bar_processing_tracking)\nTest bar processing tracking works ... ok\ntest_check_for_new_bars_method_exists (__main__.TestEventDrivenImplementation.test_check_for_new_bars_method_exists)\nTest that the new check_for_new_bars method exists ... ok\ntest_cleanup_cancels_timers (__main__.TestEventDrivenImplementation.test_cleanup_cancels_timers)\nTest that cleanup cancels pending timers ... ok\ntest_execution_timer_calls_strategy (__main__.TestEventDrivenImplementation.test_execution_timer_calls_strategy)\nTest that the execution timer actually calls the strategy ... ok\ntest_import_success (__main__.TestEventDrivenImplementation.test_import_success)\nTest that threading import was added successfully ... ok\ntest_main_loop_modifications (__main__.TestEventDrivenImplementation.test_main_loop_modifications)\nTest that main loop was modified correctly ... ok\ntest_on_bar_received_method_exists (__main__.TestEventDrivenImplementation.test_on_bar_received_method_exists)\nTest that the new on_bar_received method exists ... ok\ntest_on_bar_received_processing (__main__.TestEventDrivenImplementation.test_on_bar_received_processing)\nTest that on_bar_received processes bars correctly ... No price data for bar ending at 2025-06-24 15:50:00\nok\ntest_schedule_execution_creates_timer (__main__.TestEventDrivenImplementation.test_schedule_execution_creates_timer)\nTest that schedule_execution creates a proper timer ... ok\ntest_schedule_execution_method_exists (__main__.TestEventDrivenImplementation.test_schedule_execution_method_exists)\nTest that the new schedule_execution method exists ... ok\ntest_third_bar_schedules_execution (__main__.TestEventDrivenImplementation.test_third_bar_schedules_execution)\nTest that third bar schedules execution ... ok\n\n----------------------------------------------------------------------\nRan 11 tests in 1.112s\n\nOK\n"}, {"name": "No Trade Safety Mechanism (Data Validation)", "success": true, "duration": 0.28926873207092285, "stdout": "🔬 NO TRADE SAFETY MECHANISM TEST\n==================================================\n\n📊 TEST RESULTS\n==============================\nTests run: 7\nFailures: 0\nErrors: 0\n\n✅ SUCCESS RATE: 100.0%\n\n🎉 NO TRADE SAFETY MECHANISM VERIFIED!\n💡 SAFETY FEATURES:\n   ✅ Validates all bar data before execution\n   ✅ Defaults to NT when data is incomplete\n   ✅ Preserves original decision for audit trail\n   ✅ Generates proper trade reports for NT\n   ✅ Handles NT decisions in main execution flow\n\n💡 SAFETY SUMMARY:\n- No Trade safety mechanism is working correctly\n- System will default to NT if bar data is missing\n- Ready for production with enhanced safety\n", "stderr": "test_execute_no_trade (__main__.TestNoTradeSafety.test_execute_no_trade)\nTest No Trade execution works correctly ... ok\ntest_main_strategy_execution_with_complete_data (__main__.TestNoTradeSafety.test_main_strategy_execution_with_complete_data)\nTest that main strategy execution proceeds normally with complete data ... ok\ntest_main_strategy_execution_with_missing_data (__main__.TestNoTradeSafety.test_main_strategy_execution_with_missing_data)\nTest that main strategy execution defaults to NT when data is missing ... ❌ Missing critical data: Bar 1, Bar 2, Bar 3, SPAN1, SPAN2, SPAN3\nMissing bar data: Bar 1, Bar 2, Bar 3, SPAN1, SPAN2, SPAN3\n🚫 SAFETY: Missing bar data - defaulting to NO TRADE\n❌ Bar data validation failed - converting to NO TRADE\nok\ntest_no_trade_decision_type_execution (__main__.TestNoTradeSafety.test_no_trade_decision_type_execution)\nTest that DecisionType.NONE is handled correctly in main execution ... ok\ntest_validate_bar_data_complete_missing_bar (__main__.TestNoTradeSafety.test_validate_bar_data_complete_missing_bar)\nTest validation fails when bar data is missing ... ❌ Missing critical data: Bar 3, SPAN3\nMissing bar data: Bar 3, SPAN3\nok\ntest_validate_bar_data_complete_missing_span (__main__.TestNoTradeSafety.test_validate_bar_data_complete_missing_span)\nTest validation fails when span data is missing ... ❌ Missing critical data: SPAN1, SPAN2, SPAN3\nMissing bar data: SPAN1, SPAN2, SPAN3\nok\ntest_validate_bar_data_complete_with_all_data (__main__.TestNoTradeSafety.test_validate_bar_data_complete_with_all_data)\nTest validation passes when all bar data is available ... ok\n\n----------------------------------------------------------------------\nRan 7 tests in 0.008s\n\nOK\n"}, {"name": "Decision Engine Accuracy", "success": false, "duration": 0.04309439659118652, "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"/home/<USER>/trade/JerryTrade/may-dev/core-refactor/backtesting/debug/test_decision_engine.py\", line 17, in <module>\n    from strategy_state import StrategyState, BarData, DecisionType\nModuleNotFoundError: No module named 'strategy_state'\n"}, {"name": "Live Integration (Order Flow)", "success": false, "duration": 0.01743769645690918, "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"/home/<USER>/trade/JerryTrade/may-dev/core-refactor/debug/test_live_integration.py\", line 11, in <module>\n    from combo_orders import ComboOrderFactory, OptionPricingUtils, ComboQuoteResult\nModuleNotFoundError: No module named 'combo_orders'\n"}, {"name": "Simulation with Scenario Data", "success": false, "duration": 0.****************, "stdout": "📡 Using port 4001 for both data and trading connections\nLogging setup complete - files in logging/ (account: trading)\n🧪 SIMULATION MODE ENABLED\n   Scenario: BULL_001\nDEBUG: About to connect to IB...\nDEBUG: IB connection result: False\n", "stderr": "2025-06-24 17:47:37,439 - INFO - database.mssql_handler - Successfully connected to SQL Server at *************\n2025-06-24 17:47:37,487 - INFO - database.mssql_handler - Verified executed_trades table exists\n2025-06-24 17:47:37,487 - INFO - database.mssql_handler - MSSQL Database initialized on *************\n"}, {"name": "IB Paper Trading Connectivity", "success": false, "duration": 0.****************, "stdout": "", "stderr": "API connection failed: ConnectionRefusedError(111, \"Connect call failed ('127.0.0.1', 4001)\")\nMake sure API port on TWS/IBG is open\nTraceback (most recent call last):\n  File \"<string>\", line 1, in <module>\n  File \"/home/<USER>/trade/venv/lib/python3.12/site-packages/ib_insync/ib.py\", line 279, in connect\n    return self._run(self.connectAsync(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/trade/venv/lib/python3.12/site-packages/ib_insync/ib.py\", line 318, in _run\n    return util.run(*awaitables, timeout=self.RequestTimeout)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/trade/venv/lib/python3.12/site-packages/ib_insync/util.py\", line 341, in run\n    result = loop.run_until_complete(task)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3.12/asyncio/base_events.py\", line 687, in run_until_complete\n    return future.result()\n           ^^^^^^^^^^^^^^^\n  File \"/home/<USER>/trade/venv/lib/python3.12/site-packages/ib_insync/ib.py\", line 1748, in connectAsync\n    await self.client.connectAsync(host, port, clientId, timeout)\n  File \"/home/<USER>/trade/venv/lib/python3.12/site-packages/ib_insync/client.py\", line 211, in connectAsync\n    await asyncio.wait_for(self.conn.connectAsync(host, port), timeout)\n  File \"/usr/lib/python3.12/asyncio/tasks.py\", line 520, in wait_for\n    return await fut\n           ^^^^^^^^^\n  File \"/home/<USER>/trade/venv/lib/python3.12/site-packages/ib_insync/connection.py\", line 39, in connectAsync\n    self.transport, _ = await loop.create_connection(\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3.12/asyncio/base_events.py\", line 1122, in create_connection\n    raise exceptions[0]\n  File \"/usr/lib/python3.12/asyncio/base_events.py\", line 1104, in create_connection\n    sock = await self._connect_sock(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3.12/asyncio/base_events.py\", line 1007, in _connect_sock\n    await self.sock_connect(sock, address)\n  File \"/usr/lib/python3.12/asyncio/selector_events.py\", line 651, in sock_connect\n    return await fut\n           ^^^^^^^^^\n  File \"/usr/lib/python3.12/asyncio/selector_events.py\", line 691, in _sock_connect_cb\n    raise OSError(err, f'Connect call failed {address}')\nConnectionRefusedError: [Errno 111] Connect call failed ('127.0.0.1', 4001)\n"}, {"name": "Live Test Mode Order Flow", "success": false, "duration": 0.****************, "stdout": "📡 Using port 4001 for both data and trading connections\nLogging setup complete - files in logging/ (account: trading)\nDEBUG: About to connect to IB...\nDEBUG: IB connection result: False\n", "stderr": "2025-06-24 17:47:38,261 - INFO - database.mssql_handler - Successfully connected to SQL Server at *************\n2025-06-24 17:47:38,308 - INFO - database.mssql_handler - Verified executed_trades table exists\n2025-06-24 17:47:38,308 - INFO - database.mssql_handler - MSSQL Database initialized on *************\n2025-06-24 17:47:38,309 - INFO - ib_insync.client - Connecting to 127.0.0.1:4001 with clientId 1...\n2025-06-24 17:47:38,309 - INFO - ib_insync.client - Disconnecting\n2025-06-24 17:47:38,309 - ERROR - ib_insync.client - API connection failed: ConnectionRefusedError(111, \"Connect call failed ('127.0.0.1', 4001)\")\n2025-06-24 17:47:38,310 - ERROR - ib_insync.client - Make sure API port on TWS/IBG is open\n"}]}