#!/usr/bin/env python3
import sys
sys.path.append('core-refactor')
from quote_engine import OptionQuoteEngine
from combo_orders import ComboOrderFactory
from ib_insync import IB
import datetime as dt
import time

def test_quote_reliability():
    """Test quote reliability over multiple attempts"""
    
    # Connect to IB
    ib = IB()
    try:
        ib.connect('127.0.0.1', 4015, clientId=1, timeout=30)
        print("Connected to IB")
        
        # Wait for connection to stabilize
        print("Waiting 5 seconds for market data feeds to stabilize...")
        time.sleep(5)
        
        # Initialize quote engine
        quote_engine = OptionQuoteEngine(ib)
        
        # Test current SPX price area
        center_strike = 6020.0
        print(f'\n=== Testing Wide Iron Butterfly Quotes for Strike {center_strike} ===\n')
        
        # Test multiple times to see reliability
        for attempt in range(5):
            print(f"--- Attempt {attempt + 1} ---")
            
            try:
                # Method 1: Direct combo quote
                combo_def = ComboOrderFactory.create_wide_iron_butterfly(center_strike)
                quote_result = quote_engine.get_combo_quote(combo_def)
                
                print(f"  Direct combo quote:")
                print(f"    Valid: {quote_result.is_valid}")
                print(f"    Bid: {quote_result.bid} ({'NaN' if quote_result.bid != quote_result.bid else 'Valid'})")
                print(f"    Ask: {quote_result.ask} ({'NaN' if quote_result.ask != quote_result.ask else 'Valid'})")
                print(f"    Error: {quote_result.error_message}")
                
                if quote_result.is_valid and quote_result.bid is not None:
                    credit = abs(quote_result.bid) if quote_result.bid == quote_result.bid else 0.0
                    print(f"    → Credit would be: ${credit:.2f}")
                    print(f"    → Above $6.00 threshold: {'YES' if credit >= 6.0 else 'NO'}")
                
                # Method 2: Using get_wide_butterfly_quote
                wide_quote = quote_engine.get_wide_butterfly_quote(center_strike)
                print(f"  get_wide_butterfly_quote(): ${wide_quote:.2f}")
                
            except Exception as e:
                print(f"  ERROR: {str(e)}")
                import traceback
                traceback.print_exc()
            
            # Wait between attempts
            if attempt < 4:
                print("  Waiting 2 seconds...")
                time.sleep(2)
        
        # Test narrow iron butterfly as fallback
        print(f'\n=== Testing Narrow Iron Butterfly (Fallback) ===')
        try:
            narrow_combo_def = ComboOrderFactory.create_iron_butterfly(center_strike, wing_width=5)
            narrow_quote_result = quote_engine.get_combo_quote(narrow_combo_def)
            
            print(f"Narrow butterfly quote:")
            print(f"  Valid: {narrow_quote_result.is_valid}")
            print(f"  Bid: {narrow_quote_result.bid}")
            print(f"  Ask: {narrow_quote_result.ask}")
            print(f"  Error: {narrow_quote_result.error_message}")
            
            if narrow_quote_result.is_valid and narrow_quote_result.bid is not None:
                credit = abs(narrow_quote_result.bid) if narrow_quote_result.bid == narrow_quote_result.bid else 0.0
                print(f"  → Narrow credit: ${credit:.2f}")
                print(f"  → Above $3.40 threshold: {'YES' if credit >= 3.4 else 'NO'}")
        
        except Exception as e:
            print(f"Narrow butterfly error: {str(e)}")
        
        print(f'\n=== Summary ===')
        print(f"If wide butterfly quote >= $6.00: Execute wide butterfly")
        print(f"If wide butterfly quote < $6.00: Execute narrow butterfly fallback")
        print(f"Current issue: Wide butterfly returning $0.00, fallback failing")
        
        ib.disconnect()
        print("\nDisconnected from IB")
        
    except Exception as e:
        print(f"Connection error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_quote_reliability() 