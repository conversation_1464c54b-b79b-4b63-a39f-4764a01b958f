import sys
sys.path.append('core-refactor')
from quote_engine import OptionQuoteEngine
from combo_orders import ComboOrderFactory
from ib_insync import IB
import datetime as dt
import time

# Connect to IB
ib = IB()
try:
    ib.connect('127.0.0.1', 4015, clientId=1, timeout=30)
    print("Connected to IB")
    
    # Wait for connection to stabilize and market data to be ready
    print("Waiting 3 seconds for market data feeds to stabilize...")
    time.sleep(3)
    
    # Initialize quote engine  
    quote_engine = OptionQuoteEngine(ib)
    
    # Test the exact combo that failed
    center_strike = 6010.0
    print(f'Testing iron butterfly for strike {center_strike}...\n')
    
    # Create the wide iron butterfly definition
    combo_def = ComboOrderFactory.create_wide_iron_butterfly(center_strike)
    print(f'Combo definition: {combo_def}')
    print(f'Combo type: {combo_def.combo_type}')
    print(f'Is credit strategy: {combo_def.is_credit_strategy}')
    print(f'Legs:')
    for i, leg in enumerate(combo_def.legs):
        print(f'  Leg {i+1}: {leg.action.value} {leg.strike} {leg.option_type.value}')
    
    # Test the full quote process
    print(f'\n--- Testing get_combo_quote() ---')
    expiry_date = dt.datetime.now().strftime("%Y%m%d")
    quote_result = quote_engine.get_combo_quote(combo_def, expiry_date)
    
    print(f'Quote result valid: {quote_result.is_valid}')
    print(f'Quote result bid: {quote_result.bid}')
    print(f'Quote result ask: {quote_result.ask}')
    print(f'Quote result error: {quote_result.error_message}')
    
    if quote_result.bid is not None:
        print(f'Bid type: {type(quote_result.bid)}')
        print(f'Bid is NaN: {quote_result.bid != quote_result.bid}')
        print(f'Abs bid: {abs(quote_result.bid)}')
    
    ib.disconnect()
    print("Disconnected from IB")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc() 