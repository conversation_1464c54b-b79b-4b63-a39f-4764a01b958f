#!/usr/bin/env python3
"""
Test: No Trade Safety Mechanism
Tests that the system defaults to NT when bar data is incomplete
"""

import sys
import os
import datetime as dt
import unittest
from unittest.mock import Mock, patch, MagicMock

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import required modules
from spx_strategy import SPXTradingStrategy
from strategy_state import StrategyState, DecisionType, BarData, SpanData
from config import TradingConfig

class TestNoTradeSafety(unittest.TestCase):
    """Test the No Trade safety mechanism"""
    
    def setUp(self):
        """Set up test environment"""
        # Create minimal config
        self.config = TradingConfig()
        
        # Mock dependencies
        self.mock_quote_engine = Mock()
        self.mock_execution_engine = Mock()
        
        # Create strategy
        with patch('spx_strategy.get_global_config', return_value=self.config):
            with patch('spx_strategy.TradeReportGenerator'):
                self.strategy = SPXTradingStrategy(self.mock_quote_engine, self.mock_execution_engine)
    
    def test_validate_bar_data_complete_with_all_data(self):
        """Test validation passes when all bar data is available"""
        # Setup complete bar data
        bar1 = BarData(dt.datetime.now(), 5000, 5010, 4990, 5005, name="first")
        bar2 = BarData(dt.datetime.now(), 5005, 5015, 4995, 5010, name="second") 
        bar3 = BarData(dt.datetime.now(), 5010, 5020, 5000, 5015, name="third")
        
        span1 = SpanData("SPAN1", 5010, 4990, ["first"])
        span2 = SpanData("SPAN2", 5015, 4990, ["first", "second"])
        span3 = SpanData("SPAN3", 5020, 4990, ["first", "second", "third"])
        
        self.strategy.state.first_bar = bar1
        self.strategy.state.second_bar = bar2
        self.strategy.state.third_bar = bar3
        self.strategy.state.span1 = span1
        self.strategy.state.span2 = span2
        self.strategy.state.span3 = span3
        
        # Test validation
        result = self.strategy._validate_bar_data_complete()
        self.assertTrue(result)
    
    def test_validate_bar_data_complete_missing_bar(self):
        """Test validation fails when bar data is missing"""
        # Setup incomplete bar data (missing third bar)
        bar1 = BarData(dt.datetime.now(), 5000, 5010, 4990, 5005, name="first")
        bar2 = BarData(dt.datetime.now(), 5005, 5015, 4995, 5010, name="second")
        
        span1 = SpanData("SPAN1", 5010, 4990, ["first"])
        span2 = SpanData("SPAN2", 5015, 4990, ["first", "second"])
        
        self.strategy.state.first_bar = bar1
        self.strategy.state.second_bar = bar2
        # self.strategy.state.third_bar = None  # Missing!
        self.strategy.state.span1 = span1
        self.strategy.state.span2 = span2
        # self.strategy.state.span3 = None  # Missing!
        
        # Test validation
        result = self.strategy._validate_bar_data_complete()
        self.assertFalse(result)
    
    def test_validate_bar_data_complete_missing_span(self):
        """Test validation fails when span data is missing"""
        # Setup incomplete span data
        bar1 = BarData(dt.datetime.now(), 5000, 5010, 4990, 5005, name="first")
        bar2 = BarData(dt.datetime.now(), 5005, 5015, 4995, 5010, name="second")
        bar3 = BarData(dt.datetime.now(), 5010, 5020, 5000, 5015, name="third")
        
        self.strategy.state.first_bar = bar1
        self.strategy.state.second_bar = bar2
        self.strategy.state.third_bar = bar3
        # Missing all span data
        
        # Test validation
        result = self.strategy._validate_bar_data_complete()
        self.assertFalse(result)
    
    def test_execute_no_trade(self):
        """Test No Trade execution works correctly"""
        # Set initial decision
        self.strategy.state.current_decision = DecisionType.BULL
        
        # Execute No Trade
        result = self.strategy._execute_no_trade("Test safety mechanism")
        
        # Verify result
        self.assertTrue(result)
        self.assertEqual(self.strategy.state.current_decision, DecisionType.NONE)
        self.assertIsNotNone(self.strategy.state.trade_execution)
        self.assertEqual(self.strategy.state.trade_execution.decision_type, DecisionType.NONE)
        self.assertEqual(self.strategy.state.trade_execution.original_decision, DecisionType.BULL)
        self.assertTrue(self.strategy.state.trade_execution.trade_id.startswith("NT_"))
    
    def test_main_strategy_execution_with_missing_data(self):
        """Test that main strategy execution defaults to NT when data is missing"""
        # Setup incomplete data (no bars)
        self.strategy.state.current_decision = DecisionType.IRON_BUTTERFLY
        
        # Mock _get_current_spx_price to return a value
        self.strategy._get_current_spx_price = Mock(return_value=5000.0)
        
        # Execute main strategy
        result = self.strategy._execute_main_strategy()
        
        # Should return True (handled safely) and convert to No Trade
        self.assertTrue(result)
        self.assertEqual(self.strategy.state.current_decision, DecisionType.NONE)
        self.assertIsNotNone(self.strategy.state.trade_execution)
        self.assertEqual(self.strategy.state.trade_execution.decision_type, DecisionType.NONE)
        self.assertEqual(self.strategy.state.trade_execution.original_decision, DecisionType.IRON_BUTTERFLY)
    
    def test_main_strategy_execution_with_complete_data(self):
        """Test that main strategy execution proceeds normally with complete data"""
        # Setup complete bar data
        bar1 = BarData(dt.datetime.now(), 5000, 5010, 4990, 5005, name="first")
        bar2 = BarData(dt.datetime.now(), 5005, 5015, 4995, 5010, name="second") 
        bar3 = BarData(dt.datetime.now(), 5010, 5020, 5000, 5015, name="third")
        
        span1 = SpanData("SPAN1", 5010, 4990, ["first"])
        span2 = SpanData("SPAN2", 5015, 4990, ["first", "second"])
        span3 = SpanData("SPAN3", 5020, 4990, ["first", "second", "third"])
        
        self.strategy.state.first_bar = bar1
        self.strategy.state.second_bar = bar2
        self.strategy.state.third_bar = bar3
        self.strategy.state.span1 = span1
        self.strategy.state.span2 = span2
        self.strategy.state.span3 = span3
        self.strategy.state.current_decision = DecisionType.BULL
        
        # Mock execution method to avoid actual trading
        self.strategy._execute_bull_bear_with_retries = Mock(return_value=True)
        
        # Execute main strategy
        result = self.strategy._execute_main_strategy()
        
        # Should proceed to normal execution (not convert to NT)
        self.assertTrue(result)
        self.assertEqual(self.strategy.state.current_decision, DecisionType.BULL)  # Should remain BULL
        self.strategy._execute_bull_bear_with_retries.assert_called_once()
    
    def test_no_trade_decision_type_execution(self):
        """Test that DecisionType.NONE is handled correctly in main execution"""
        # Setup complete data but decision is already NONE
        bar1 = BarData(dt.datetime.now(), 5000, 5010, 4990, 5005, name="first")
        bar2 = BarData(dt.datetime.now(), 5005, 5015, 4995, 5010, name="second") 
        bar3 = BarData(dt.datetime.now(), 5010, 5020, 5000, 5015, name="third")
        
        span1 = SpanData("SPAN1", 5010, 4990, ["first"])
        span2 = SpanData("SPAN2", 5015, 4990, ["first", "second"])
        span3 = SpanData("SPAN3", 5020, 4990, ["first", "second", "third"])
        
        self.strategy.state.first_bar = bar1
        self.strategy.state.second_bar = bar2
        self.strategy.state.third_bar = bar3
        self.strategy.state.span1 = span1
        self.strategy.state.span2 = span2
        self.strategy.state.span3 = span3
        self.strategy.state.current_decision = DecisionType.NONE
        
        # Execute main strategy
        result = self.strategy._execute_main_strategy()
        
        # Should execute No Trade properly
        self.assertTrue(result)
        self.assertEqual(self.strategy.state.current_decision, DecisionType.NONE)


def run_no_trade_safety_test():
    """Run No Trade safety mechanism tests"""
    print("🔬 NO TRADE SAFETY MECHANISM TEST")
    print("=" * 50)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestNoTradeSafety)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Summary
    print(f"\n📊 TEST RESULTS")
    print("=" * 30)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  {test}: {traceback}")
    
    if result.errors:
        print("\n💥 ERRORS:")
        for test, traceback in result.errors:
            print(f"  {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n✅ SUCCESS RATE: {success_rate:.1f}%")
    
    if result.wasSuccessful():
        print("\n🎉 NO TRADE SAFETY MECHANISM VERIFIED!")
        print("💡 SAFETY FEATURES:")
        print("   ✅ Validates all bar data before execution")
        print("   ✅ Defaults to NT when data is incomplete")
        print("   ✅ Preserves original decision for audit trail")
        print("   ✅ Generates proper trade reports for NT")
        print("   ✅ Handles NT decisions in main execution flow")
    else:
        print("\n⚠️ SAFETY VERIFICATION ISSUES FOUND")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_no_trade_safety_test()
    
    print("\n💡 SAFETY SUMMARY:")
    if success:
        print("- No Trade safety mechanism is working correctly")
        print("- System will default to NT if bar data is missing")
        print("- Ready for production with enhanced safety")
    else:
        print("- Fix safety mechanism issues before deployment")
        print("- Re-run tests after fixes")
    
    exit(0 if success else 1) 