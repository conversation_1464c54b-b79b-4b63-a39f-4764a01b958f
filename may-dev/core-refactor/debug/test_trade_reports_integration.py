#!/usr/bin/env python3
"""
Test script for trade_reports table integration
"""

import sys
import os
import datetime as dt
from typing import Dict, Any

# Add paths for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_database_integration():
    """Test that database handlers can record trade reports"""
    try:
        from database.db_factory import get_database_handler
        
        # Test data
        test_report_data = {
            'account_id': 'TEST123',
            'account_name': 'test_account',
            'session_date': dt.date.today(),
            'session_start_time': dt.datetime.now() - dt.timedelta(minutes=5),
            'session_end_time': dt.datetime.now(),
            'session_duration_seconds': 300,
            'trading_mode': 'TEST',
            'final_decision': 'bull',
            'trade_executed': True,
            'execution_status': 'executed',
            'primary_strike': 5980.0,
            'execution_price': 2.45,
            'retry_attempts': 0,
            'bar_count': 3,
            'span3_value': 4.25,
            'close_percentage': 75.5,
            'total_events': 6,
            'error_message': None,
            'report_content': '# Test Report\n\nThis is a test trade report.',
            'algorithm_version': 'SPX New Algorithm v3.0'
        }
        
        # Get database handler
        db = get_database_handler()
        print(f"✅ Database handler created: {type(db).__name__}")
        
        # Test recording trade report
        report_id = db.record_trade_report(test_report_data)
        
        if report_id:
            print(f"✅ Trade report recorded successfully with ID: {report_id}")
            return True
        else:
            print("❌ Failed to record trade report")
            return False
            
    except Exception as e:
        print(f"❌ Database integration test failed: {str(e)}")
        return False

def test_execution_engine_integration():
    """Test that execution engine can record trade reports"""
    try:
        from execution_engine import OptionExecutionEngine
        from strategy_state import StrategyState, DecisionType
        from ib_insync import IB
        
        # Create mock IB connection
        ib = IB()
        
        # Create execution engine
        execution_engine = OptionExecutionEngine(ib)
        print(f"✅ Execution engine created")
        
        # Create mock strategy state
        state = StrategyState()
        state.start_session()
        state.current_decision = DecisionType.BULL
        
        # Test markdown content
        test_report_content = """# SPX Trading Report - 🧪 LIVE TEST

## Session Summary
- **Date:** 2025-06-20
- **Time:** 15:41:36 - 15:44:29
- **Duration:** 173.5 seconds
- **Final Decision:** **bull**
- **Trade Executed:** No

## Test Report
This is a test integration report.
"""
        
        # Test recording
        report_id = execution_engine.record_trade_report_to_database(test_report_content, state)
        
        if report_id:
            print(f"✅ Execution engine trade report recorded with ID: {report_id}")
            return True
        else:
            print("❌ Execution engine failed to record trade report")
            return False
            
    except Exception as e:
        print(f"❌ Execution engine integration test failed: {str(e)}")
        return False

def main():
    """Run all integration tests"""
    print("🧪 Testing trade_reports table integration...\n")
    
    tests_passed = 0
    total_tests = 2
    
    print("1. Testing database handler integration:")
    if test_database_integration():
        tests_passed += 1
    print()
    
    print("2. Testing execution engine integration:")
    if test_execution_engine_integration():
        tests_passed += 1
    print()
    
    print(f"🏁 Tests completed: {tests_passed}/{total_tests} passed")
    
    if tests_passed == total_tests:
        print("🎉 All integration tests passed! Ready for live testing.")
        return True
    else:
        print("❌ Some tests failed. Check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 