#!/usr/bin/env python3
"""
Quick Bull Test - Manually edit config and run test
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

print("🎯 QUICK BULL TEST SETUP")
print("=" * 40)
print("To force a bull decision, edit your config.py:")
print()
print("1. Open config.py")
print("2. In StrategyConfig class, change:")
print("   bear_threshold_lower: float = 1.0   # Was 12.0")
print("   bull_threshold_upper: float = 2.0   # Was 88.0")
print()
print("3. Run live test mode:")
print("   python live_trading_app.py --live-test --dry-run --data-port 4001")
print()
print("This will:")
print("✅ Force bull decisions (2% threshold vs 88%)")
print("✅ Use safe $0.05 test orders") 
print("✅ Auto-cancel orders after timeout")
print("✅ Generate trade reports")
print()
print("Expected result: BULL spread execution with auto-cancel")
print()
print("Current config values:")

try:
    from config import TradingConfig
    config = TradingConfig()
    print(f"   Bear threshold: {config.strategy.bear_threshold_lower}%")
    print(f"   Bull threshold: {config.strategy.bull_threshold_upper}%")
    print(f"   Live test mode: {config.live_test.enabled}")
    
    if config.strategy.bull_threshold_upper <= 10:
        print("✅ Config already set for bull forcing!")
    else:
        print("⚠️ Config needs modification for bull forcing")
        
except Exception as e:
    print(f"❌ Error reading config: {e}")

print()
print("💡 Alternative: Use the automated script:")
print("   python debug/test_forced_bull.py") 