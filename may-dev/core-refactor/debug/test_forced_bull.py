#!/usr/bin/env python3
"""
Forced Bull Test - Test bull decision with live test mode auto-cancel
"""

import sys
import os
import datetime as dt
import subprocess
import tempfile
import shutil
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_custom_config_for_bull(temp_dir: Path) -> Path:
    """Create a custom config that forces bull decisions"""
    
    config_content = '''#!/usr/bin/env python3
"""
Custom config for forced bull testing
"""

from dataclasses import dataclass
from typing import Optional
import datetime as dt

@dataclass
class StrategyConfig:
    """Strategy decision parameters - Modified for BULL testing"""
    # MODIFIED: Set thresholds to force bull decisions
    bear_threshold_lower: float = 1.0   # Bear if close < 1% (very unlikely)
    bull_threshold_upper: float = 2.0   # Bull if close > 2% (very likely!)
    
    # Keep reversal thresholds same as original
    reversal_bear_threshold: float = 1.0
    reversal_bull_threshold: float = 2.0
    
    # Final reversal thresholds
    final_reversal_bear_threshold: float = 1.0
    final_reversal_bull_threshold: float = 2.0
    
    # Range threshold for narrow conversions
    narrow_range_threshold: float = 4.0
    
    # Iron butterfly thresholds
    iron_butterfly_upper_threshold: float = 70.0
    iron_butterfly_lower_threshold: float = 30.0
    
    # Overshoot thresholds
    spread_overshoot_strikes: int = 1
    iron_butterfly_overshoot_strikes: int = 2
    
    def is_bear_signal_first_bar(self, close_percentage: float) -> bool:
        return close_percentage < self.bear_threshold_lower
    
    def is_bull_signal_first_bar(self, close_percentage: float) -> bool:
        return close_percentage > self.bull_threshold_upper
    
    def is_bear_reversal(self, close_percentage: float) -> bool:
        return close_percentage < self.reversal_bear_threshold
    
    def is_bull_reversal(self, close_percentage: float) -> bool:
        return close_percentage > self.reversal_bull_threshold

@dataclass
class TimingConfig:
    """Timing configuration"""
    reference_minute: int = 50
    spread_retry_1: float = 2.0
    spread_retry_2: float = 5.0
    spread_retry_3: float = 8.0
    
    def get_timing_schedule(self):
        return {
            'first_bar_analysis': {'minute': self.reference_minute, 'second': 30},
            'second_bar_analysis': {'minute': self.reference_minute + 1, 'second': 0},
            'third_bar_analysis': {'minute': self.reference_minute + 1, 'second': 30},
            'spread_execution': {'minute': self.reference_minute + 1, 'second': 31},
            'wide_iron_butterfly_fallback': {'minute': self.reference_minute + 1, 'second': 40},
            'overshoot_check': {'minute': self.reference_minute + 8, 'second': 55},
            'cancel_narrow': {'minute': self.reference_minute + 8, 'second': 59}
        }

@dataclass  
class PricingConfig:
    """Pricing configuration"""
    spread_price_limit: float = 5.50
    narrow_spread_limit: float = 1.60
    wide_iron_butterfly_credit_min: float = 6.50
    narrow_iron_butterfly_credit_min: float = 4.20

@dataclass
class LiveTestConfig:
    """Live test configuration - ENABLED for auto-cancel"""
    enabled: bool = True  # FORCE LIVE TEST MODE
    test_order_price: float = 0.05  # Safe test price
    auto_cancel_seconds: float = 5.0  # Auto-cancel after 5 seconds

@dataclass
class TradingConfig:
    """Main trading configuration"""
    
    def __init__(self):
        self.strategy = StrategyConfig()
        self.timing = TimingConfig()
        self.pricing = PricingConfig()
        self.live_test = LiveTestConfig()
        
        # Force live test mode
        self.live_test.enabled = True
        
        print("🔧 FORCED BULL CONFIG LOADED:")
        print(f"   Bear threshold: {self.strategy.bear_threshold_lower}% (very low)")
        print(f"   Bull threshold: {self.strategy.bull_threshold_upper}% (very low - forces bull)")
        print(f"   Live test mode: {self.live_test.enabled}")
        print(f"   Auto-cancel: {self.live_test.auto_cancel_seconds}s")

def get_global_config():
    return TradingConfig()

def set_global_config(config):
    pass
'''
    
    config_file = temp_dir / "custom_config.py"
    config_file.write_text(config_content)
    return config_file

def run_forced_bull_test():
    """Run a test that forces bull decisions"""
    print("🎯 FORCED BULL TEST WITH AUTO-CANCEL")
    print("=" * 60)
    print(f"Time: {dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Purpose: Test bull execution with live test mode")
    print("=" * 60)
    
    # Create temporary directory for custom config
    temp_dir = Path(tempfile.mkdtemp())
    
    try:
        # Create custom config
        custom_config = create_custom_config_for_bull(temp_dir)
        print(f"📁 Created custom config: {custom_config}")
        
        # Copy to project root with backup
        project_root = Path.cwd()
        original_config = project_root / "config.py"
        backup_config = project_root / "config_backup.py"
        
        # Backup original
        if original_config.exists():
            shutil.copy2(original_config, backup_config)
            print("💾 Backed up original config.py")
        
        # Copy custom config
        shutil.copy2(custom_config, original_config)
        print("🔧 Applied custom bull-forcing config")
        
        print("\n🧪 TESTING OPTIONS:")
        print("1. Simulation mode (safe)")
        print("2. Live test mode with IB connection")
        print("3. Quick integration test")
        
        choice = input("\nChoose test (1-3): ").strip()
        
        if choice == "1":
            print("\n🎮 Running simulation test...")
            cmd = [
                sys.executable, "live_trading_app.py",
                "--simulation", "--dry-run", "--debug"
            ]
            
        elif choice == "2":
            print("\n🔗 Running live test with IB...")
            print("⚠️ Make sure IB Gateway/TWS is running!")
            cmd = [
                sys.executable, "live_trading_app.py", 
                "--live-test", "--dry-run", "--debug",
                "--data-port", "4001"
            ]
            
        elif choice == "3":
            print("\n⚡ Running quick integration test...")
            cmd = [sys.executable, "debug/test_live_integration.py"]
            
        else:
            print("❌ Invalid choice")
            return False
        
        print(f"\n🚀 Executing: {' '.join(cmd)}")
        print("=" * 60)
        
        # Run the test
        try:
            result = subprocess.run(cmd, timeout=60, text=True)
            
            if result.returncode == 0:
                print("\n✅ TEST COMPLETED SUCCESSFULLY!")
                print("💡 Expected result: BULL decision due to forced thresholds")
                print("💡 Live test mode: Orders should auto-cancel after 5 seconds")
                return True
            else:
                print(f"\n❌ TEST FAILED (exit code: {result.returncode})")
                return False
                
        except subprocess.TimeoutExpired:
            print("\n⏰ TEST TIMED OUT (60s)")
            print("💡 This might be normal for live mode - check logs")
            return True
            
        except KeyboardInterrupt:
            print("\n🛑 TEST INTERRUPTED BY USER")
            return True
        
    finally:
        # Restore original config
        if backup_config.exists():
            shutil.copy2(backup_config, original_config)
            backup_config.unlink()
            print("\n🔄 Restored original config.py")
        
        # Cleanup temp directory
        shutil.rmtree(temp_dir)
        print("🧹 Cleaned up temporary files")

def main():
    """Main function"""
    print("🔧 BULL DECISION FORCING TEST")
    print("This test modifies thresholds to force bull decisions")
    print("Bear threshold: 1% (very unlikely)")
    print("Bull threshold: 2% (very likely)")
    print("Live test mode: ON (auto-cancel orders)")
    print("\nThis will:")
    print("1. Backup your config.py")
    print("2. Apply bull-forcing thresholds") 
    print("3. Run test with live test mode")
    print("4. Restore original config")
    
    proceed = input("\nProceed? (y/n): ").strip().lower()
    if proceed != 'y':
        print("❌ Cancelled")
        return False
    
    return run_forced_bull_test()

if __name__ == "__main__":
    success = main()
    
    print(f"\n{'='*60}")
    if success:
        print("✅ FORCED BULL TEST COMPLETED")
        print("💡 Check logs for bull decision and auto-cancelled orders")
    else:
        print("❌ FORCED BULL TEST FAILED")
        print("💡 Check error messages above")
    print(f"{'='*60}")
    
    exit(0 if success else 1) 