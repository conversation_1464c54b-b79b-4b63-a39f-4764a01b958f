#!/usr/bin/env python3
"""
Quick test script to verify extra butterfly output works
"""

import sys
import os
import logging

# Add the backtesting directory to the path
sys.path.append('backtesting')

from historical_backtester import HistoricalBacktester

def test_extra_butterfly_output():
    """Test that extra butterfly mode produces visible output"""
    print("🧪 Testing Extra Butterfly Output")
    print("=" * 50)
    
    # Set up logging to see info messages
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    try:
        # Create backtester with extra butterfly enabled
        print("Creating backtester with extra butterfly mode...")
        backtester = HistoricalBacktester(
            start_date="2025-01-02",
            end_date="2025-01-03",  # Just 2 days for testing
            extra_butterfly=True,
            pricing_mode="basic"
        )
        
        print(f"✅ Extra butterfly enabled: {backtester.extra_butterfly}")
        print(f"✅ Extra butterfly engine: {backtester.extra_butterfly_engine is not None}")
        
        # Run backtest
        print("\nRunning backtest...")
        results = backtester.run_backtest()
        
        print(f"✅ Backtest completed: {len(results)} results")
        
        # Check for extra butterfly data in results
        extra_butterfly_found = False
        for result in results:
            if hasattr(result, 'extra_butterfly_filled') and result.extra_butterfly_filled:
                extra_butterfly_found = True
                print(f"✅ Found extra butterfly trade on {result.trading_date}")
                if hasattr(result, 'extra_butterfly_result') and result.extra_butterfly_result:
                    print(f"   Case: {result.extra_butterfly_result.case}")
                    print(f"   P&L: ${result.extra_butterfly_profit_loss:.2f}")
                break
        
        if not extra_butterfly_found:
            print("ℹ️  No extra butterfly trades found (normal - depends on price movements)")
        
        # Test CSV output
        print("\nTesting CSV output...")
        backtester.save_results_to_csv()
        
        # Test summary
        print("\nTesting summary...")
        backtester.print_summary()
        
        print("\n🎉 Extra Butterfly Output Test Completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test Failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_extra_butterfly_output() 