#!/usr/bin/env python3
"""
Examples demonstrating nickel rounding for option orders
"""

from combo_orders import OptionPricingUtils

def demonstrate_nickel_rounding():
    """Demonstrate how nickel rounding works for different strategies"""
    
    print("=== NICKEL ROUNDING EXAMPLES ===\n")
    
    # Example scenarios with mid prices that aren't nickel multiples
    test_cases = [
        {"mid": 2.17, "description": "Bull spread mid price"},
        {"mid": -4.22, "description": "Iron butterfly mid price (credit)"},
        {"mid": 1.33, "description": "Bear spread mid price"},
        {"mid": -6.77, "description": "Wide iron butterfly mid price (credit)"},
    ]
    
    print("🔸 DEBIT STRATEGIES (Spreads) - Round UP to pay more, ensure fill:")
    for case in test_cases:
        if case["mid"] > 0:  # Debit strategies have positive mid prices
            buffered = case["mid"] + 0.05
            final_price = OptionPricingUtils.calculate_limit_price_with_buffer(
                case["mid"], is_credit_strategy=False, buffer=0.05
            )
            print(f"  {case['description']}")
            print(f"    Mid: ${case['mid']:.2f} → Buffered: ${buffered:.2f} → Final: ${final_price:.2f}")
            print(f"    Validation: {OptionPricingUtils.validate_nickel_price(final_price)}")
            print()
    
    print("\n🔸 CREDIT STRATEGIES (Iron Butterflies) - Round DOWN to accept less, ensure fill:")
    for case in test_cases:
        if case["mid"] < 0:  # Credit strategies have negative mid prices internally
            buffered = case["mid"] + 0.05  # Less negative = less credit
            final_price = OptionPricingUtils.calculate_limit_price_with_buffer(
                case["mid"], is_credit_strategy=True, buffer=0.05
            )
            print(f"  {case['description']}")
            print(f"    Mid: ${case['mid']:.2f} → Buffered: ${buffered:.2f} → Final: ${final_price:.2f}")
            print(f"    Credit received: ${abs(final_price):.2f}")
            print(f"    Validation: {OptionPricingUtils.validate_nickel_price(final_price)}")
            print()
    
    print("\n🔸 MANUAL ROUNDING EXAMPLES:")
    examples = [2.17, 2.22, 2.23, 2.27, 2.28]
    
    print("  Round UP (for debit strategies):")
    for price in examples:
        rounded = OptionPricingUtils.round_to_nickel(price, round_up=True)
        print(f"    ${price:.2f} → ${rounded:.2f}")
    
    print("\n  Round DOWN (for credit strategies):")
    for price in examples:
        rounded = OptionPricingUtils.round_to_nickel(price, round_up=False)
        print(f"    ${price:.2f} → ${rounded:.2f}")
    
    print("\n🔸 VALIDATION EXAMPLES:")
    test_prices = [2.25, 2.20, 2.17, 2.30, 1.05, 0.25]
    for price in test_prices:
        is_valid = OptionPricingUtils.validate_nickel_price(price)
        status = "✅ Valid" if is_valid else "❌ Invalid"
        print(f"    ${price:.2f}: {status}")


if __name__ == "__main__":
    demonstrate_nickel_rounding() 