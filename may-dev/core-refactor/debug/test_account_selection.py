#!/usr/bin/env python3
"""
Test script to verify account selection for <PERSON>
"""

from ib_insync import IB
import sys

def test_account_selection():
    """Test account selection for <PERSON> accounts"""
    
    try:
        print("🔍 Connecting to <PERSON> on port 4017...")
        ib = IB()
        ib.connect('127.0.0.1', 4017, clientId=300)
        
        print("✅ Connected successfully!")
        
        # Get all managed accounts
        accounts = ib.managedAccounts()
        print(f"📋 Available accounts: {accounts}")
        
        # Get current account info
        account_summary = ib.accountSummary()
        
        # Get account number from the first account summary item
        account_number = None
        if account_summary:
            account_number = account_summary[0].account
            print(f"📊 Account Number: {account_number}")
        
        # Find the net liquidation value
        balance = None
        for item in account_summary:
            if item.tag == 'NetLiquidation' and item.currency == 'USD':
                balance = float(item.value)
                break
        
        if balance:
            print(f"💰 Current balance: ${balance:,.2f}")
            
            # Show additional account details
            print(f"📋 Account Details:")
            for item in account_summary:
                if item.tag in ['AccountCode', 'AccountType', 'AccountOrGroup']:
                    print(f"   {item.tag}: {item.value}")
            
            # Determine which account this likely is
            if balance > 7000:
                print(f"📝 This appears to be the $7000+ account")
                print(f"💡 To test the $70 account, you need to:")
                print(f"   1. Open IB Gateway/TWS")
                print(f"   2. Switch to the other account")
                print(f"   3. Run this test again")
            elif balance < 100:
                print(f"📝 This appears to be the $70 account")
                print(f"💡 To test the $7000 account, you need to:")
                print(f"   1. Open IB Gateway/TWS")
                print(f"   2. Switch to the other account")
                print(f"   3. Run this test again")
            else:
                print(f"📝 Unknown account type")
        else:
            print(f"❌ Could not get balance")
        
        ib.disconnect()
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    print(f"\n💡 Manual Account Testing:")
    print(f"   1. Open IB Gateway/TWS")
    print(f"   2. Switch to the $70 account")
    print(f"   3. Run: python3 test_account_selection.py")
    print(f"   4. Note the balance and account ID")
    print(f"   5. Switch to the $7000 account")
    print(f"   6. Run the test again")
    print(f"   7. Update the scripts with the correct account IDs")

if __name__ == "__main__":
    test_account_selection() 