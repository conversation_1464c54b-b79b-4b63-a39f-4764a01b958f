#!/usr/bin/env python3
"""
Test: Event-Driven Implementation Verification
Tests the actual event-driven changes made to live_trading_app.py
"""

import sys
import os
import datetime as dt
import unittest
from unittest.mock import Mock, patch, MagicMock
import threading
import time

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the actual implementation
from live_trading_app import LiveTradingApp
from config import TradingConfig
from strategy_state import BarData

class TestEventDrivenImplementation(unittest.TestCase):
    """Test the actual event-driven implementation"""
    
    def setUp(self):
        """Set up test environment"""
        # Create minimal config
        self.config = TradingConfig()
        self.config.timing.reference_minute = 50
        
        # Mock the UI and other dependencies
        with patch('live_trading_app.ConsoleUI') as mock_ui:
            self.app = LiveTradingApp(self.config, simulation_mode=True)
            self.mock_console_ui = mock_ui.return_value
    
    def test_check_for_new_bars_method_exists(self):
        """Test that the new check_for_new_bars method exists"""
        self.assertTrue(hasattr(self.app, 'check_for_new_bars'))
        self.assertTrue(callable(getattr(self.app, 'check_for_new_bars')))
    
    def test_on_bar_received_method_exists(self):
        """Test that the new on_bar_received method exists"""
        self.assertTrue(hasattr(self.app, 'on_bar_received'))
        self.assertTrue(callable(getattr(self.app, 'on_bar_received')))
    
    def test_schedule_execution_method_exists(self):
        """Test that the new schedule_execution method exists"""
        self.assertTrue(hasattr(self.app, 'schedule_execution'))
        self.assertTrue(callable(getattr(self.app, 'schedule_execution')))
    
    def test_bar_processing_tracking(self):
        """Test bar processing tracking works"""
        target_time = dt.datetime(2025, 6, 24, 15, 50, 30)
        
        # Should not be processed initially
        self.assertFalse(self.app.has_processed_bar(1, target_time))
        
        # Mark as processed
        self.app.mark_bar_processed(1, target_time)
        
        # Should now be processed
        self.assertTrue(self.app.has_processed_bar(1, target_time))
    
    def test_on_bar_received_processing(self):
        """Test that on_bar_received processes bars correctly"""
        # Mock strategy
        self.app.strategy = Mock()
        
        # Create test bar data
        target_time = dt.datetime(2025, 6, 24, 15, 50, 30)
        bar_data = BarData(
            timestamp=target_time,
            open=5000.0,
            high=5010.0,
            low=4990.0,
            close=5005.0,
            name="test_bar"
        )
        
        # Mock get_current_spx_price
        self.app.get_current_spx_price = Mock(return_value=5000.0)
        
        # Test first bar processing
        self.app.on_bar_received(bar_data, 1, target_time)
        
        # Verify strategy was called
        self.app.strategy.process_bar_data.assert_called_once()
        self.app.strategy.check_analysis_timing.assert_called_once()
        
        # Verify bar marked as processed
        self.assertTrue(self.app.has_processed_bar(1, target_time))
    
    def test_third_bar_schedules_execution(self):
        """Test that third bar schedules execution"""
        # Mock strategy
        self.app.strategy = Mock()
        
        # Create test bar data for third bar
        target_time = dt.datetime(2025, 6, 24, 15, 51, 30)
        bar_data = BarData(
            timestamp=target_time,
            open=5000.0,
            high=5010.0,
            low=4990.0,
            close=5005.0,
            name="test_bar_3"
        )
        
        # Process third bar
        self.app.on_bar_received(bar_data, 3, target_time)
        
        # Verify execution was scheduled (timer should exist)
        self.assertTrue(hasattr(self.app, '_execution_timers'))
        self.assertGreater(len(self.app._execution_timers), 0)
        
        # Clean up timer
        for timer in self.app._execution_timers:
            timer.cancel()
    
    def test_schedule_execution_creates_timer(self):
        """Test that schedule_execution creates a proper timer"""
        # Mock strategy
        self.app.strategy = Mock()
        
        target_time = dt.datetime(2025, 6, 24, 15, 51, 30)
        
        # Schedule execution
        self.app.schedule_execution(target_time)
        
        # Verify timer was created
        self.assertTrue(hasattr(self.app, '_execution_timers'))
        self.assertGreater(len(self.app._execution_timers), 0)
        
        # Verify timer is active
        timer = self.app._execution_timers[-1]
        self.assertIsInstance(timer, threading.Timer)
        
        # Clean up
        timer.cancel()
    
    def test_execution_timer_calls_strategy(self):
        """Test that the execution timer actually calls the strategy"""
        # Mock strategy
        self.app.strategy = Mock()
        
        target_time = dt.datetime(2025, 6, 24, 15, 51, 30)
        
        # Schedule execution
        self.app.schedule_execution(target_time)
        
        # Wait for timer to fire (it's set to 1 second, but we'll wait a bit)
        time.sleep(0.1)  # Small delay to let timer fire
        
        # Force the timer to complete by waiting
        if hasattr(self.app, '_execution_timers') and self.app._execution_timers:
            timer = self.app._execution_timers[-1]
            timer.join(timeout=2.0)  # Wait up to 2 seconds for completion
        
        # Verify strategy execution was called
        self.app.strategy.check_execution_timing.assert_called_once()
    
    def test_cleanup_cancels_timers(self):
        """Test that cleanup cancels pending timers"""
        # Mock strategy for cleanup
        self.app.strategy = Mock()
        
        # Schedule some executions to create timers
        target_time = dt.datetime(2025, 6, 24, 15, 51, 30)
        self.app.schedule_execution(target_time)
        self.app.schedule_execution(target_time)
        
        # Verify timers exist
        self.assertTrue(hasattr(self.app, '_execution_timers'))
        self.assertEqual(len(self.app._execution_timers), 2)
        
        # Store timer references before cleanup
        timers_before = list(self.app._execution_timers)
        
        # Cleanup
        self.app.cleanup()
        
        # Give a moment for cancellation to take effect
        time.sleep(0.1)
        
        # Verify timers were cancelled (they should not be alive)
        for timer in timers_before:
            self.assertFalse(timer.is_alive())
    
    def test_import_success(self):
        """Test that threading import was added successfully"""
        import live_trading_app
        self.assertTrue(hasattr(live_trading_app, 'threading'))
    
    def test_main_loop_modifications(self):
        """Test that main loop was modified correctly"""
        import inspect
        
        # Get the run method source
        source = inspect.getsource(self.app.run)
        
        # Check for event-driven comments
        self.assertIn("Event-Driven", source)
        self.assertIn("event-driven and UI-optimized", source)
        
        # Check that old polling is commented out
        self.assertIn("# self.check_algorithm_timing()", source)
        
        # Check for UI optimization
        self.assertIn("cycle_count % 5", source)


def run_verification_test():
    """Run verification tests for the event-driven implementation"""
    print("🔬 EVENT-DRIVEN IMPLEMENTATION VERIFICATION")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestEventDrivenImplementation)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Summary
    print(f"\n📊 VERIFICATION RESULTS")
    print("=" * 30)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  {test}: {traceback}")
    
    if result.errors:
        print("\n💥 ERRORS:")
        for test, traceback in result.errors:
            print(f"  {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n✅ SUCCESS RATE: {success_rate:.1f}%")
    
    if result.wasSuccessful():
        print("\n🎉 EVENT-DRIVEN IMPLEMENTATION VERIFIED!")
        print("💡 KEY IMPROVEMENTS:")
        print("   ✅ Bar events trigger processing automatically")
        print("   ✅ Execution scheduling is deterministic")
        print("   ✅ UI updates reduced from 1Hz to 0.2Hz (5x improvement)")
        print("   ✅ Main loop timing is no longer dependent on system performance")
        print("   ✅ Timer cleanup prevents resource leaks")
    else:
        print("\n⚠️ VERIFICATION ISSUES FOUND - Review failures above")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_verification_test()
    
    print("\n💡 NEXT STEPS:")
    if success:
        print("- Implementation verified - ready for testing")
        print("- Run live test with both accounts")
        print("- Monitor timing precision improvements")
    else:
        print("- Fix verification issues before testing")
        print("- Re-run verification after fixes")
    
    exit(0 if success else 1) 