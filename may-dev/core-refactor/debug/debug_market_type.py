#!/usr/bin/env python3
import sys
sys.path.append('core-refactor')
from ib_insync import IB, Option, Index
import time

def check_market_data_type():
    """Check what type of market data we're receiving"""
    
    ib = IB()
    try:
        ib.connect('127.0.0.1', 4015, clientId=1, timeout=30)
        print("✅ Connected to IB")
        
        # Request market data type info
        print("\n=== Market Data Type Check ===")
        
        # Check what market data types are available
        # Type 1 = Live, Type 2 = Frozen, Type 3 = Delayed, Type 4 = Delayed-Frozen
        print("Current market data type setting...")
        
        # Try to request real-time market data
        print("\n=== Requesting Real-Time Market Data ===")
        ib.reqMarketDataType(1)  # Request live market data
        print("✅ Requested live market data (type 1)")
        
        time.sleep(2)
        
        # Test SPX index with longer wait
        print("\n=== SPX Index Test (Extended Wait) ===")
        spx_index = Index('SPX', 'CBOE')
        qualified_index = ib.qualifyContracts(spx_index)
        
        if qualified_index:
            ticker = ib.reqMktData(qualified_index[0])
            print("Waiting 10 seconds for market data...")
            
            for i in range(10):
                ib.sleep(1)
                print(f"  {i+1}s: Price={ticker.last}, Bid={ticker.bid}, Ask={ticker.ask}")
                if ticker.last and not str(ticker.last).lower() == 'nan':
                    print("✅ Got valid SPX data!")
                    break
            else:
                print("❌ Still no valid SPX data after 10 seconds")
            
            ib.cancelMktData(qualified_index[0])
        
        # Test individual option with extended wait
        print("\n=== Single Option Test (Extended Wait) ===")
        option_contract = Option(
            symbol='SPX',
            lastTradeDateOrContractMonth='20250612',
            strike=6020.0,
            right='C',
            exchange='SMART',
            tradingClass='SPXW'
        )
        
        qualified_options = ib.qualifyContracts(option_contract)
        if qualified_options:
            ticker = ib.reqMktData(qualified_options[0])
            print("Waiting 10 seconds for option data...")
            
            for i in range(10):
                ib.sleep(1)
                print(f"  {i+1}s: Bid={ticker.bid}, Ask={ticker.ask}, Last={ticker.last}")
                if ticker.bid and not str(ticker.bid).lower() == 'nan':
                    print("✅ Got valid option data!")
                    break
            else:
                print("❌ Still no valid option data after 10 seconds")
            
            ib.cancelMktData(qualified_options[0])
        
        # Check account market data permissions
        print("\n=== Account Market Data Permissions ===")
        try:
            # This might show market data permissions
            managed_accounts = ib.managedAccounts()
            print(f"Managed accounts: {managed_accounts}")
            
            # Try to get account info that might show data permissions
            account_values = ib.accountValues()
            data_permissions = [av for av in account_values if 'Data' in av.tag or 'Market' in av.tag]
            
            if data_permissions:
                print("Market data related settings:")
                for av in data_permissions:
                    print(f"  {av.tag}: {av.value}")
            else:
                print("No explicit market data permission info found")
                
        except Exception as e:
            print(f"Account info error: {e}")
        
        print(f"\n=== Summary ===")
        print(f"Try different market data types:")
        print(f"Type 1 = Live")
        print(f"Type 2 = Frozen") 
        print(f"Type 3 = Delayed")
        print(f"Type 4 = Delayed-Frozen")
        
        ib.disconnect()
        print("\n✅ Disconnected from IB")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_market_data_type() 