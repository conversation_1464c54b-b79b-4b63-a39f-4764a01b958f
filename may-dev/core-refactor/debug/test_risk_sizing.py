#!/usr/bin/env python3
"""
Test script to verify risk sizing is working correctly
"""

import sys
import math
from config import TradingConfig
from combo_orders import ComboOrderFactory, ComboType

def test_risk_percentage_assignment():
    """Test that strategies get correct risk percentages"""
    print("🧪 TEST 1: RISK PERCENTAGE ASSIGNMENT")
    print("=" * 60)
    
    config = TradingConfig()
    
    # Expected assignments
    expected_8pct = [
        'bull_spread', 'narrow_bull_spread', 
        'one_better_narrow_bull', 'overshoot_reversal_bull'
    ]
    expected_7pct = [
        'bear_spread', 'narrow_bear_spread', 'overshoot_reversal_bear',
        'iron_butterfly', 'wide_iron_butterfly', 'narrow_iron_butterfly'
    ]
    
    all_correct = True
    
    print("📊 Bull Strategies (Expected: 8%)")
    for strategy in expected_8pct:
        risk = config.pricing.get_strategy_risk_percentage(strategy)
        status = "✅" if risk == 8.0 else "❌"
        if risk != 8.0:
            all_correct = False
        print(f"  {status} {strategy:<25}: {risk}%")
    
    print("\n📊 Other Strategies (Expected: 7%)")
    for strategy in expected_7pct:
        risk = config.pricing.get_strategy_risk_percentage(strategy)
        status = "✅" if risk == 7.0 else "❌"
        if risk != 7.0:
            all_correct = False
        print(f"  {status} {strategy:<25}: {risk}%")
    
    print(f"\n🎯 Overall Result: {'✅ PASS' if all_correct else '❌ FAIL'}")
    return all_correct

def test_order_size_calculations():
    """Test order size calculations with different scenarios"""
    print("\n🧪 TEST 2: ORDER SIZE CALCULATIONS")
    print("=" * 60)
    
    config = TradingConfig()
    
    # Test scenarios
    test_cases = [
        {
            'name': 'Bull Spread - $6000 balance, $2.30 price',
            'combo_type': ComboType.BULL_SPREAD,
            'wallet_balance': 6000.0,
            'limit_price': 2.30,
            'expected_risk_pct': 8.0,
            'expected_order_size': 2  # $480 / $230 = 2.08 → 2
        },
        {
            'name': 'Bear Spread - $6000 balance, $2.30 price', 
            'combo_type': ComboType.BEAR_SPREAD,
            'wallet_balance': 6000.0,
            'limit_price': 2.30,
            'expected_risk_pct': 7.0,
            'expected_order_size': 1  # $420 / $230 = 1.82 → 1
        },
        {
            'name': 'Narrow Bull - $6000 balance, $2.05 price',
            'combo_type': ComboType.NARROW_BULL_SPREAD,
            'wallet_balance': 6000.0,
            'limit_price': 2.05,
            'expected_risk_pct': 8.0,
            'expected_order_size': 2  # $480 / $205 = 2.34 → 2
        },
                 {
             'name': 'Iron Butterfly - $6000 balance, $3.40 credit',
             'combo_type': ComboType.IRON_BUTTERFLY,
             'wallet_balance': 6000.0,
             'limit_price': -3.40,  # Credit strategy
             'expected_risk_pct': 7.0,
             'expected_order_size': 1  # $420 / $660 = 0.636 → 1 (10pt wing - 3.40 credit = 6.60 risk)
         },
         {
             'name': 'Narrow Iron Butterfly - $6000 balance, $3.40 credit',
             'combo_type': ComboType.NARROW_IRON_BUTTERFLY,
             'wallet_balance': 6000.0,
             'limit_price': -3.40,  # Credit strategy
             'expected_risk_pct': 7.0,
             'expected_order_size': 2  # $420 / $160 = 2.625 → 2 (5pt wing - 3.40 credit = 1.60 risk)
         }
    ]
    
    all_correct = True
    
    for test_case in test_cases:
        print(f"\n📋 {test_case['name']}")
        print("-" * 50)
        
        # Get risk percentage
        risk_pct = config.pricing.get_strategy_risk_percentage(test_case['combo_type'].value)
        risk_budget = test_case['wallet_balance'] * (risk_pct / 100.0)
        
        # Calculate per-contract risk
        if test_case['combo_type'] in [ComboType.IRON_BUTTERFLY, ComboType.NARROW_IRON_BUTTERFLY, ComboType.WIDE_IRON_BUTTERFLY]:
            # Credit strategy: Risk = (wing_width - credit_received) * 100
            wing_width = 5.0 if 'narrow' in test_case['combo_type'].value else 10.0
            credit_received = abs(test_case['limit_price'])
            max_loss = wing_width - credit_received
            per_contract_risk = max_loss * 100
        else:
            # Debit strategy: Risk = premium paid * 100
            per_contract_risk = abs(test_case['limit_price']) * 100
        
        # Calculate order size
        order_size = math.floor(risk_budget / per_contract_risk) if per_contract_risk > 0 else 1
        order_size = max(1, order_size)
        
        # Check results
        risk_ok = risk_pct == test_case['expected_risk_pct']
        size_ok = order_size == test_case['expected_order_size']
        
        print(f"  💰 Wallet Balance: ${test_case['wallet_balance']:,.2f}")
        print(f"  📊 Risk Percentage: {risk_pct}% {'✅' if risk_ok else '❌'} (expected {test_case['expected_risk_pct']}%)")
        print(f"  💵 Risk Budget: ${risk_budget:.2f}")
        print(f"  💸 Per-Contract Risk: ${per_contract_risk:.2f}")
        print(f"  📦 Order Size: {order_size} contracts {'✅' if size_ok else '❌'} (expected {test_case['expected_order_size']})")
        
        if not (risk_ok and size_ok):
            all_correct = False
    
    print(f"\n🎯 Overall Result: {'✅ PASS' if all_correct else '❌ FAIL'}")
    return all_correct

def test_execution_engine_integration():
    """Test that execution engine would use the correct risk sizing"""
    print("\n🧪 TEST 3: EXECUTION ENGINE INTEGRATION")
    print("=" * 60)
    
    try:
        from execution_engine import OptionExecutionEngine
        from unittest.mock import Mock
        
        # Create mock IB connection
        mock_ib = Mock()
        config = TradingConfig()
        
        # Create execution engine
        engine = OptionExecutionEngine(mock_ib)
        engine.config = config
        engine.wallet_balance = 6000.0  # Set test balance
        
        # Test bull spread combo
        bull_combo = ComboOrderFactory.create_bull_spread(6180.0)
        limit_price = 2.30
        
        # Calculate expected values
        expected_risk_pct = 8.0
        expected_risk_budget = 6000.0 * 0.08  # $480
        expected_per_contract_risk = 230.0  # $2.30 * 100
        expected_order_size = 2  # floor(480/230) = 2
        
        # Test the actual method
        calculated_size = engine.get_risk_adjusted_order_size(limit_price, bull_combo)
        
        print(f"📊 Bull Spread Test:")
        print(f"  Expected Order Size: {expected_order_size}")
        print(f"  Calculated Order Size: {calculated_size}")
        print(f"  Result: {'✅ PASS' if calculated_size == expected_order_size else '❌ FAIL'}")
        
        # Test bear spread combo  
        bear_combo = ComboOrderFactory.create_bear_spread(6180.0)
        
        expected_risk_pct_bear = 7.0
        expected_risk_budget_bear = 6000.0 * 0.07  # $420
        expected_order_size_bear = 1  # floor(420/230) = 1
        
        calculated_size_bear = engine.get_risk_adjusted_order_size(limit_price, bear_combo)
        
        print(f"\n📊 Bear Spread Test:")
        print(f"  Expected Order Size: {expected_order_size_bear}")
        print(f"  Calculated Order Size: {calculated_size_bear}")
        print(f"  Result: {'✅ PASS' if calculated_size_bear == expected_order_size_bear else '❌ FAIL'}")
        
        integration_ok = (calculated_size == expected_order_size and 
                         calculated_size_bear == expected_order_size_bear)
        
        print(f"\n🎯 Integration Result: {'✅ PASS' if integration_ok else '❌ FAIL'}")
        return integration_ok
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def main():
    """Run all risk sizing tests"""
    print("🔬 COMPREHENSIVE RISK SIZING VERIFICATION")
    print("=" * 70)
    
    results = []
    
    # Run all tests
    results.append(test_risk_percentage_assignment())
    results.append(test_order_size_calculations()) 
    results.append(test_execution_engine_integration())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n🏁 FINAL RESULTS")
    print("=" * 30)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Overall Status: {'✅ ALL TESTS PASS' if passed == total else '❌ SOME TESTS FAILED'}")
    
    if passed == total:
        print("\n🎉 Risk sizing is working correctly!")
        print("   • Bull strategies get 8% risk allocation")
        print("   • Other strategies get 7% risk allocation")
        print("   • Order sizes are calculated properly")
        print("   • Execution engine integration works")
    else:
        print("\n⚠️  Risk sizing has issues that need fixing!")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 