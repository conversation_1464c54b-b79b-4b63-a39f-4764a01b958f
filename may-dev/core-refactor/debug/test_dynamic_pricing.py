#!/usr/bin/env python3
"""
Test Script for Dynamic Pricing Logic
Tests the calculate_limit_price_with_buffer function with various scenarios
"""

import sys
import os
import logging

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from combo_orders import OptionPricingUtils, ComboOrderFactory, ComboType

# Set up logging to see the detailed pricing calculations
logging.basicConfig(level=logging.INFO, format='%(message)s')

def test_pricing_scenarios():
    """Test various pricing scenarios"""
    
    print("=" * 80)
    print("🧪 DYNAMIC PRICING TEST SCENARIOS")
    print("=" * 80)
    
    # Test scenarios: (mid_price, is_credit, expected_result_approx, description)
    test_cases = [
        # Credit strategies (butterflies)
        (-4.30, True, -4.25, "Narrow Butterfly: $4.30 credit mid → $4.25 limit"),
        (-7.00, True, -6.95, "Wide Butterfly: $7.00 credit mid → $6.95 limit"),
        (-3.47, True, -3.40, "Credit with odd mid-price"),  # -3.47 + 0.05 = -3.42 → round down to -3.40
        (-6.52, True, -6.45, "Credit rounding down to nickel"),  # -6.52 + 0.05 = -6.47 → round down to -6.45
        
        # Debit strategies (spreads)
        (1.90, False, 1.95, "Bull/Bear Spread: $1.90 debit mid → $1.95 limit"),
        (2.37, False, 2.45, "Spread with odd mid-price"),
        (1.83, False, 1.90, "Debit rounding up to nickel"),
        (0.67, False, 0.75, "Small debit spread"),
        
        # Edge cases
        (-5.00, True, -4.95, "Round credit number"),
        (2.00, False, 2.05, "Round debit number"),
        (-3.33, True, -3.25, "Credit with cents"),  # -3.33 + 0.05 = -3.28 → round down to -3.25
        (1.77, False, 1.85, "Debit with cents"),
    ]
    
    print(f"\n📊 Testing {len(test_cases)} scenarios:\n")
    
    all_passed = True
    
    for i, (mid_price, is_credit, expected, description) in enumerate(test_cases, 1):
        print(f"[{i:2d}] {description}")
        print(f"     Input: Mid=${mid_price:.2f}, Credit={is_credit}")
        
        # Calculate the limit price
        result = OptionPricingUtils.calculate_limit_price_with_buffer(
            mid_price, is_credit, buffer=0.05
        )
        
        # Check if result matches expected (within $0.01)
        passed = abs(result - expected) < 0.01
        status = "✅ PASS" if passed else "❌ FAIL"
        
        print(f"     Result: ${result:.2f} (expected ~${expected:.2f}) {status}")
        
        if not passed:
            all_passed = False
            print(f"     ERROR: Expected ${expected:.2f}, got ${result:.2f}")
        
        print()
    
    print("=" * 80)
    if all_passed:
        print("🎉 ALL TESTS PASSED - Dynamic pricing logic working correctly!")
    else:
        print("❌ SOME TESTS FAILED - Check pricing logic")
    print("=" * 80)
    
    return all_passed

def test_nickel_validation():
    """Test that all results are proper nickel increments"""
    print("\n🪙 NICKEL VALIDATION TEST")
    print("-" * 40)
    
    test_prices = [-4.30, -7.15, 1.90, 2.37, -3.47, 0.67]
    
    all_valid = True
    for price in test_prices:
        for is_credit in [True, False]:
            result = OptionPricingUtils.calculate_limit_price_with_buffer(price, is_credit)
            is_nickel = OptionPricingUtils.validate_nickel_price(result)
            
            status = "✅" if is_nickel else "❌"
            print(f"{status} ${result:.2f} ({'Credit' if is_credit else 'Debit'})")
            
            if not is_nickel:
                all_valid = False
    
    if all_valid:
        print("🎉 All prices are valid nickel increments!")
    else:
        print("❌ Some prices are not nickel increments!")
    
    return all_valid

def test_real_market_examples():
    """Test with real market scenarios"""
    print("\n📈 REAL MARKET EXAMPLE TESTS")
    print("-" * 40)
    
    # Real examples from your trading
    examples = [
        {
            'strategy': 'Narrow Iron Butterfly',
            'bid': 4.20, 'ask': 4.40,
            'expected_behavior': 'Should offer less than mid to get filled'
        },
        {
            'strategy': 'Wide Iron Butterfly', 
            'bid': 6.80, 'ask': 7.20,
            'expected_behavior': 'Should offer less than mid to get filled'
        },
        {
            'strategy': 'Bull Spread',
            'bid': 1.80, 'ask': 2.00,
            'expected_behavior': 'Should pay more than mid to get filled'
        }
    ]
    
    for example in examples:
        print(f"\n{example['strategy']}:")
        bid, ask = example['bid'], example['ask']
        mid = (bid + ask) / 2
        is_credit = 'butterfly' in example['strategy'].lower()
        
        print(f"  Market: Bid=${bid:.2f}, Ask=${ask:.2f}, Mid=${mid:.2f}")
        
        result = OptionPricingUtils.calculate_limit_price_with_buffer(
            -mid if is_credit else mid,  # Negative for credit strategies
            is_credit_strategy=is_credit
        )
        
        print(f"  Our Limit: ${abs(result):.2f}")
        print(f"  Logic: {example['expected_behavior']}")
        
        if is_credit:
            print(f"  Credit Strategy: Offering ${abs(result):.2f} vs ${mid:.2f} mid (giving up ${mid - abs(result):.2f})")
        else:
            print(f"  Debit Strategy: Paying ${result:.2f} vs ${mid:.2f} mid (extra ${result - mid:.2f})")

def test_risk_adjusted_order_sizing():
    """Test the ACTUAL risk-adjusted order sizing in OptionExecutionEngine"""
    print("=== REAL RISK-ADJUSTED ORDER SIZING TESTS ===")
    
    # Import the real execution engine
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from execution_engine import OptionExecutionEngine
    from config import TradingConfig
    
    # Create a mock IB connection for testing
    class MockIB:
        def accountSummary(self):
            # Mock account summary with test wallet balance
            class MockAccountItem:
                def __init__(self, tag, value, currency):
                    self.tag = tag
                    self.value = value
                    self.currency = currency
            
            return [
                MockAccountItem('NetLiquidation', '6000.00', 'USD'),
                MockAccountItem('TotalCashValue', '5500.00', 'USD')
            ]
        
        def reqMarketDataType(self, market_data_type):
            # Mock method to avoid the attribute error
            pass
    
    # Test scenarios from the user's examples
    test_cases = [
        {
            'name': 'Bull Spread - 2.20 debit with $6000 wallet',
            'wallet_balance': 6000,
            'risk_percentage': 5.0,
            'limit_price': 2.20,
            'combo_factory': lambda: ComboOrderFactory.create_bull_spread(5990),
            'expected_contracts': 1  # 300/220 = 1.36 → floor(1)
        },
        {
            'name': 'Bull Spread - 0.75 debit with $6000 wallet',
            'wallet_balance': 6000,
            'risk_percentage': 5.0,
            'limit_price': 0.75,
            'combo_factory': lambda: ComboOrderFactory.create_bull_spread(5990),
            'expected_contracts': 4  # 300/75 = 4
        },
        {
            'name': 'Wide Butterfly - 8.50 credit with $6000 wallet',
            'wallet_balance': 6000,
            'risk_percentage': 5.0,
            'limit_price': -8.50,  # Credit strategies use negative prices
            'combo_factory': lambda: ComboOrderFactory.create_wide_iron_butterfly(5990),
            'expected_contracts': 2  # Risk: (10-8.50)*100=150, 300/150=2
        },
        {
            'name': 'Narrow Butterfly - 4.00 credit with $6000 wallet',
            'wallet_balance': 6000,
            'risk_percentage': 5.0,
            'limit_price': -4.00,  # Credit strategies use negative prices
            'combo_factory': lambda: ComboOrderFactory.create_narrow_iron_butterfly(5990),
            'expected_contracts': 3  # Risk: (5-4)*100=100, 300/100=3
        },
        {
            'name': 'High priced spread with small wallet',
            'wallet_balance': 1000,  # Only $1000 wallet
            'risk_percentage': 5.0,
            'limit_price': 2.50,
            'combo_factory': lambda: ComboOrderFactory.create_bull_spread(5990),
            'expected_contracts': 1  # Risk: 250, budget: 50, 50/250 = 0.2 → max(1,0) = 1
        },
        {
            'name': 'Test with 6% risk (user changed config)',
            'wallet_balance': 6000,
            'risk_percentage': 6.0,  # User changed to 6% in config
            'limit_price': 2.20,
            'combo_factory': lambda: ComboOrderFactory.create_bull_spread(5990),
            'expected_contracts': 1  # 360/220 = 1.63 → floor(1)
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n[{i}] {test_case['name']}")
        print("-" * 60)
        
        # Create real execution engine with mock IB
        mock_ib = MockIB()
        
        # Create config with test risk percentage
        config = TradingConfig()
        config.pricing.order_size_risk_percentage = test_case['risk_percentage']
        
        # Create execution engine
        engine = OptionExecutionEngine(mock_ib, db_handler=None)
        engine.config = config
        engine.wallet_balance = test_case['wallet_balance']  # Override wallet balance for test
        
        # Create combo definition using the real factory
        combo_def = test_case['combo_factory']()
        
        # Test the REAL get_risk_adjusted_order_size method
        calculated_size = engine.get_risk_adjusted_order_size(test_case['limit_price'], combo_def)
        
        # Calculate details for display
        risk_budget = test_case['wallet_balance'] * (test_case['risk_percentage'] / 100.0)
        per_contract_risk = engine._calculate_per_contract_risk(test_case['limit_price'], combo_def)
        
        print(f"💰 Wallet Balance: ${test_case['wallet_balance']:,.2f}")
        print(f"📊 Risk Budget ({test_case['risk_percentage']}%): ${risk_budget:.2f}")
        print(f"💵 Limit Price: ${test_case['limit_price']:.2f}")
        print(f"⚠️ Per-Contract Risk: ${per_contract_risk:.2f}")
        print(f"🎯 Expected Contracts: {test_case['expected_contracts']}")
        print(f"🧮 Calculated Contracts: {calculated_size}")
        print(f"🔧 Strategy Type: {combo_def.combo_type.value}")
        print(f"💳 Credit Strategy: {combo_def.is_credit_strategy}")
        
        # Check if result matches expectation
        if calculated_size == test_case['expected_contracts']:
            print("✅ PASS - Calculation matches expected result")
        else:
            print("❌ FAIL - Calculation does not match expected result")
            # Show calculation details for debugging
            print(f"   Debug: {risk_budget:.2f} ÷ {per_contract_risk:.2f} = {risk_budget/per_contract_risk:.2f}")
    
    print("\n" + "=" * 60)
    print("✅ REAL risk-adjusted order sizing tests completed")

def test_limit_price_calculation():
    """Test the calculate_limit_price_with_buffer function"""
    print("=== LIMIT PRICE CALCULATION TESTS ===")
    
    test_cases = [
        # Credit strategies (iron butterflies)
        {
            'mid_price': -3.75,
            'is_credit': True,
            'expected_range': (-3.70, -3.75),  # Should be around -3.70 after buffer and rounding
            'description': 'Credit strategy with -3.75 mid'
        },
        {
            'mid_price': -6.25,
            'is_credit': True,
            'expected_range': (-6.20, -6.25),
            'description': 'Credit strategy with -6.25 mid'
        },
        
        # Debit strategies (spreads)
        {
            'mid_price': 2.13,
            'is_credit': False,
            'expected_range': (2.15, 2.25),  # Should round up to nearest nickel
            'description': 'Debit strategy with 2.13 mid'
        },
        {
            'mid_price': 0.73,
            'is_credit': False,
            'expected_range': (0.75, 0.85),
            'description': 'Debit strategy with 0.73 mid'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n[{i}] {test_case['description']}")
        print("-" * 50)
        
        result = OptionPricingUtils.calculate_limit_price_with_buffer(
            test_case['mid_price'], 
            test_case['is_credit'], 
            buffer=0.05
        )
        
        print(f"Mid Price: ${test_case['mid_price']:.2f}")
        print(f"Strategy: {'CREDIT' if test_case['is_credit'] else 'DEBIT'}")
        print(f"Result: ${result:.2f}")
        print(f"Expected Range: ${test_case['expected_range'][0]:.2f} to ${test_case['expected_range'][1]:.2f}")
        
        # Check if result is in expected range
        min_expected, max_expected = test_case['expected_range']
        if min_expected <= result <= max_expected:
            print("✅ PASS - Result within expected range")
        else:
            print("❌ FAIL - Result outside expected range")
        
        # Check nickel rounding
        if OptionPricingUtils.validate_nickel_price(result):
            print("✅ PASS - Price is nickel-rounded")
        else:
            print("❌ FAIL - Price is not nickel-rounded")

if __name__ == "__main__":
    success1 = test_pricing_scenarios()
    success2 = test_nickel_validation()
    test_real_market_examples()
    test_risk_adjusted_order_sizing()
    print("\n")
    test_limit_price_calculation()
    
    if success1 and success2:
        print(f"\n🎉 ALL VALIDATION TESTS PASSED!")
        sys.exit(0)
    else:
        print(f"\n❌ SOME TESTS FAILED!")
        sys.exit(1) 