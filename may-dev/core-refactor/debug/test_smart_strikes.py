#!/usr/bin/env python3
"""
Test Smart Strike Selection Logic
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from combo_orders import StrikeCalculator

def test_smart_strike_selection():
    """Test the new smart strike selection logic"""
    
    print("=== TESTING SMART STRIKE SELECTION ===\n")
    
    # Test SPX price of 6036 (like in the trade report)
    spx_price = 6036.0
    print(f"SPX Price: {spx_price:.2f}")
    print(f"ATM Strike: {StrikeCalculator.get_atm_strike(spx_price):.0f}")
    
    print("\n--- BULL SPREAD STRIKE SEQUENCE ---")
    bull_strikes = StrikeCalculator.get_bull_strike_sequence(spx_price)
    for i, (buy_strike, sell_strike) in enumerate(bull_strikes, 1):
        print(f"  Attempt {i}: Buy {buy_strike:.0f}C, Sell {sell_strike:.0f}C")
    
    print("\n--- BEAR SPREAD STRIKE SEQUENCE ---")
    bear_strikes = StrikeCalculator.get_bear_strike_sequence(spx_price)
    for i, (buy_strike, sell_strike) in enumerate(bear_strikes, 1):
        print(f"  Attempt {i}: Buy {buy_strike:.0f}P, Sell {sell_strike:.0f}P")
    

    print("\n--- TEST DIFFERENT SPX PRICES ---")
    test_prices = [6001.3, 6003.7, 6006.8, 6031.2, 6036.0, 6038.9, 6041.5, 6042.50, 6050.00]
    
    for price in test_prices:
        atm = StrikeCalculator.get_atm_strike(price)
        bull_strikes = StrikeCalculator.get_bull_strike_sequence(price)
        bear_strikes = StrikeCalculator.get_bear_strike_sequence(price)
        
        print(f"  SPX {price:7.2f} → ATM {atm:.0f}")
        print(f"    Bull: {bull_strikes[0][0]:.0f}/{bull_strikes[0][1]:.0f} → {bull_strikes[1][0]:.0f}/{bull_strikes[1][1]:.0f}")
        print(f"    Bear: {bear_strikes[0][0]:.0f}/{bear_strikes[0][1]:.0f} → {bear_strikes[1][0]:.0f}/{bear_strikes[1][1]:.0f}")
        print()

if __name__ == "__main__":
    test_smart_strike_selection() 