#!/usr/bin/env python3
"""
Quick Live Test - Immediate validation before trading
Run this 10 minutes before market close to validate everything works
"""

import subprocess
import sys
import datetime as dt

def main():
    print("🚀 QUICK LIVE TEST FOR TONIGHT'S SESSION")
    print("=" * 60)
    print(f"Time: {dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Purpose: Final validation before trading")
    print("=" * 60)
    
    print("\n1️⃣ Testing No Trade Safety Mechanism...")
    try:
        result = subprocess.run([sys.executable, "test_no_trade_safety.py"], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ No Trade safety: WORKING")
        else:
            print("❌ No Trade safety: FAILED")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ No Trade safety: ERROR - {e}")
        return False
    
    print("\n2️⃣ Testing Event-Driven Architecture...")
    try:
        result = subprocess.run([sys.executable, "test_event_driven_implementation.py"], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ Event-driven timing: WORKING")
        else:
            print("❌ Event-driven timing: FAILED")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Event-driven timing: ERROR - {e}")
        return False
    
    print("\n3️⃣ Testing Live Integration...")
    try:
        result = subprocess.run([sys.executable, "test_live_integration.py"], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ Live integration: WORKING")
        else:
            print("❌ Live integration: FAILED")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Live integration: ERROR - {e}")
        return False
    
    print("\n🎉 ALL CRITICAL TESTS PASSED!")
    print("\n💡 READY FOR TONIGHT'S TRADING:")
    print("✅ Event-driven timing will prevent execution failures")
    print("✅ No Trade safety will handle missing data")
    print("✅ Integration components are working")
    print("\n🚀 START YOUR TRADING SESSIONS NORMALLY")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 