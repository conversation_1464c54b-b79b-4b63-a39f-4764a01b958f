{"timestamp": "2025-06-24T17:48:49.809020", "summary": {"total_tests": 8, "passed_tests": 4, "failed_tests": 4, "success_rate": 50.0, "recommendation": "DO NOT TRADE"}, "detailed_results": [{"name": "Trading Configuration Validation", "success": true, "duration": 0.1, "stdout": "Configuration checks completed", "stderr": ""}, {"name": "Event-Driven Architecture (Timing Reliability)", "success": true, "duration": 1.4028773307800293, "stdout": "🔬 EVENT-DRIVEN IMPLEMENTATION VERIFICATION\n============================================================\n\n📊 VERIFICATION RESULTS\n==============================\nTests run: 11\nFailures: 0\nErrors: 0\n\n✅ SUCCESS RATE: 100.0%\n\n🎉 EVENT-DRIVEN IMPLEMENTATION VERIFIED!\n💡 KEY IMPROVEMENTS:\n   ✅ Bar events trigger processing automatically\n   ✅ Execution scheduling is deterministic\n   ✅ UI updates reduced from 1Hz to 0.2Hz (5x improvement)\n   ✅ Main loop timing is no longer dependent on system performance\n   ✅ Timer cleanup prevents resource leaks\n\n💡 NEXT STEPS:\n- Implementation verified - ready for testing\n- Run live test with both accounts\n- Monitor timing precision improvements\n", "stderr": "test_bar_processing_tracking (__main__.TestEventDrivenImplementation.test_bar_processing_tracking)\nTest bar processing tracking works ... ok\ntest_check_for_new_bars_method_exists (__main__.TestEventDrivenImplementation.test_check_for_new_bars_method_exists)\nTest that the new check_for_new_bars method exists ... ok\ntest_cleanup_cancels_timers (__main__.TestEventDrivenImplementation.test_cleanup_cancels_timers)\nTest that cleanup cancels pending timers ... ok\ntest_execution_timer_calls_strategy (__main__.TestEventDrivenImplementation.test_execution_timer_calls_strategy)\nTest that the execution timer actually calls the strategy ... ok\ntest_import_success (__main__.TestEventDrivenImplementation.test_import_success)\nTest that threading import was added successfully ... ok\ntest_main_loop_modifications (__main__.TestEventDrivenImplementation.test_main_loop_modifications)\nTest that main loop was modified correctly ... ok\ntest_on_bar_received_method_exists (__main__.TestEventDrivenImplementation.test_on_bar_received_method_exists)\nTest that the new on_bar_received method exists ... ok\ntest_on_bar_received_processing (__main__.TestEventDrivenImplementation.test_on_bar_received_processing)\nTest that on_bar_received processes bars correctly ... No price data for bar ending at 2025-06-24 15:50:00\nok\ntest_schedule_execution_creates_timer (__main__.TestEventDrivenImplementation.test_schedule_execution_creates_timer)\nTest that schedule_execution creates a proper timer ... ok\ntest_schedule_execution_method_exists (__main__.TestEventDrivenImplementation.test_schedule_execution_method_exists)\nTest that the new schedule_execution method exists ... ok\ntest_third_bar_schedules_execution (__main__.TestEventDrivenImplementation.test_third_bar_schedules_execution)\nTest that third bar schedules execution ... ok\n\n----------------------------------------------------------------------\nRan 11 tests in 1.112s\n\nOK\n"}, {"name": "No Trade Safety Mechanism (Data Validation)", "success": true, "duration": 0.2954134941101074, "stdout": "🔬 NO TRADE SAFETY MECHANISM TEST\n==================================================\n\n📊 TEST RESULTS\n==============================\nTests run: 7\nFailures: 0\nErrors: 0\n\n✅ SUCCESS RATE: 100.0%\n\n🎉 NO TRADE SAFETY MECHANISM VERIFIED!\n💡 SAFETY FEATURES:\n   ✅ Validates all bar data before execution\n   ✅ Defaults to NT when data is incomplete\n   ✅ Preserves original decision for audit trail\n   ✅ Generates proper trade reports for NT\n   ✅ Handles NT decisions in main execution flow\n\n💡 SAFETY SUMMARY:\n- No Trade safety mechanism is working correctly\n- System will default to NT if bar data is missing\n- Ready for production with enhanced safety\n", "stderr": "test_execute_no_trade (__main__.TestNoTradeSafety.test_execute_no_trade)\nTest No Trade execution works correctly ... ok\ntest_main_strategy_execution_with_complete_data (__main__.TestNoTradeSafety.test_main_strategy_execution_with_complete_data)\nTest that main strategy execution proceeds normally with complete data ... ok\ntest_main_strategy_execution_with_missing_data (__main__.TestNoTradeSafety.test_main_strategy_execution_with_missing_data)\nTest that main strategy execution defaults to NT when data is missing ... ❌ Missing critical data: Bar 1, Bar 2, Bar 3, SPAN1, SPAN2, SPAN3\nMissing bar data: Bar 1, Bar 2, Bar 3, SPAN1, SPAN2, SPAN3\n🚫 SAFETY: Missing bar data - defaulting to NO TRADE\n❌ Bar data validation failed - converting to NO TRADE\nok\ntest_no_trade_decision_type_execution (__main__.TestNoTradeSafety.test_no_trade_decision_type_execution)\nTest that DecisionType.NONE is handled correctly in main execution ... ok\ntest_validate_bar_data_complete_missing_bar (__main__.TestNoTradeSafety.test_validate_bar_data_complete_missing_bar)\nTest validation fails when bar data is missing ... ❌ Missing critical data: Bar 3, SPAN3\nMissing bar data: Bar 3, SPAN3\nok\ntest_validate_bar_data_complete_missing_span (__main__.TestNoTradeSafety.test_validate_bar_data_complete_missing_span)\nTest validation fails when span data is missing ... ❌ Missing critical data: SPAN1, SPAN2, SPAN3\nMissing bar data: SPAN1, SPAN2, SPAN3\nok\ntest_validate_bar_data_complete_with_all_data (__main__.TestNoTradeSafety.test_validate_bar_data_complete_with_all_data)\nTest validation passes when all bar data is available ... ok\n\n----------------------------------------------------------------------\nRan 7 tests in 0.008s\n\nOK\n"}, {"name": "Decision Engine Accuracy", "success": false, "duration": 0.04627037048339844, "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"/home/<USER>/trade/JerryTrade/may-dev/core-refactor/backtesting/debug/test_decision_engine.py\", line 17, in <module>\n    from strategy_state import StrategyState, BarData, DecisionType\nModuleNotFoundError: No module named 'strategy_state'\n"}, {"name": "Live Integration (Order Flow)", "success": false, "duration": 0.01925945281982422, "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"/home/<USER>/trade/JerryTrade/may-dev/core-refactor/debug/test_live_integration.py\", line 11, in <module>\n    from combo_orders import ComboOrderFactory, OptionPricingUtils, ComboQuoteResult\nModuleNotFoundError: No module named 'combo_orders'\n"}, {"name": "Simulation with Scenario Data", "success": false, "duration": 0.***************, "stdout": "📡 Using port 4001 for both data and trading connections\nLogging setup complete - files in logging/ (account: trading)\n🧪 SIMULATION MODE ENABLED\n   Scenario: BULL_001\nDEBUG: About to connect to IB...\nDEBUG: IB connection result: False\n", "stderr": "2025-06-24 17:48:33,689 - INFO - database.mssql_handler - Successfully connected to SQL Server at *************\n2025-06-24 17:48:33,734 - INFO - database.mssql_handler - Verified executed_trades table exists\n2025-06-24 17:48:33,734 - INFO - database.mssql_handler - MSSQL Database initialized on *************\n"}, {"name": "IB Paper Trading Connectivity", "success": true, "duration": 1.****************, "stdout": "✅ Connected to IB paper trading\n", "stderr": ""}, {"name": "Live Test Mode Order Flow", "success": false, "duration": 15.**************, "stdout": "📡 Using port 4015 for both data and trading connections\nLogging setup complete - files in logging/ (account: coupledlogic)\nDEBUG: About to connect to IB...\nDEBUG: IB connection result: True\nDEBUG: IB connection successful, proceeding to setup...\nDEBUG: Setting up contracts...\nDEBUG: Contracts setup result: True\nDEBUG: Setting up market data...\nDEBUG: Market data setup result: True\nDEBUG: Initializing trading components...\nDEBUG: Trading components result: True\n\u001b[H\u001b[2J\u001b[3J\n===== SPX TRADING SYSTEM - NEW ALGORITHM ===== 2025-06-24 17:48:48 =====\n\n MARKET DATA \nSymbol       Last    Change  % Change    Bid    Ask    Updated\n--------  -------  --------  ----------  -----  -----  ---------\nSPX       6095.02     69.85  +1.16%      N/A    N/A    0.0s\nVIX         17.61     -2.22  -11.20%     N/A    N/A    0.0s\n\n LIVE STRATEGY QUOTES \n\nBull & Bear Spreads:\nBull Strategy    Strikes      Net Mid  Bear Strategy    Strikes      Net Mid\n---------------  ---------  ---------  ---------------  ---------  ---------\nOne Better       6090/6095      -3.05  One Better       6100/6095      -2.7\nNearest          6095/6100      -2.25  Nearest          6095/6090      -1.9\nNext Worst       6100/6105      -1.43  Next Worst       6090/6085      -1.12\n\nIron Butterflies:\nStrategy       Center  Wings      Net Mid  Status\n-----------  --------  -------  ---------  --------\nWide ATM         6095  ±10           6.7   ✓\nNarrow ATM       6095  ±5            4.15  ✓\nWide Up          6100  ±10           6.77  ✓\nNarrow Up        6100  ±5            4.12  ✓\nWide Down        6090  ±10           7.08  ✓\nNarrow Down      6090  ±5            4.18  ✓\n\nOvershoot:\nStrategy    Strikes      Net Mid\n----------  ---------  ---------\nBull        6100/6105      -1.43\nBear        6090/6085      -1.12\n\n TIMING STATUS \nCurrent Time: 17:48:48\n\nCurrent 30s Bar (Real-Time):\n  O: 6094.54  H: 6095.02  L: 6094.51  C: 6095.02\n  Ticks: 5  Duration: 4s\n\nTrading Schedule:\n  ○ XX:50:30 - First Bar Analysis\n  ○ XX:51:00 - Second Bar Analysis\n  ○ XX:51:30 - Third Bar Analysis\n  ○ XX:51:31 - Main Execution\n  ○ XX:58:55 - Overshoot Check\n  ○ XX:58:59 - Cancel Narrow Orders\n\n BAR DATA COLLECTION \nNo bar data collected yet\n\n STRATEGY DECISION \nItem              Status\n----------------  --------------\nCurrent Decision  IRON BUTTERFLY\nMain Order        Pending\n\n EXECUTION STATUS \nOrder Type          Status\n------------------  -------------\nMain Order          Not Started\nWide Fallback       Not Attempted\nOvershoot Reversal  Pending\n\n DATABASE STATUS \n✓ Connected to MSSQLDatabaseHandler\nTrades will be automatically saved\n\n RECENT LOGS \n17:48:40 - INFO - cancel_narrow: 08:59\n17:48:40 - INFO - =======================\n17:48:40 - INFO - Live quote manager initialized\n17:48:40 - INFO - Console UI components configured\n17:48:40 - INFO - All systems initialized - starting main loop (Event-Driven)\n\nPress Ctrl+C to exit\n", "stderr": "2025-06-24 17:48:35,285 - INFO - database.mssql_handler - Successfully connected to SQL Server at *************\n2025-06-24 17:48:35,331 - INFO - database.mssql_handler - Verified executed_trades table exists\n2025-06-24 17:48:35,331 - INFO - database.mssql_handler - MSSQL Database initialized on *************\n2025-06-24 17:48:35,332 - INFO - ib_insync.client - Connecting to 127.0.0.1:4015 with clientId 1...\n2025-06-24 17:48:35,333 - INFO - ib_insync.client - Connected\n2025-06-24 17:48:35,340 - INFO - ib_insync.client - Logged on to server version 176\n2025-06-24 17:48:35,384 - INFO - ib_insync.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:eufarm\n2025-06-24 17:48:35,385 - INFO - ib_insync.wrapper - Warning 2107, reqId -1: HMDS data farm connection is inactive but should be available upon demand.euhmds\n2025-06-24 17:48:35,385 - INFO - ib_insync.wrapper - Warning 2158, reqId -1: Sec-def data farm connection is OK:secdefeu\n2025-06-24 17:48:35,385 - INFO - ib_insync.client - API connection ready\n2025-06-24 17:48:35,390 - INFO - ib_insync.wrapper - position: Position(account='*********', contract=Option(conId=*********, symbol='SPX', lastTradeDateOrContractMonth='********', strike=6020.0, right='C', multiplier='100', currency='USD', localSymbol='SPXW  250623C06020000', tradingClass='SPXW'), position=0.0, avgCost=0.0)\n2025-06-24 17:48:35,430 - INFO - ib_insync.wrapper - position: Position(account='*********', contract=Option(conId=*********, symbol='SPX', lastTradeDateOrContractMonth='********', strike=6015.0, right='P', multiplier='100', currency='USD', localSymbol='SPXW  250623P06015000', tradingClass='SPXW'), position=0.0, avgCost=0.0)\n2025-06-24 17:48:35,430 - INFO - ib_insync.wrapper - position: Position(account='*********', contract=Option(conId=*********, symbol='SPX', lastTradeDateOrContractMonth='********', strike=6025.0, right='C', multiplier='100', currency='USD', localSymbol='SPXW  250623C06025000', tradingClass='SPXW'), position=0.0, avgCost=0.0)\n2025-06-24 17:48:35,430 - INFO - ib_insync.wrapper - position: Position(account='*********', contract=Option(conId=*********, symbol='SPX', lastTradeDateOrContractMonth='********', strike=6020.0, right='P', multiplier='100', currency='USD', localSymbol='SPXW  250623P06020000', tradingClass='SPXW'), position=0.0, avgCost=0.0)\n2025-06-24 17:48:35,590 - INFO - ib_insync.wrapper - updatePortfolio: PortfolioItem(contract=Option(conId=*********, symbol='SPX', lastTradeDateOrContractMonth='********', strike=6015.0, right='P', multiplier='100', primaryExchange='CBOE', currency='USD', localSymbol='SPXW  250623P06015000', tradingClass='SPXW'), position=0.0, marketPrice=0.0, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=0.0, account='*********')\n2025-06-24 17:48:35,591 - INFO - ib_insync.wrapper - updatePortfolio: PortfolioItem(contract=Option(conId=*********, symbol='SPX', lastTradeDateOrContractMonth='********', strike=6020.0, right='C', multiplier='100', primaryExchange='CBOE', currency='USD', localSymbol='SPXW  250623C06020000', tradingClass='SPXW'), position=0.0, marketPrice=5.1699219, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=0.0, account='*********')\n2025-06-24 17:48:35,591 - INFO - ib_insync.wrapper - updatePortfolio: PortfolioItem(contract=Option(conId=*********, symbol='SPX', lastTradeDateOrContractMonth='********', strike=6020.0, right='P', multiplier='100', primaryExchange='CBOE', currency='USD', localSymbol='SPXW  250623P06020000', tradingClass='SPXW'), position=0.0, marketPrice=0.0, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=0.0, account='*********')\n2025-06-24 17:48:35,591 - INFO - ib_insync.wrapper - updatePortfolio: PortfolioItem(contract=Option(conId=*********, symbol='SPX', lastTradeDateOrContractMonth='********', strike=6025.0, right='C', multiplier='100', primaryExchange='CBOE', currency='USD', localSymbol='SPXW  250623C06025000', tradingClass='SPXW'), position=0.0, marketPrice=0.1699219, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=0.0, account='*********')\n2025-06-24 17:48:35,702 - INFO - ib_insync.wrapper - execDetails Execution(execId='000109ce.685a337c.01.01', time=datetime.datetime(2025, 6, 24, 5, 16, 58, tzinfo=datetime.timezone.utc), acctNumber='*********', exchange='SMART', side='BOT', shares=2.0, price=0.0, permId=**********, clientId=0, orderId=0, liquidation=0, cumQty=2.0, avgPrice=0.0, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=**********)\n2025-06-24 17:48:35,733 - INFO - ib_insync.wrapper - execDetails Execution(execId='000109ce.685a337d.01.01', time=datetime.datetime(2025, 6, 24, 5, 16, 58, tzinfo=datetime.timezone.utc), acctNumber='*********', exchange='SMART', side='SLD', shares=2.0, price=0.0, permId=**********, clientId=0, orderId=0, liquidation=0, cumQty=2.0, avgPrice=0.0, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=**********)\n2025-06-24 17:48:35,734 - INFO - ib_insync.wrapper - execDetails Execution(execId='000109ce.685a337e.01.01', time=datetime.datetime(2025, 6, 24, 5, 16, 58, tzinfo=datetime.timezone.utc), acctNumber='*********', exchange='SMART', side='BOT', shares=2.0, price=0.0, permId=1705034013, clientId=0, orderId=0, liquidation=0, cumQty=2.0, avgPrice=0.0, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=**********)\n2025-06-24 17:48:35,734 - INFO - ib_insync.wrapper - execDetails Execution(execId='000109ce.685a337f.01.01', time=datetime.datetime(2025, 6, 24, 5, 16, 58, tzinfo=datetime.timezone.utc), acctNumber='*********', exchange='SMART', side='SLD', shares=2.0, price=0.0, permId=1705034014, clientId=0, orderId=0, liquidation=0, cumQty=2.0, avgPrice=0.0, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=**********)\n2025-06-24 17:48:35,734 - INFO - ib_insync.ib - Synchronization complete\n2025-06-24 17:48:40,051 - INFO - database.mssql_handler - Successfully connected to SQL Server at *************\n2025-06-24 17:48:40,096 - INFO - database.mssql_handler - Verified executed_trades table exists\n2025-06-24 17:48:40,097 - INFO - database.mssql_handler - MSSQL Database initialized on *************\n2025-06-24 17:48:40,097 - INFO - quote_engine - ✅ Requested live market data for quote engine\n2025-06-24 17:48:40,097 - INFO - quote_engine - ✅ Requested live market data for quote engine\n2025-06-24 17:48:40,097 - INFO - ib_insync.wrapper - Warning 2119, reqId -1: Market data farm is connecting:usfarm.nj\n2025-06-24 17:48:40,097 - INFO - ib_insync.wrapper - Warning 2119, reqId -1: Market data farm is connecting:usfuture\n2025-06-24 17:48:40,097 - INFO - ib_insync.wrapper - Warning 2119, reqId -1: Market data farm is connecting:usfarm.nj\n2025-06-24 17:48:40,097 - INFO - ib_insync.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:usfuture\n2025-06-24 17:48:40,098 - INFO - ib_insync.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:usfarm.nj\n2025-06-24 17:48:40,497 - INFO - execution_engine - 💰 Wallet Balance: $6,072.12\n2025-06-24 17:48:40,498 - INFO - trading_detailed - Account balance initialized: $6,072.12\n2025-06-24 17:48:40,498 - INFO - spx_strategy - 🚀 SPX Trading Strategy initialized with new algorithm\n2025-06-24 17:48:40,498 - INFO - spx_strategy - 🚀 Trading session started at 17:48:40\n2025-06-24 17:48:40,498 - INFO - spx_strategy - 📋 Default Decision: iron_butterfly\n2025-06-24 17:48:40,499 - INFO - live_quote_manager - SPX price updated: None -> 6094.42\n2025-06-24 17:48:40,499 - INFO - live_quote_manager - Setting up strategy quotes for SPX 6094.42\n2025-06-24 17:48:40,841 - INFO - ib_insync.wrapper - Warning 2119, reqId -1: Market data farm is connecting:usopt\n2025-06-24 17:48:41,152 - INFO - ib_insync.wrapper - Warning 2104, reqId -1: Market data farm connection is OK:usopt\n2025-06-24 17:48:44,779 - INFO - live_quote_manager - Live quotes setup complete: 10 option contracts\n"}]}