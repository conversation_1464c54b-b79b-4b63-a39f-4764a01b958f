#!/usr/bin/env python3
"""
Live Readiness Test Suite
Comprehensive testing before tonight's trading session
"""

import sys
import os
import subprocess
import datetime as dt
import time
import json
from typing import Dict, List, Any

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_test_command(command: str, description: str) -> Dict[str, Any]:
    """Run a test command and capture results"""
    print(f"\n🔬 {description}")
    print("-" * 60)
    
    try:
        start_time = time.time()
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=30
        )
        end_time = time.time()
        
        success = result.returncode == 0
        duration = end_time - start_time
        
        if success:
            print(f"✅ PASSED ({duration:.2f}s)")
            if result.stdout:
                print(result.stdout)
        else:
            print(f"❌ FAILED ({duration:.2f}s)")
            if result.stderr:
                print(f"Error: {result.stderr}")
            if result.stdout:
                print(f"Output: {result.stdout}")
        
        return {
            'name': description,
            'success': success,
            'duration': duration,
            'stdout': result.stdout,
            'stderr': result.stderr
        }
        
    except subprocess.TimeoutExpired:
        print(f"⏰ TIMEOUT (30s)")
        return {
            'name': description,
            'success': False,
            'duration': 30.0,
            'stdout': '',
            'stderr': 'Test timed out after 30 seconds'
        }
    except Exception as e:
        print(f"💥 ERROR: {str(e)}")
        return {
            'name': description,
            'success': False,
            'duration': 0.0,
            'stdout': '',
            'stderr': str(e)
        }

def test_event_driven_architecture():
    """Test the new event-driven timing architecture"""
    return run_test_command(
        "python debug/test_event_driven_implementation.py",
        "Event-Driven Architecture (Timing Reliability)"
    )

def test_no_trade_safety():
    """Test the No Trade safety mechanism"""
    return run_test_command(
        "python debug/test_no_trade_safety.py",
        "No Trade Safety Mechanism (Data Validation)"
    )

def test_live_integration():
    """Test live integration components"""
    return run_test_command(
        "python debug/test_live_integration.py",
        "Live Integration (Order Flow)"
    )

def test_decision_engine():
    """Test decision engine with recent data"""
    return run_test_command(
        "python backtesting/debug/test_decision_engine.py",
        "Decision Engine Accuracy"
    )

def test_simulation_mode():
    """Test simulation mode functionality"""
    print(f"\n🔬 Simulation Mode (Market Conditions)")
    print("-" * 60)
    
    # Test with a known scenario
    scenario_test = run_test_command(
        "timeout 10s python live_trading_app.py --simulation --scenario BULL_001 --dry-run",
        "Simulation with Scenario Data"
    )
    
    return scenario_test

def test_live_test_mode():
    """Test live test mode (safe orders)"""
    print(f"\n🔬 Live Test Mode (Safe Order Testing)")
    print("-" * 60)
    print("NOTE: This will test order flow but use safe test prices")
    
    return run_test_command(
        "timeout 15s python live_trading_app.py --live-test --dry-run --data-port 4015",
        "Live Test Mode Order Flow"
    )

def validate_config():
    """Validate trading configuration"""
    print(f"\n🔬 Trading Configuration Validation")
    print("-" * 60)
    
    try:
        from config import TradingConfig
        config = TradingConfig()
        
        # Check critical settings
        checks = [
            ("Timing reference minute", config.timing.reference_minute >= 0),
            ("Price limits configured", config.pricing.spread_price_limit > 0),
            ("Safety timeouts set", config.timing.spread_retry_1 > 0),
            ("Test mode available", hasattr(config.live_test, 'enabled')),
        ]
        
        all_passed = True
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"   {status} {check_name}: {check_result}")
            if not check_result:
                all_passed = False
        
        return {
            'name': 'Trading Configuration Validation',
            'success': all_passed,
            'duration': 0.1,
            'stdout': 'Configuration checks completed',
            'stderr': '' if all_passed else 'Some configuration checks failed'
        }
        
    except Exception as e:
        print(f"❌ Configuration error: {str(e)}")
        return {
            'name': 'Trading Configuration Validation',
            'success': False,
            'duration': 0.1,
            'stdout': '',
            'stderr': str(e)
        }

def check_data_connectivity():
    """Check IB connectivity (paper trading)"""
    print(f"\n🔬 IB Connectivity Check (Paper Trading)")
    print("-" * 60)
    print("NOTE: This assumes IB Gateway/TWS is running on port 4001 (paper)")
    
    return run_test_command(
        "timeout 10s python -c \"from ib_insync import IB; ib=IB(); ib.connect('127.0.0.1', 4015, clientId=999); print('✅ Connected to IB paper trading'); ib.disconnect()\"",
        "IB Paper Trading Connectivity"
    )

def run_comprehensive_test_suite():
    """Run all critical tests before tonight's session"""
    print("🚀 LIVE READINESS TEST SUITE")
    print("=" * 80)
    print(f"Testing Time: {dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Purpose: Validate system before tonight's trading session")
    print("=" * 80)
    
    # Run all tests
    test_results = []
    
    # Core system tests
    test_results.append(validate_config())
    test_results.append(test_event_driven_architecture())
    test_results.append(test_no_trade_safety())
    test_results.append(test_decision_engine())
    
    # Integration tests
    test_results.append(test_live_integration())
    test_results.append(test_simulation_mode())
    
    # Connectivity tests
    test_results.append(check_data_connectivity())
    test_results.append(test_live_test_mode())
    
    # Calculate results
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results if result['success'])
    failed_tests = total_tests - passed_tests
    success_rate = (passed_tests / total_tests) * 100
    
    # Summary
    print(f"\n📊 READINESS SUMMARY")
    print("=" * 50)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    # Failed test details
    if failed_tests > 0:
        print(f"\n❌ FAILED TESTS:")
        for result in test_results:
            if not result['success']:
                print(f"   • {result['name']}")
                if result['stderr']:
                    print(f"     Error: {result['stderr']}")
    
    # Readiness assessment
    print(f"\n🎯 READINESS ASSESSMENT")
    print("=" * 40)
    
    if success_rate >= 90:
        print("🟢 READY FOR LIVE TRADING")
        print("✅ All critical systems validated")
        print("✅ Safety mechanisms verified")
        print("✅ Event-driven architecture tested")
        recommendation = "PROCEED"
    elif success_rate >= 75:
        print("🟡 MOSTLY READY - Minor Issues")
        print("⚠️ Some non-critical tests failed")
        print("✅ Core systems appear functional")
        recommendation = "PROCEED WITH CAUTION"
    else:
        print("🔴 NOT READY - Critical Issues")
        print("❌ Multiple system failures detected")
        print("🚨 DO NOT TRADE UNTIL FIXED")
        recommendation = "DO NOT TRADE"
    
    # Trading recommendations
    print(f"\n💡 TONIGHT'S TRADING RECOMMENDATIONS:")
    print("-" * 45)
    
    if recommendation == "PROCEED":
        print("1. ✅ Start both CoupledLogic and PhaedrusTrader normally")
        print("2. ✅ Event-driven timing should prevent execution failures")
        print("3. ✅ No Trade safety will handle missing data scenarios")
        print("4. ✅ Full trading confidence with new safety features")
        
    elif recommendation == "PROCEED WITH CAUTION":
        print("1. ⚠️ Start trading but monitor closely for first few minutes")
        print("2. ✅ Event-driven timing improvements are working")
        print("3. ⚠️ Be prepared to stop if issues arise")
        print("4. 📋 Keep manual backup ready")
        
    else:
        print("1. ❌ DO NOT START AUTOMATED TRADING")
        print("2. 🔧 Fix failed tests before proceeding")
        print("3. 📋 Use manual trading as backup")
        print("4. 🧪 Re-run tests after fixes")
    
    # Save detailed results
    results_file = f"test_results_{dt.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        with open(results_file, 'w') as f:
            json.dump({
                'timestamp': dt.datetime.now().isoformat(),
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'failed_tests': failed_tests,
                    'success_rate': success_rate,
                    'recommendation': recommendation
                },
                'detailed_results': test_results
            }, f, indent=2)
        
        print(f"\n📁 Detailed results saved: {results_file}")
        
    except Exception as e:
        print(f"\n⚠️ Could not save results file: {str(e)}")
    
    return success_rate >= 75


if __name__ == "__main__":
    print("🧪 Starting live readiness validation...")
    
    # Change to correct directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(script_dir)
    os.chdir(parent_dir)
    
    success = run_comprehensive_test_suite()
    
    print(f"\n{'='*60}")
    if success:
        print("🎉 SYSTEM READY FOR TONIGHT'S TRADING SESSION!")
    else:
        print("🚨 SYSTEM NOT READY - ADDRESS ISSUES BEFORE TRADING")
    print(f"{'='*60}")
    
    exit(0 if success else 1) 