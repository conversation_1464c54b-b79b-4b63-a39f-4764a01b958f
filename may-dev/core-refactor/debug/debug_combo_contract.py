#!/usr/bin/env python3
import sys
sys.path.append('core-refactor')
from quote_engine import OptionQuoteEngine
from combo_orders import ComboOrderFactory, ComboOrderBuilder
from ib_insync import IB
import time

def debug_combo_contract():
    """Debug combo contract building and real quotes"""
    
    ib = IB()
    try:
        ib.connect('127.0.0.1', 4015, clientId=1, timeout=30)
        print("✅ Connected to IB")
        
        # Request live market data
        ib.reqMarketDataType(1)
        print("✅ Requested live market data")
        time.sleep(2)
        
        # Create wide iron butterfly
        center_strike = 6030.0
        combo_def = ComboOrderFactory.create_wide_iron_butterfly(center_strike)
        print(f"\n=== Wide Iron Butterfly Definition ===")
        print(f"Center strike: {center_strike}")
        print(f"Combo type: {combo_def.combo_type}")
        print(f"Is credit strategy: {combo_def.is_credit_strategy}")
        print(f"Number of legs: {len(combo_def.legs)}")
        
        for i, leg in enumerate(combo_def.legs):
            print(f"  Leg {i+1}: {leg.action.value} {leg.strike} {leg.option_type.value} (ratio: {leg.ratio})")
        
        # Test combo contract building
        print(f"\n=== Testing Combo Contract Building ===")
        try:
            combo_contract = ComboOrderBuilder.build_combo_contract(
                combo_def,
                symbol='SPX',
                exchange='SMART',
                currency='USD'
            )
            print(f"✅ Combo contract built successfully")
            print(f"Symbol: {combo_contract.symbol}")
            print(f"Security type: {combo_contract.secType}")
            print(f"Exchange: {combo_contract.exchange}")
            print(f"Currency: {combo_contract.currency}")
            print(f"Number of combo legs: {len(combo_contract.comboLegs) if combo_contract.comboLegs else 0}")
            
            if combo_contract.comboLegs:
                for i, leg in enumerate(combo_contract.comboLegs):
                    print(f"  Combo leg {i+1}: ConId={leg.conId}, Ratio={leg.ratio}, Action={leg.action}")
            
        except Exception as e:
            print(f"❌ Error building combo contract: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # Test individual contract qualification first
        print(f"\n=== Testing Individual Contract Qualification ===")
        from ib_insync import Option
        import datetime as dt
        
        expiry = dt.datetime.now().strftime("%Y%m%d")
        strikes = [6010.0, 6020.0, 6030.0]
        
        qualified_contracts = {}
        for strike in strikes:
            for option_type in ['C', 'P']:
                contract = Option(
                    symbol='SPX',
                    lastTradeDateOrContractMonth=expiry,
                    strike=strike,
                    right=option_type,
                    exchange='SMART',
                    tradingClass='SPXW'
                )
                
                qualified = ib.qualifyContracts(contract)
                if qualified:
                    key = f"{strike}{option_type}"
                    qualified_contracts[key] = qualified[0]
                    print(f"✅ Qualified {key}: ConId={qualified[0].conId}")
                else:
                    print(f"❌ Failed to qualify {strike}{option_type}")
        
        # Update combo contract with qualified contract IDs
        print(f"\n=== Updating Combo Legs with Qualified ConIds ===")
        if combo_contract.comboLegs and qualified_contracts:
            for i, leg in enumerate(combo_def.legs):
                leg_key = f"{leg.strike}{leg.option_type.value}"
                if leg_key in qualified_contracts:
                    combo_contract.comboLegs[i].conId = qualified_contracts[leg_key].conId
                    print(f"✅ Updated leg {i+1} ({leg_key}) with ConId={qualified_contracts[leg_key].conId}")
                else:
                    print(f"❌ No qualified contract for leg {leg_key}")
        
        # Test real combo quote
        print(f"\n=== Testing Real Combo Quote ===")
        try:
            combo_ticker = ib.reqMktData(combo_contract)
            print(f"✅ Requested market data for combo")
            
            print("Waiting for combo quote...")
            for i in range(10):
                ib.sleep(1)
                print(f"  {i+1}s: Bid={combo_ticker.bid}, Ask={combo_ticker.ask}, Last={combo_ticker.last}")
                
                if combo_ticker.bid is not None or combo_ticker.ask is not None:
                    print(f"✅ Got combo quote!")
                    break
            else:
                print(f"❌ No combo quote after 10 seconds")
                
                # Try individual leg quotes for comparison
                print(f"\n=== Individual Leg Quotes for Comparison ===")
                for key, contract in qualified_contracts.items():
                    ticker = ib.reqMktData(contract)
                    ib.sleep(1)
                    print(f"  {key}: Bid={ticker.bid}, Ask={ticker.ask}, Last={ticker.last}")
                    ib.cancelMktData(contract)
            
            ib.cancelMktData(combo_contract)
            
        except Exception as e:
            print(f"❌ Error getting combo quote: {e}")
            import traceback
            traceback.print_exc()
        
        print(f"\n=== Summary ===")
        print(f"Combo contracts might not have real market quotes")
        print(f"IB often doesn't provide quotes for complex combos")
        print(f"You may need to calculate from individual legs")
        
        ib.disconnect()
        print("\n✅ Disconnected from IB")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_combo_contract() 