import sys
import os
import unittest
from unittest.mock import MagicMock, patch
from time import sleep

# Add the parent directory to sys.path so we can import from core-refactor
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from execution_engine import OptionExecutionEngine
from combo_orders import ComboOrderFactory
from config import TradingConfig

class DummyIB:
    def __init__(self):
        self.reqMarketDataType = MagicMock()
        self.qualifyContracts = MagicMock()
        self.reqMktData = MagicMock()
        self.cancelMktData = MagicMock()
        self.sleep = sleep  # Add sleep method
        
    def placeOrder(self, contract, order):
        # Just return a dummy object with the order attached for inspection
        class DummyTrade:
            def __init__(self, order):
                self.order = order
                self.orderStatus = type('OrderStatus', (), {'status': 'Submitted'})()
                self.fills = []
            def isDone(self):
                return True
        return DummyTrade(order)

class DummyDB:
    def __init__(self):
        self.save_order = MagicMock()
        self.update_order = MagicMock()

class TestOrderSign(unittest.TestCase):
    def setUp(self):
        self.config = TradingConfig()
        self.ib = DummyIB()
        self.db = DummyDB()
        self.engine = OptionExecutionEngine(self.ib, self.db)
        self.engine.config = self.config
        self.engine.db = None  # Disable DB interactions
        
    def test_credit_strategy_negative_price(self):
        # Wide iron butterfly is a credit strategy
        combo_def = ComboOrderFactory.create_wide_iron_butterfly(4000)
        limit_price = 6.50
        # Patch: capture the effective price
        self.engine.config.live_test.enabled = False
        self.engine.config.get_effective_order_price = lambda lp, st: lp  # Bypass test mode
        
        # Monkeypatch LimitOrder to capture price
        captured = {}
        orig_LimitOrder = None
        
        # Mock the quote checking methods
        with patch.object(self.engine, '_get_quote_for_combo', return_value=type('QR', (), {'is_valid': True, 'error_message': None})()), \
             patch.object(self.engine, '_wait_for_fill', return_value=type('Res', (), {'success': True, 'fill_price': None, 'trade_id': 'TEST'})()), \
             patch.object(self.engine, '_create_enhanced_database_notes', return_value=""):
            
            try:
                import ib_insync
                orig_LimitOrder = ib_insync.LimitOrder
                def FakeLimitOrder(side, qty, price):
                    captured['price'] = price
                    return MagicMock()
                ib_insync.LimitOrder = FakeLimitOrder
                
                self.engine.execute_combo_order(combo_def, limit_price)
                self.assertLess(captured['price'], 0, "Credit strategy should use negative price")
            finally:
                if orig_LimitOrder:
                    ib_insync.LimitOrder = orig_LimitOrder

    def test_debit_strategy_positive_price(self):
        # Bull spread is a debit strategy
        combo_def = ComboOrderFactory.create_bull_spread(4000)
        limit_price = 2.45
        self.engine.config.live_test.enabled = False
        self.engine.config.get_effective_order_price = lambda lp, st: lp
        
        # Monkeypatch LimitOrder to capture price
        captured = {}
        orig_LimitOrder = None
        
        # Mock the quote checking methods
        with patch.object(self.engine, '_get_quote_for_combo', return_value=type('QR', (), {'is_valid': True, 'error_message': None})()), \
             patch.object(self.engine, '_wait_for_fill', return_value=type('Res', (), {'success': True, 'fill_price': None, 'trade_id': 'TEST'})()), \
             patch.object(self.engine, '_create_enhanced_database_notes', return_value=""):
            
            try:
                import ib_insync
                orig_LimitOrder = ib_insync.LimitOrder
                def FakeLimitOrder(side, qty, price):
                    captured['price'] = price
                    return MagicMock()
                ib_insync.LimitOrder = FakeLimitOrder
                
                self.engine.execute_combo_order(combo_def, limit_price)
                self.assertGreater(captured['price'], 0, "Debit strategy should use positive price")
            finally:
                if orig_LimitOrder:
                    ib_insync.LimitOrder = orig_LimitOrder

if __name__ == '__main__':
    unittest.main()