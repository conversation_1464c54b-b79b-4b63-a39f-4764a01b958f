#!/usr/bin/env python3
import sys
sys.path.append('core-refactor')
from quote_engine import OptionQuoteEngine
from combo_orders import ComboOrderFactory
from ib_insync import IB
import time

def test_fixed_quotes():
    """Test the fixed quote engine"""
    
    ib = IB()
    try:
        ib.connect('127.0.0.1', 4015, clientId=1, timeout=30)
        print("✅ Connected to IB")
        
        # Initialize quote engine (now automatically requests live data)
        quote_engine = OptionQuoteEngine(ib)
        print("✅ Quote engine initialized with live market data")
        
        # Wait a moment for market data to stabilize
        time.sleep(2)
        
        # Test 1: Wide iron butterfly quote
        print("\n=== Test 1: Wide Iron Butterfly Quote ===")
        center_strike = 6020.0
        wide_quote = quote_engine.get_wide_butterfly_quote(center_strike)
        print(f"Wide iron butterfly quote: ${wide_quote:.2f}")
        
        # Test 2: Get full combo quote details
        print("\n=== Test 2: Full Combo Quote Details ===")
        combo_def = ComboOrderFactory.create_wide_iron_butterfly(center_strike)
        quote_result = quote_engine.get_combo_quote(combo_def)
        
        print(f"Valid: {quote_result.is_valid}")
        print(f"Bid: {quote_result.bid}")
        print(f"Ask: {quote_result.ask}")
        print(f"Last: {quote_result.last}")
        print(f"Error: {quote_result.error_message}")
        
        if quote_result.individual_leg_quotes:
            print("Individual leg quotes:")
            for leg_id, quotes in quote_result.individual_leg_quotes.items():
                print(f"  {leg_id}: {quotes}")
        
        # Test 3: Threshold check
        print("\n=== Test 3: Threshold Check ===")
        meets_threshold = quote_engine.check_wide_butterfly_fallback_threshold(center_strike)
        print(f"Meets $6.00 threshold: {meets_threshold}")
        
        # Test 4: Different decision types
        print("\n=== Test 4: Different Decision Types ===")
        decisions = ['iron_butterfly', 'bull_spread', 'bear_spread']
        
        for decision in decisions:
            try:
                quote_result = quote_engine.get_quote_from_decision(decision, center_strike)
                print(f"{decision}: Valid={quote_result.is_valid}, Bid={quote_result.bid}, Ask={quote_result.ask}")
            except Exception as e:
                print(f"{decision}: Error - {e}")
        
        print(f"\n✅ All tests completed!")
        
        ib.disconnect()
        print("✅ Disconnected from IB")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_quotes() 