#!/usr/bin/env python3
# Live Market Data Monitor - SPX, ES, VIX, risk-free rate

import argparse
import datetime as dt
import logging
import os
import sys
import time
from ib_insync import *
from tabulate import tabulate
import collections
from datetime import timedelta
from collections import deque

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Default connection settings
DEFAULT_IP = '127.0.0.1'
DEFAULT_PORT = 4001  # Chris account port
DEFAULT_CLIENT_ID = 1
DEFAULT_REFRESH = 1.0  # Refresh rate in seconds

# Global variables for data storage
market_data = {
    'SPX': {'last': None, 'bid': None, 'ask': None, 'change': None, 'change_pct': None, 'timestamp': None},
    'ES': {'last': None, 'bid': None, 'ask': None, 'change': None, 'change_pct': None, 'timestamp': None, 'volume': None, 'avg_volume': None},
    'VIX': {'last': None, 'bid': None, 'ask': None, 'change': None, 'change_pct': None, 'timestamp': None},
    'RISK_FREE': {'rate': None, 'timestamp': None}
}

# Previous day's closing prices (for calculating change)
previous_close = {'SPX': None, 'ES': None, 'VIX': None}

# Store historical price data
price_history = {
    'SPX': collections.deque(maxlen=16),  # Store last 16 minutes of data
    'VIX': collections.deque(maxlen=16)
}

# For options tracking
spx_options = {}
contracts = {}  # Global variable to store contracts

# For volume tracking
volume_history = {
    'ES': collections.deque(maxlen=10)  # Store last 10 volume readings
}

# Add this to your global variables section:
volume_bins = {
    'ES': {
        'bins': deque(maxlen=6),  # Store 6 bins of 10-second data
        'last_volume': None,      # Last total volume seen
        'last_bin_time': None     # Timestamp of last bin
    }
}

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Live Market Data Monitor')
    parser.add_argument('--ip', type=str, default=DEFAULT_IP,
                        help=f'IP address of IB Gateway (default: {DEFAULT_IP})')
    parser.add_argument('--port', type=int, default=DEFAULT_PORT,
                        help=f'Port number (default: {DEFAULT_PORT})')
    parser.add_argument('--client-id', type=int, default=DEFAULT_CLIENT_ID,
                        help=f'Client ID (default: {DEFAULT_CLIENT_ID})')
    parser.add_argument('--refresh', type=float, default=DEFAULT_REFRESH,
                        help=f'Refresh rate in seconds (default: {DEFAULT_REFRESH})')
    parser.add_argument('--debug', action='store_true',
                        help='Enable debug logging')
    return parser.parse_args()

def connect_to_ib(ip, port, client_id):
    """Connect to Interactive Brokers Gateway"""
    logger.info(f"Connecting to IB Gateway at {ip}:{port} with client ID {client_id}")
    
    ib = IB()
    try:
        ib.connect(ip, port, clientId=client_id)
        
        if ib.isConnected():
            logger.info("✅ Successfully connected to IB Gateway")
            return ib
        else:
            logger.error("❌ Failed to connect to IB Gateway")
            return None
    except Exception as e:
        logger.error(f"❌ Connection error: {str(e)}")
        return None

def get_active_es_contract(ib):
    """Get the most active ES futures contract with proper specifications"""
    logger.info("Finding ES futures contract...")
    
    try:
        # Create the contract with the exact specifications
        contract = Future(symbol='ES', 
                          exchange='CME',  # Not GLOBEX - CME is correct
                          currency='USD')
        
        # Set the expiration to current front-month (June 2025)
        contract.lastTradeDateOrContractMonth = '202506'
        
        logger.info(f"Trying ES contract with expiry: 202506")
        
        qualified = ib.qualifyContracts(contract)
        if qualified:
            logger.info(f"Successfully qualified ES futures contract: {qualified[0].localSymbol}")
            return qualified[0]
    except Exception as e:
        logger.error(f"Error qualifying ES futures contract: {str(e)}")
    
    # If still not successful, fall back to SPY
    try:
        logger.warning("Could not find ES futures contract. Falling back to SPY ETF for volume data.")
        spy = Stock('SPY', 'SMART', 'USD')
        qualified = ib.qualifyContracts(spy)
        if qualified:
            logger.info("Using SPY as fallback for ES volume data")
            return qualified[0]
    except Exception as e:
        logger.error(f"Error qualifying SPY fallback: {str(e)}")
    
    return None 

def setup_contracts(ib):
    """Set up all the contracts we need to monitor"""
    contracts = {}
    
    # SPX Index
    try:
        spx = Index('SPX', 'CBOE')
        ib.qualifyContracts(spx)
        contracts['SPX'] = spx
        logger.info("SPX contract qualified")
    except Exception as e:
        logger.error(f"Error qualifying SPX contract: {str(e)}")
    
    # ES Futures
    es = get_active_es_contract(ib)
    if es:
        contracts['ES'] = es
    else:
        # Use SPY as fallback for volume tracking
        try:
            spy = Stock('SPY', 'SMART', 'USD')
            ib.qualifyContracts(spy)
            contracts['ES'] = spy  # We'll still call it 'ES' in our data structure
            logger.info("Using SPY as fallback for ES contract")
        except Exception as e:
            logger.error(f"Error qualifying SPY fallback contract: {str(e)}")
    
    # VIX Index
    try:
        vix = Index('VIX', 'CBOE')
        ib.qualifyContracts(vix)
        contracts['VIX'] = vix
        logger.info("VIX contract qualified")
    except Exception as e:
        logger.error(f"Error qualifying VIX contract: {str(e)}")
    
    # For risk-free rate, use a constant value instead
    market_data['RISK_FREE']['rate'] = 0.053
    market_data['RISK_FREE']['timestamp'] = dt.datetime.now().strftime("%H:%M:%S")
    
    return contracts

def get_previous_close(ib, contract, symbol):
    """Get the previous day's closing price"""
    try:
        # Request 2 days of daily bars to get yesterday's close
        bars = ib.reqHistoricalData(
            contract=contract,
            endDateTime='',
            durationStr='2 D',
            barSizeSetting='1 day',
            whatToShow='TRADES',
            useRTH=True,
            formatDate=1
        )
        
        if len(bars) >= 2:
            # Get the second-to-last bar (yesterday)
            prev_close = bars[-2].close
            logger.info(f"Previous close for {symbol}: {prev_close}")
            return prev_close
        elif len(bars) == 1:
            # If only one bar, assume it's yesterday
            prev_close = bars[0].close
            logger.info(f"Previous close for {symbol} (using latest): {prev_close}")
            return prev_close
        else:
            logger.warning(f"Could not get previous close for {symbol}")
            return None
    except Exception as e:
        logger.warning(f"Error getting previous close for {symbol}: {str(e)}")
        return None

def get_atm_options(ib, underlying_contract, underlying_price):
    """Get at-the-money call and put options expiring today (0DTE)"""
    if not underlying_price:
        logger.warning(f"Cannot get options: no price for {underlying_contract.symbol}")
        return {}
    
    # Round price to nearest strike multiple (typically 5 for SPX)
    strike_increment = 5
    atm_strike = round(underlying_price / strike_increment) * strike_increment
    
    # Get more strikes for 0DTE options (wider spread around current price)
    strikes = [
        atm_strike - 25, 
        atm_strike - 20, 
        atm_strike - 15,
        atm_strike - 10, 
        atm_strike - 5, 
        atm_strike,
        atm_strike + 5, 
        atm_strike + 10,
        atm_strike + 15,
        atm_strike + 20,
        atm_strike + 25
    ]
    
    # Get today's date for 0DTE options
    today = dt.date.today()
    
    # Check if there are 0DTE options available
    # SPX has Monday, Wednesday, Friday expirations
    expiries = []
    
    # Add today as expiry
    expiry = today.strftime("%Y%m%d")
    expiries.append(expiry)
    
    logger.info(f"Looking for 0DTE options expiring today ({expiry})")
    
    # Create option contracts
    options = {}
    for expiry in expiries:
        for strike in strikes:
            # Create call
            call_contract = Option(underlying_contract.symbol, expiry, strike, 'C', 'CBOE')
            call_key = f"{expiry} {strike}C"
            
            # Create put
            put_contract = Option(underlying_contract.symbol, expiry, strike, 'P', 'CBOE')
            put_key = f"{expiry} {strike}P"
            
            try:
                # Qualify contracts
                qualified_calls = ib.qualifyContracts(call_contract)
                qualified_puts = ib.qualifyContracts(put_contract)
                
                if qualified_calls:
                    ticker = ib.reqMktData(qualified_calls[0])
                    options[call_key] = {'contract': qualified_calls[0], 'ticker': ticker}
                    logger.debug(f"Added 0DTE call option: {call_key}")
                
                if qualified_puts:
                    ticker = ib.reqMktData(qualified_puts[0])
                    options[put_key] = {'contract': qualified_puts[0], 'ticker': ticker}
                    logger.debug(f"Added 0DTE put option: {put_key}")
                
            except Exception as e:
                logger.warning(f"Error setting up option {call_key}: {str(e)}")
    
    if not options:
        # If no 0DTE options found, try to look for the next available expiry
        logger.warning("No 0DTE options found, checking for the nearest expiry")
        
        # Add one more day to check if options are available for tomorrow (sometimes trading the day before)
        tomorrow = today + timedelta(days=1)
        next_expiry = tomorrow.strftime("%Y%m%d")
        
        for strike in strikes:
            call_contract = Option(underlying_contract.symbol, next_expiry, strike, 'C', 'CBOE')
            put_contract = Option(underlying_contract.symbol, next_expiry, strike, 'P', 'CBOE')
            
            try:
                qualified_calls = ib.qualifyContracts(call_contract)
                qualified_puts = ib.qualifyContracts(put_contract)
                
                if qualified_calls:
                    ticker = ib.reqMktData(qualified_calls[0])
                    options[f"{next_expiry} {strike}C"] = {'contract': qualified_calls[0], 'ticker': ticker}
                
                if qualified_puts:
                    ticker = ib.reqMktData(qualified_puts[0])
                    options[f"{next_expiry} {strike}P"] = {'contract': qualified_puts[0], 'ticker': ticker}
                    
                # If we found at least some options, break
                if options:
                    logger.info(f"Using next available expiry: {next_expiry}")
                    break
                    
            except Exception as e:
                continue
    
    return options

def setup_market_data(ib, contracts):
    """Subscribe to market data for all contracts"""
    tickers = {}
    
    for symbol, contract in contracts.items():
        try:
            ticker = ib.reqMktData(contract)
            tickers[symbol] = ticker
            logger.info(f"Subscribed to market data for {symbol}")
            
            # Get previous close for price change calculation
            if symbol in ['SPX', 'VIX']:
                previous_close[symbol] = get_previous_close(ib, contract, symbol)
                
                # Create empty history
                price_history[symbol] = collections.deque(maxlen=16)  # 16 minutes of history
                
        except Exception as e:
            logger.error(f"Error setting up market data for {symbol}: {str(e)}")
    
    return tickers

def update_option_data(ib):
    """Update option data based on current SPX price"""
    global spx_options
    
    # Only proceed if we have SPX price
    if market_data['SPX']['last'] is None:
        return
    
    current_price = market_data['SPX']['last']
    
    # Check if we need to get new ATM options (price moved significantly or no options yet)
    if not spx_options or abs(spx_options.get('last_price', 0) - current_price) > 15:
        # Clear any existing option subscriptions
        for option_key, option_data in spx_options.items():
            if option_key != 'last_price' and 'ticker' in option_data:
                try:
                    ib.cancelMktData(option_data['contract'])
                except:
                    pass
        
        # Get new ATM options
        spx_contract = next((c for s, c in contracts.items() if s == 'SPX'), None)
        if spx_contract:
            spx_options = get_atm_options(ib, spx_contract, current_price)
            spx_options['last_price'] = current_price
            logger.info(f"Updated SPX options for price: {current_price}")

def calculate_trend_data():
    """Calculate price changes over different timeframes"""
    trend_data = {}
    
    for symbol in ['SPX', 'VIX']:
        if symbol not in price_history or not price_history[symbol]:
            trend_data[symbol] = {'1m': None, '5m': None, '15m': None}
            continue
        
        # Get current price
        current = price_history[symbol][-1] if price_history[symbol] else None
        
        # No trend data if we don't have current price
        if current is None:
            trend_data[symbol] = {'1m': None, '5m': None, '15m': None}
            continue
            
        # Get historical prices
        price_1m_ago = price_history[symbol][-2] if len(price_history[symbol]) >= 2 else None
        price_5m_ago = price_history[symbol][-6] if len(price_history[symbol]) >= 6 else None
        price_15m_ago = price_history[symbol][-16] if len(price_history[symbol]) >= 16 else None
        
        # Calculate changes
        change_1m = current - price_1m_ago if price_1m_ago is not None else None
        change_5m = current - price_5m_ago if price_5m_ago is not None else None
        change_15m = current - price_15m_ago if price_15m_ago is not None else None
        
        trend_data[symbol] = {
            '1m': change_1m,
            '5m': change_5m,
            '15m': change_15m
        }
    
    return trend_data

def update_market_data(tickers):
    """Update market data from tickers"""
    now = dt.datetime.now()
    
    for symbol, ticker in tickers.items():
        if ticker and ticker.time:
            # Get the latest price, prefer last > close > bid/ask midpoint
            last_price = None
            
            if ticker.last is not None and ticker.last > 0:
                last_price = ticker.last
            elif hasattr(ticker, 'close') and ticker.close is not None and ticker.close > 0:
                last_price = ticker.close
            elif ticker.bid is not None and ticker.ask is not None and ticker.bid > 0 and ticker.ask > 0:
                last_price = (ticker.bid + ticker.ask) / 2
            
            if last_price:
                # Store the data in market_data
                market_data[symbol]['last'] = last_price
                market_data[symbol]['bid'] = ticker.bid if ticker.bid and ticker.bid > 0 else None
                market_data[symbol]['ask'] = ticker.ask if ticker.ask and ticker.ask > 0 else None
                market_data[symbol]['timestamp'] = now.strftime("%H:%M:%S")
                
                # Add to price history for trend calculation
                if symbol in price_history:
                    price_history[symbol].append(last_price)
                
                # Calculate change from previous close
                if market_data[symbol]['last'] and previous_close[symbol]:
                    change = market_data[symbol]['last'] - previous_close[symbol]
                    change_pct = (change / previous_close[symbol]) * 100
                    market_data[symbol]['change'] = change
                    market_data[symbol]['change_pct'] = change_pct
                
                # Update volume bins for ES
                if symbol == 'ES' and hasattr(ticker, 'volume') and ticker.volume:
                    current_volume = ticker.volume
                    current_time = time.time()
                    
                    # Store total volume
                    market_data[symbol]['volume'] = current_volume
                    
                    # Initialize bin tracking if needed
                    if volume_bins[symbol]['last_volume'] is None:
                        volume_bins[symbol]['last_volume'] = current_volume
                        volume_bins[symbol]['last_bin_time'] = current_time
                        # Initialize first bin with 0 volume
                        volume_bins[symbol]['bins'].append({
                            'timestamp': now.strftime("%H:%M:%S"),
                            'volume': 0,
                            'relative': 1.0  # Baseline
                        })
                    
                    # Check if we need to create a new bin (10 seconds passed)
                    if current_time - volume_bins[symbol]['last_bin_time'] >= 10:
                        # Calculate volume in this 10-second period
                        period_volume = current_volume - volume_bins[symbol]['last_volume']
                        
                        # Calculate relative volume compared to previous bin
                        relative = 1.0  # Default if no previous data
                        if len(volume_bins[symbol]['bins']) > 0 and volume_bins[symbol]['bins'][-1]['volume'] > 0:
                            relative = period_volume / volume_bins[symbol]['bins'][-1]['volume']
                        
                        # Add the bin
                        volume_bins[symbol]['bins'].append({
                            'timestamp': now.strftime("%H:%M:%S"),
                            'volume': period_volume,
                            'relative': relative
                        })
                        
                        # Update tracking variables
                        volume_bins[symbol]['last_volume'] = current_volume
                        volume_bins[symbol]['last_bin_time'] = current_time

def calculate_option_spreads(spx_price):
    """Calculate bull spreads, bear spreads and iron condors near the current price"""
    if not spx_price or not spx_options or 'last_price' not in spx_options:
        return None
        
    # Round to nearest 5 to find our base strike
    base_strike = round(spx_price / 5) * 5
    
    # Define strike ranges for spreads
    bull_spreads = [
        {'name': f"Bull Call {base_strike+5}/{base_strike+10}", 'low': base_strike+5, 'high': base_strike+10, 'type': 'C'},
        {'name': f"Bull Call {base_strike+10}/{base_strike+15}", 'low': base_strike+10, 'high': base_strike+15, 'type': 'C'}
    ]
    
    bear_spreads = [
        {'name': f"Bear Put {base_strike-5}/{base_strike-10}", 'low': base_strike-10, 'high': base_strike-5, 'type': 'P'},
        {'name': f"Bear Put {base_strike-10}/{base_strike-15}", 'low': base_strike-15, 'high': base_strike-10, 'type': 'P'}
    ]
    
    # Iron condor (requires 4 strikes)
    iron_condor = {
        'name': f"Iron Condor {base_strike-10}/{base_strike-5}/{base_strike+5}/{base_strike+10}",
        'put_low': base_strike-10, 
        'put_high': base_strike-5,
        'call_low': base_strike+5, 
        'call_high': base_strike+10
    }
    
    # Today's expiry
    today = dt.date.today().strftime("%Y%m%d")
    
    # Collect the result
    spreads = {
        'bull_spreads': [],
        'bear_spreads': [],
        'iron_condors': {}
    }
    
    # Find bull spreads (debit spreads)
    for spread in bull_spreads:
        low_key = f"{today} {spread['low']}{spread['type']}"
        high_key = f"{today} {spread['high']}{spread['type']}"
        
        low_price = None
        high_price = None
        
        # Look for the option contracts
        for key, data in spx_options.items():
            if key == 'last_price':
                continue
                
            if 'contract' in data and data['ticker']:
                contract = data['contract']
                ticker = data['ticker']
                
                if contract.lastTradeDateOrContractMonth == today and contract.right == spread['type']:
                    if contract.strike == spread['low']:
                        if ticker.ask and ticker.ask > 0:
                            low_price = ticker.ask
                    elif contract.strike == spread['high']:
                        if ticker.bid and ticker.bid > 0:
                            high_price = ticker.bid
        
        # Calculate spread cost
        if low_price is not None and high_price is not None:
            spread_cost = round(low_price - high_price, 2)
            spreads['bull_spreads'].append({
                'name': spread['name'],
                'cost': spread_cost,
                'low_strike': spread['low'],
                'high_strike': spread['high'],
                'type': 'Call'
            })
    
    # Find bear spreads (debit spreads)
    for spread in bear_spreads:
        low_key = f"{today} {spread['low']}{spread['type']}"
        high_key = f"{today} {spread['high']}{spread['type']}"
        
        low_price = None
        high_price = None
        
        # Look for the option contracts
        for key, data in spx_options.items():
            if key == 'last_price':
                continue
                
            if 'contract' in data and data['ticker']:
                contract = data['contract']
                ticker = data['ticker']
                
                if contract.lastTradeDateOrContractMonth == today and contract.right == spread['type']:
                    if contract.strike == spread['low']:
                        if ticker.bid and ticker.bid > 0:
                            low_price = ticker.bid
                    elif contract.strike == spread['high']:
                        if ticker.ask and ticker.ask > 0:
                            high_price = ticker.ask
        
        # Calculate spread cost
        if low_price is not None and high_price is not None:
            spread_cost = round(high_price - low_price, 2)
            spreads['bear_spreads'].append({
                'name': spread['name'],
                'cost': spread_cost,
                'low_strike': spread['low'],
                'high_strike': spread['high'],
                'type': 'Put'
            })
    
    # Calculate Iron Condor (credit spread)
    put_low_price = None
    put_high_price = None
    call_low_price = None
    call_high_price = None
    
    # Look for all 4 needed options
    for key, data in spx_options.items():
        if key == 'last_price':
            continue
            
        if 'contract' in data and data['ticker']:
            contract = data['contract']
            ticker = data['ticker']
            
            if contract.lastTradeDateOrContractMonth == today:
                if contract.right == 'P':
                    if contract.strike == iron_condor['put_low']:
                        if ticker.bid and ticker.bid > 0:
                            put_low_price = ticker.bid
                    elif contract.strike == iron_condor['put_high']:
                        if ticker.ask and ticker.ask > 0:
                            put_high_price = ticker.ask
                elif contract.right == 'C':
                    if contract.strike == iron_condor['call_low']:
                        if ticker.ask and ticker.ask > 0:
                            call_low_price = ticker.ask
                    elif contract.strike == iron_condor['call_high']:
                        if ticker.bid and ticker.bid > 0:
                            call_high_price = ticker.bid
    
    # Calculate Iron Condor credit
    if put_low_price and put_high_price and call_low_price and call_high_price:
        put_credit = round(put_low_price - put_high_price, 2)
        call_credit = round(call_high_price - call_low_price, 2)
        total_credit = round(put_credit + call_credit, 2)
        
        spreads['iron_condors'] = {
            'name': iron_condor['name'],
            'credit': total_credit,
            'put_low': iron_condor['put_low'],
            'put_high': iron_condor['put_high'],
            'call_low': iron_condor['call_low'],
            'call_high': iron_condor['call_high']
        }
        
    return spreads

def clear_terminal():
    """Clear the terminal screen"""
    os.system('cls' if os.name == 'nt' else 'clear')

def display_market_data():
    """Display all market data in a clean, formatted way"""
    # Clear the terminal for a clean display
    clear_terminal()
    
    # Get current time for header
    now = dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"=== LIVE MARKET DATA [{now}] ===\n")
    
    # Create enhanced table for index/futures data that includes trend data
    headers = ["Symbol", "Last", "Bid", "Ask", "Change", "% Change", "1m Δ", "5m Δ", "15m Δ", "Volume", "Time"]
    table_data = []
    
    # Calculate trend data once
    trend_data = calculate_trend_data()
    
    for symbol in ['SPX', 'ES', 'VIX']:
        data = market_data[symbol]
        if data['last']:
            # Format price with appropriate decimals
            decimals = 2
            last = f"{data['last']:.{decimals}f}" if data['last'] else "N/A"
            bid = f"{data['bid']:.{decimals}f}" if data['bid'] is not None else "N/A"
            ask = f"{data['ask']:.{decimals}f}" if data['ask'] is not None else "N/A"
            
            # Format change with color indicators (+ for green, - for red)
            change_str = "N/A"
            change_pct_str = "N/A"
            
            if data['change'] is not None:
                change_str = f"+{data['change']:.{decimals}f}" if data['change'] > 0 else f"{data['change']:.{decimals}f}"
                change_pct_str = f"+{data['change_pct']:.2f}%" if data['change_pct'] > 0 else f"{data['change_pct']:.2f}%"
                
            # Get trend data if available
            change_1m = "N/A"
            change_5m = "N/A"
            change_15m = "N/A"
            
            if symbol in trend_data:
                td = trend_data[symbol]
                change_1m = f"+{td['1m']:.2f}" if td['1m'] and td['1m'] > 0 else f"{td['1m']:.2f}" if td['1m'] is not None else "N/A"
                change_5m = f"+{td['5m']:.2f}" if td['5m'] and td['5m'] > 0 else f"{td['5m']:.2f}" if td['5m'] is not None else "N/A"
                change_15m = f"+{td['15m']:.2f}" if td['15m'] and td['15m'] > 0 else f"{td['15m']:.2f}" if td['15m'] is not None else "N/A"
            
            # Get volume data if available (primarily for ES)
            volume_str = "N/A"
            if symbol == 'ES' and 'volume' in data and data['volume'] is not None:
                volume_str = f"{data['volume']:,}"
                if 'avg_volume' in data and data['avg_volume'] is not None:
                    volume_str += f" (Avg: {int(data['avg_volume']):,})"
            elif symbol == 'SPX':  # SPX is an index, no direct volume
                volume_str = "Index"
            elif symbol == 'VIX':  # VIX is an index
                volume_str = "Index"
            
            # Volume info
            if symbol == 'ES' and 'volume' in data and data['volume']:
                # Display volume with recent 10-second bins
                volume_str += "\n"
                # Add each 10-second bin with relative indicator
                for i, bin_data in enumerate(reversed(volume_bins[symbol]['bins'])):
                    if i >= 6:  # Limit to 6 bins
                        break
                        
                    # Create visual indicator of relative volume
                    if bin_data['relative'] < 0.7:
                        rel_indicator = "▼▼"
                    elif bin_data['relative'] < 0.9:
                        rel_indicator = "▼"
                    elif bin_data['relative'] < 1.1:
                        rel_indicator = "■"
                    elif bin_data['relative'] < 1.5:
                        rel_indicator = "▲"
                    else:
                        rel_indicator = "▲▲"
                        
                    volume_str += f"{bin_data['timestamp'][-5:]}: {int(bin_data['volume']):,} {rel_indicator}\n"
            else:
                volume_str = "Index"
            
            table_data.append([symbol, last, bid, ask, change_str, change_pct_str, 
                              change_1m, change_5m, change_15m, volume_str, data['timestamp']])
        else:
            table_data.append([symbol, "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A"])
    
    # Print the enhanced market data table
    print(tabulate(table_data, headers=headers, tablefmt="grid"))
    print()
    
    # Display SPX options data in a better format (calls and puts side by side)
    if spx_options:
        # First organize by expiration and strike
        options_by_expiry = {}
        for key, data in spx_options.items():
            if key == 'last_price':  # Skip the tracking field
                continue
                
            if 'contract' in data and data['ticker']:
                contract = data['contract']
                expiry = contract.lastTradeDateOrContractMonth
                strike = contract.strike
                right = contract.right
                
                # Create entry for this expiry if it doesn't exist
                if expiry not in options_by_expiry:
                    options_by_expiry[expiry] = {}
                
                # Create entry for this strike if it doesn't exist
                if strike not in options_by_expiry[expiry]:
                    options_by_expiry[expiry][strike] = {'C': None, 'P': None}
                
                ticker = data['ticker']
                
                # Get option price and other data
                last_price = ticker.last if ticker.last and ticker.last > 0 else None
                if not last_price and ticker.bid and ticker.ask and ticker.bid > 0 and ticker.ask > 0:
                    last_price = (ticker.bid + ticker.ask) / 2
                
                # Get volume and open interest
                volume = ticker.volume if hasattr(ticker, 'volume') else None
                open_interest = ticker.openInterest if hasattr(ticker, 'openInterest') else None
                
                # Store all the option data
                options_by_expiry[expiry][strike][right] = {
                    'ticker': ticker,
                    'last': last_price,
                    'bid': ticker.bid if ticker.bid and ticker.bid > 0 else None,
                    'ask': ticker.ask if ticker.ask and ticker.ask > 0 else None,
                    'volume': volume,
                    'open_interest': open_interest
                }
        
        # Now for each expiry, display the options
        for expiry, strikes in options_by_expiry.items():
            # Format the expiry date nicely
            expiry_date = dt.datetime.strptime(expiry, "%Y%m%d").strftime("%a, %b %d")
            days_to_expiry = (dt.datetime.strptime(expiry, "%Y%m%d").date() - dt.date.today()).days
            
            if days_to_expiry == 0:
                expiry_str = f"SPX OPTIONS: {expiry_date} (0DTE)"
            else:
                expiry_str = f"SPX OPTIONS: {expiry_date} ({days_to_expiry} days to expiry)"
                
            print(f"\n=== {expiry_str} ===")
            
            # Prepare the table headers
            option_headers = [
                "Strike", 
                "Call Bid", "Call Ask", "Call Last", "Call Vol",
                "|", 
                "Put Bid", "Put Ask", "Put Last", "Put Vol"
            ]
            
            option_data = []
            
            # Sort strikes in ascending order
            sorted_strikes = sorted(strikes.keys())
            
            # Determine ATM strike for highlighting
            atm_strike = None
            if market_data['SPX']['last']:
                atm_strike = round(market_data['SPX']['last'] / 5) * 5
            
            for strike in sorted_strikes:
                strike_data = strikes[strike]
                row = [strike]
                
                # Call data
                if 'C' in strike_data and strike_data['C']:
                    call = strike_data['C']
                    row.extend([
                        f"{call['bid']:.2f}" if call['bid'] else "N/A",
                        f"{call['ask']:.2f}" if call['ask'] else "N/A",
                        f"{call['last']:.2f}" if call['last'] else "N/A",
                        call['volume'] if call['volume'] else "N/A"
                    ])
                else:
                    row.extend(["N/A", "N/A", "N/A", "N/A"])
                
                # Separator
                row.append("|")
                
                # Put data
                if 'P' in strike_data and strike_data['P']:
                    put = strike_data['P']
                    row.extend([
                        f"{put['bid']:.2f}" if put['bid'] else "N/A",
                        f"{put['ask']:.2f}" if put['ask'] else "N/A",
                        f"{put['last']:.2f}" if put['last'] else "N/A",
                        put['volume'] if put['volume'] else "N/A"
                    ])
                else:
                    row.extend(["N/A", "N/A", "N/A", "N/A"])
                
                option_data.append(row)
            
            # Print the options table
            print(tabulate(option_data, headers=option_headers, tablefmt="grid"))
            print("\nNote: Min order size for SPX options is 1 contract")
    
    # Display option spread strategies
    if market_data['SPX']['last'] is not None:
        spreads = calculate_option_spreads(market_data['SPX']['last'])
        
        if spreads:
            print("\n=== OPTION SPREADS (0DTE) ===")
            
            # Display bull and bear spreads side by side
            bull_headers = ["Bull Strategy", "Strikes", "Cost ($)"]
            bear_headers = ["Bear Strategy", "Strikes", "Cost ($)"]
            
            # Prepare bull spread data
            bull_data = []
            for spread in spreads.get('bull_spreads', []):
                bull_data.append([
                    "Bull Call Spread",
                    f"{spread['low_strike']}/{spread['high_strike']}",
                    f"{spread['cost']:.2f}"
                ])
                
            # Fill with empty rows if needed
            while len(bull_data) < 2:
                bull_data.append(["N/A", "N/A", "N/A"])
                
            # Prepare bear spread data
            bear_data = []
            for spread in spreads.get('bear_spreads', []):
                bear_data.append([
                    "Bear Put Spread",
                    f"{spread['high_strike']}/{spread['low_strike']}",
                    f"{spread['cost']:.2f}"
                ])
                
            # Fill with empty rows if needed
            while len(bear_data) < 2:
                bear_data.append(["N/A", "N/A", "N/A"])
                
            # Combine data into side-by-side table
            combined_headers = bull_headers + [""] + bear_headers
            combined_data = []
            
            for i in range(max(len(bull_data), len(bear_data))):
                row = []
                if i < len(bull_data):
                    row.extend(bull_data[i])
                else:
                    row.extend(["", "", ""])
                    
                row.append("")  # Spacer column
                
                if i < len(bear_data):
                    row.extend(bear_data[i])
                else:
                    row.extend(["", "", ""])
                    
                combined_data.append(row)
                
            print(tabulate(combined_data, headers=combined_headers, tablefmt="grid"))
            print()
            
            # Display Iron Condor
            if spreads.get('iron_condors'):
                ic = spreads['iron_condors']
                ic_headers = ["Strategy", "Put Legs", "Call Legs", "Credit ($)"]
                ic_data = [[
                    "Iron Condor",
                    f"{ic['put_low']}/{ic['put_high']}",
                    f"{ic['call_low']}/{ic['call_high']}",
                    f"{ic['credit']:.2f}"
                ]]
                
                print("Iron Condor Strategy:")
                print(tabulate(ic_data, headers=ic_headers, tablefmt="grid"))
            
    # Print risk-free rate
    rf_rate = market_data['RISK_FREE']['rate']
    rf_time = market_data['RISK_FREE']['timestamp']
    
    if rf_rate is not None:
        print(f"Risk-Free Rate: {rf_rate*100:.3f}% (13-Week T-Bill)")
    else:
        print("Risk-Free Rate: N/A")
    
    if rf_time:
        print(f"Rate last updated: {rf_time}")
    
    print("\nPress Ctrl+C to exit")

def main():
    """Main function"""
    global contracts  # Make contracts accessible to other functions
    
    args = parse_arguments()
    
    # Set logging level
    if args.debug:
        logger.setLevel(logging.DEBUG)
    
    logger.info("=== Live Market Data Monitor ===")
    
    # Connect to IB Gateway
    ib = connect_to_ib(args.ip, args.port, args.client_id)
    if not ib:
        logger.error("Exiting due to connection failure")
        return 1
    
    try:
        # Request delayed data - may help with futures data
        ib.reqMarketDataType(3)  # 3 = Delayed data
        
        # Set up contracts
        contracts = setup_contracts(ib)
        if not contracts:
            logger.error("Failed to set up contracts")
            return 1
        
        # Subscribe to market data
        tickers = setup_market_data(ib, contracts)
        
        # Wait for initial data
        logger.info("Waiting for initial data...")
        time.sleep(3)
        
        # Main update loop
        logger.info(f"Starting data display with {args.refresh}s refresh rate")
        update_counter = 0
        
        while True:
            # Process IB messages
            ib.sleep(0)  # Process any pending messages
            
            # Update our market data
            update_market_data(tickers)
            
            # Update option data every 30 seconds
            if update_counter % 30 == 0:
                update_option_data(ib)
            
            # Display the data
            display_market_data()
            
            # Wait for next update
            time.sleep(args.refresh)
            update_counter += 1
            
    except KeyboardInterrupt:
        logger.info("Monitoring stopped by user")
    except Exception as e:
        logger.error(f"Error in main loop: {str(e)}")
    finally:
        # Cancel market data subscriptions and disconnect
        if ib and ib.isConnected():
            logger.info("Disconnecting from IB Gateway")
            ib.disconnect()
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 