import pyodbc
import datetime as dt
import os
import logging
import dotenv
from pathlib import Path

logger = logging.getLogger(__name__)

# Load environment variables
env_path = Path(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
dotenv.load_dotenv(env_path / '.env')

class MSSQLDatabaseHandler:
    def __init__(self):
        """Initialize connection to SQL Server using environment variables"""
        self.db_ip = os.getenv('DB_IP')
        self.db_user = os.getenv('DB_USER')
        self.db_password = os.getenv('DB_PASSWORD')
        self.db_name = os.getenv('DB_NAME')
        
        self.conn = None
        self.cursor = None
        self.connect()
        self.create_tables()
        logger.info(f"MSSQL Database initialized on {self.db_ip}")
        
    def connect(self):
        """Create connection to SQL Server"""
        try:
            conn_str = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={self.db_ip};"
                f"DATABASE={self.db_name};"
                f"UID={self.db_user};"
                f"PWD={self.db_password};"
                f"TrustServerCertificate=yes;"
            )
            self.conn = pyodbc.connect(conn_str)
            self.cursor = self.conn.cursor()
            logger.info(f"Successfully connected to SQL Server at {self.db_ip}")
        except Exception as e:
            logger.error(f"Error connecting to SQL Server: {str(e)}")
            # Fall back to SQLite if connection fails
            raise
        
    def create_tables(self):
        """Verify tables exist"""
        try:
            # Just verify the table exists rather than creating it
            self.cursor.execute('''
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='executed_trades' AND xtype='U')
            BEGIN
                RAISERROR('Table executed_trades does not exist!', 16, 1)
            END
            ''')
            self.conn.commit()
            logger.info("Verified executed_trades table exists")
        except Exception as e:
            logger.error(f"Error verifying tables: {str(e)}")
    
    def record_trade(self, trade_data):
        """Record a new trade in the database"""
        try:
            # Filter out any None or empty values
            update_data = {k: v for k, v in trade_data.items() if v is not None and v != ''}
            
            # Escape column names (especially 'right' which is a reserved keyword)
            columns = [f"[{col}]" if col == 'right' else col for col in update_data.keys()]
            
            # Create placeholders for the values
            placeholders = ', '.join(['?' for _ in update_data])
            columns_str = ', '.join(columns)
            
            # Create the SQL query - use executed_trades instead of trades
            sql = f"INSERT INTO executed_trades ({columns_str}) OUTPUT INSERTED.id VALUES ({placeholders})"
            
            # Execute the query and get the inserted ID
            self.cursor.execute(sql, list(update_data.values()))
            inserted_id = self.cursor.fetchone()[0]
            self.conn.commit()
            
            logger.debug(f"Trade recorded with ID: {inserted_id}")
            return inserted_id
        except Exception as e:
            logger.error(f"Error recording trade: {str(e)}")
            return None
        
    def update_trade(self, trade_id, update_data):
        """Update an existing trade"""
        try:
            # Create SET clause with column=? format
            set_clause = ', '.join([f"[{col}]=?" if col == 'right' else f"{col}=?" for col in update_data.keys()])
            
            # Add values and the ID for the WHERE clause
            values = list(update_data.values()) + [trade_id]
            
            # Create the SQL query - use executed_trades instead of trades
            sql = f"UPDATE executed_trades SET {set_clause} WHERE id = ?"
            
            # Execute the query
            self.cursor.execute(sql, values)
            self.conn.commit()
            
            logger.debug(f"Trade {trade_id} updated with {update_data}")
            return True
        except Exception as e:
            logger.error(f"Error updating trade: {str(e)}")
            return False
        
    def get_open_trades(self):
        """Get all open trades"""
        try:
            # Use executed_trades instead of trades
            sql = "SELECT * FROM executed_trades WHERE status = 'open'"
            self.cursor.execute(sql)
            
            # Convert rows to dictionaries
            columns = [column[0] for column in self.cursor.description]
            trades = []
            for row in self.cursor.fetchall():
                trades.append(dict(zip(columns, row)))
                
            logger.debug(f"Retrieved {len(trades)} open trades from database")
            return trades
        except Exception as e:
            logger.error(f"Error getting open trades: {str(e)}")
            return []
    
    def get_trade(self, trade_id):
        """Get a specific trade by ID"""
        try:
            # Use executed_trades instead of trades
            sql = "SELECT * FROM executed_trades WHERE id = ?"
            self.cursor.execute(sql, (trade_id,))
            
            # Convert row to dictionary
            row = self.cursor.fetchone()
            if row:
                columns = [column[0] for column in self.cursor.description]
                trade = dict(zip(columns, row))
                return trade
            else:
                logger.warning(f"Trade ID {trade_id} not found")
                return None
        except Exception as e:
            logger.error(f"Error getting trade: {str(e)}")
            return None
    
    def get_trades_by_date(self, start_date, end_date=None):
        """Get trades within a date range"""
        try:
            if end_date is None:
                end_date = dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            sql = "SELECT * FROM executed_trades WHERE entry_time BETWEEN ? AND ? ORDER BY entry_time DESC"
            self.cursor.execute(sql, [start_date, end_date])
            
            # Convert rows to dictionaries
            columns = [column[0] for column in self.cursor.description]
            trades = []
            for row in self.cursor.fetchall():
                trades.append(dict(zip(columns, row)))
                
            return trades
        except Exception as e:
            logger.error(f"Error getting trades by date: {str(e)}")
            return []
    
    def close(self):
        """Close the database connection"""
        if self.conn:
            self.conn.close()
            logger.info("Database connection closed") 