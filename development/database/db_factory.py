import os
import logging
from pathlib import Path
import dotenv
from .db_handler import DatabaseHandler

logger = logging.getLogger(__name__)

# Try to import MSSQL handler
try:
    from .mssql_handler import MSSQLDatabaseHandler
    HAS_MSSQL = True
except ImportError:
    logger.warning("MSSQL dependencies not installed. Install pyodbc for SQL Server support.")
    HAS_MSSQL = False

def get_database_handler(use_remote=True, db_path=None):
    """
    Factory function to get the appropriate database handler
    
    Args:
        use_remote: Whether to use remote SQL Server (True) or local SQLite (False)
        db_path: Path to SQLite database file if local database is used
        
    Returns:
        A database handler instance
    """
    # Try to load environment variables to check for remote DB credentials
    env_path = Path(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    dotenv.load_dotenv(env_path / '.env')
    
    if use_remote and HAS_MSSQL and os.getenv('DB_IP'):
        # Use SQL Server if credentials are available
        try:
            return MSSQLDatabaseHandler()
        except Exception as e:
            logger.error(f"Failed to connect to SQL Server: {str(e)}")
            logger.info("Falling back to SQLite database")
    
    # Fall back to SQLite
    if db_path is None:
        db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'trades.db')
        
    logger.info(f"Using SQLite database at {db_path}")
    return DatabaseHandler(db_path) 