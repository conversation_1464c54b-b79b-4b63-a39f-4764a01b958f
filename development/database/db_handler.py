import sqlite3
import datetime as dt
import os
import logging

logger = logging.getLogger(__name__)

class DatabaseHandler:
    def __init__(self, db_path="trades.db"):
        # Ensure directory exists
        os.makedirs(os.path.dirname(os.path.abspath(db_path)), exist_ok=True)
        
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path)
        # Enable dictionary access to rows
        self.conn.row_factory = sqlite3.Row
        self.cursor = self.conn.cursor()
        self.create_tables()
        logger.info(f"Database initialized at {db_path}")
        
    def create_tables(self):
        """Create tables if they don't exist"""
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS trades (
            id INTEGER PRIMARY KEY,
            account_id TEXT,
            account_name TEXT,
            contract_id INTEGER,
            symbol TEXT,
            expiry TEXT,
            strike REAL,
            right TEXT,
            order_type TEXT,
            quantity INTEGER,
            entry_price REAL,
            exit_price REAL,
            entry_time TIMESTAMP,
            exit_time TIMESTAMP,
            status TEXT,
            pnl REAL,
            commission REAL,
            order_id INTEGER,
            exit_order_id INTEGER,
            ib_execution_id TEXT,
            notes TEXT
        )
        ''')
        self.conn.commit()
        logger.debug("Database tables created or verified")
    
    def record_trade(self, trade_data):
        """Record a new trade in the database"""
        columns = ', '.join(trade_data.keys())
        placeholders = ', '.join(['?' for _ in trade_data])
        values = list(trade_data.values())
        
        self.cursor.execute(
            f"INSERT INTO trades ({columns}) VALUES ({placeholders})",
            values
        )
        self.conn.commit()
        trade_id = self.cursor.lastrowid
        logger.info(f"Recorded trade ID {trade_id} for {trade_data.get('symbol', 'unknown')}")
        return trade_id
        
    def update_trade(self, trade_id, update_data):
        """Update an existing trade record"""
        set_clause = ', '.join([f"{k} = ?" for k in update_data.keys()])
        values = list(update_data.values()) + [trade_id]
        
        self.cursor.execute(
            f"UPDATE trades SET {set_clause} WHERE id = ?",
            values
        )
        self.conn.commit()
        logger.info(f"Updated trade ID {trade_id} with new data")
        
    def get_open_trades(self):
        """Get all open trades"""
        self.cursor.execute("SELECT * FROM trades WHERE status = 'open'")
        trades = [dict(row) for row in self.cursor.fetchall()]
        logger.debug(f"Retrieved {len(trades)} open trades from database")
        return trades
    
    def get_trade(self, trade_id):
        """Get a specific trade by ID"""
        self.cursor.execute("SELECT * FROM trades WHERE id = ?", (trade_id,))
        trade = self.cursor.fetchone()
        if trade:
            return dict(trade)
        return None
    
    def get_trades_by_date(self, start_date, end_date=None):
        """Get trades within a date range"""
        if end_date is None:
            end_date = dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        self.cursor.execute(
            "SELECT * FROM trades WHERE entry_time BETWEEN ? AND ? ORDER BY entry_time DESC",
            (start_date, end_date)
        )
        trades = [dict(row) for row in self.cursor.fetchall()]
        return trades
    
    def close(self):
        """Close the database connection"""
        if self.conn:
            self.conn.close()
            logger.info("Database connection closed") 