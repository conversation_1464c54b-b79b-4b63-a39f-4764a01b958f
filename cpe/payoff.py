import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Utility functions for option payoffs
def call_payoff(strike, price):
    return np.maximum(price - strike, 0)
def put_payoff(strike, price):
    return np.maximum(strike - price, 0)

# Chart params
prices = np.linspace(5900, 5955, 200)

# ========== 1. Bull Call Spread
def bull_call_spread(prices, buy_strike, sell_strike, debit):
    return call_payoff(buy_strike, prices) - call_payoff(sell_strike, prices) - debit

# Example: Buy 5935, Sell 5940
debit1 = 2  # change as needed (simulated price)
bull_call = bull_call_spread(prices, 5935, 5940, debit1)

# ========== 2. ITM Bull Call Spread
debit2 = 4  # change as needed
itmbull_call = bull_call_spread(prices, 5930, 5935, debit2)

# ========== 3. Bear Put Spread
def bear_put_spread(prices, buy_strike, sell_strike, debit):
    return put_payoff(buy_strike, prices) - put_payoff(sell_strike, prices) - debit

debit3 = 2 # change as needed
bear_put = bear_put_spread(prices, 5930, 5925, debit3)

# ========== 4. ITM Bear Put Spread
debit4 = 4  # change as needed
itmbear_put = bear_put_spread(prices, 5935, 5930, debit4)

# ========== 5. Iron Butterfly
def iron_butterfly(prices, atm_strike, wing_width, credit):
    buyC = call_payoff(atm_strike + wing_width, prices)
    sellC = -call_payoff(atm_strike, prices)
    sellP = -put_payoff(atm_strike, prices)
    buyP = put_payoff(atm_strike - wing_width, prices)
    return buyC + sellC + sellP + buyP + credit

credit5 = 3  # change as needed
ironfly = iron_butterfly(prices, 5935, 5, credit5)

# ========== 6. Wide Iron Butterfly
credit6 = 3.5  # change as needed
wide_ironfly = iron_butterfly(prices, 5935, 10, credit6)

# ========== 7. Iron Condor
def iron_condor(prices, c_short, c_long, p_short, p_long, credit):
    call_side = -call_payoff(c_short, prices) + call_payoff(c_long, prices)
    put_side  = -put_payoff(p_short, prices) + put_payoff(p_long, prices)
    return call_side + put_side + credit

credit7 = 2  # change as needed
ironcondor = iron_condor(prices, 5935, 5940, 5930, 5925, credit7)

# ========== 8. Wide Iron Condor
credit8 = 2.5         # change as needed
wide_ironcondor = iron_condor(prices, 5935, 5940, 5925, 5920, credit8)

# =========================================
# PLOTTING

strategies = [
    ("Bull Call Spread (5935/5940)", bull_call),
    ("ITM Bull Call Spread (5930/5935)", itmbull_call),
    ("Bear Put Spread (5930/5925)", bear_put),
    ("ITM Bear Put Spread (5935/5930)", itmbear_put),
    ("Iron Butterfly (5935±5)", ironfly),
    ("Wide Iron Butterfly (5935±10)", wide_ironfly),
    ("Iron Condor (5930/5935/5940/5925)", ironcondor),
    ("Wide Iron Condor (5930/5935/5945/5920)", wide_ironcondor),
]
for name, payoff in strategies:
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=prices, y=payoff, mode='lines', line=dict(width=3), name=name))
    fig.add_hline(y=0, line=dict(color='black', dash='dot'))
    fig.update_layout(
        title=name,
        xaxis_title="SPX price at Expiry",
        yaxis_title="P/L ($)",
        template="plotly_white",
        width=700,
        height=400,
    )
    fig.show()