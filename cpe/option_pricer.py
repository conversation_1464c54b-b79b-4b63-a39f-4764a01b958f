import math
import pandas as pd
from scipy.stats import norm
import matplotlib.pyplot as plt

def black_scholes_price(S, K, T, r, sigma, option_type):
    """
    Black-Scholes price for European options.
    S: spot price
    K: strike price
    T: time to expiry (in years)
    r: risk-free rate
    sigma: volatility (annualized, e.g. 0.2 for 20%)
    option_type: 'call' or 'put'
    """
    if T <= 0:
        # At expiry, option is worth intrinsic value
        if option_type == 'call':
            return max(0, S - K)
        else:
            return max(0, K - S)
    d1 = (math.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * math.sqrt(T))
    d2 = d1 - sigma * math.sqrt(T)
    if option_type == 'call':
        price = S * norm.cdf(d1) - K * math.exp(-r * T) * norm.cdf(d2)
    else:
        price = K * math.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
    return price

def price_vertical_spread(
    S,
    K1,
    K2,
    option_type,
    position1,
    position2,
    T=8.5,  # 8.5 minutes to close
    sigma=0.20, # 20% volatility
    r=0.02 # 2% risk-free rate
):
    """
    Price a generic vertical spread (bull or bear, call or put).
    S: spot price
    K1: strike price of first leg
    K2: strike price of second leg
    option_type: 'call' or 'put'
    position1: 'long' or 'short' for first leg (K1)
    position2: 'long' or 'short' for second leg (K2)
    T: time to expiry (in years)
    sigma: volatility (default 20%)
    r: risk-free rate (default 0)
    Returns: net price of the spread (positive = debit, negative = credit)
    """

    # convert T to minutes
    T=T/(60*24*365.25)  # T minutes to close, 24 hours per day, 365.25 days/year


    price1 = black_scholes_price(S, K1, T, r, sigma, option_type)
    price2 = black_scholes_price(S, K2, T, r, sigma, option_type)
    net = 0
    net += price1 if position1 == 'long' else -price1
    net += price2 if position2 == 'long' else -price2
    return net


if __name__ == "__main__":

    df = pd.DataFrame(columns=['T', 'price'])

    T = 8.5 # minutes to expiry
    S = 6007 # spot price
    K1 = 6005 # strike price of first leg
    K2 = 6010 # strike price of second leg

    while T >= 0:
        price = price_vertical_spread(S, K1, K2, 'call', 'long', 'short', T)
        df = pd.concat([df, pd.DataFrame([{'T': T, 'price': price}])], ignore_index=True)
        T -= 0.5

    print(df)

    # Plot T vs result
    plt.figure(figsize=(8, 5))
    plt.plot(df['T'], df['price'], marker='o')
    plt.xlabel('Minutes to Expiry (T)')
    plt.ylabel('Spread Price')
    plt.title('Bull Call Spread Price vs. Time to Expiry')
    plt.grid(True)
    plt.gca().invert_xaxis()  # Reverse the x-axis
    plt.show()
