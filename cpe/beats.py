import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import math
from tabulate import tabulate

from database import MSSQLDatabaseHandler

BULL = 'BULL'
BEAR = 'BEAR'
STRADDLE = 'STRADDLE'

CUTOFF_TIME = '15:49:00'
UPPER_CUTOFF = 0.88
LOWER_CUTOFF = 0.12

DB = MSSQLDatabaseHandler()


def get_SPX():
    SPX = DB.get_data('SPX')

    # Filter records where time component of date is >= '13:45:00'
    SPX = SPX[SPX['date'].dt.time >= pd.Timestamp(CUTOFF_TIME).time()]
    
    # Split date into date and time columns
    SPX['time'] = SPX['date'].dt.time
    SPX['date'] = SPX['date'].dt.date
    
    SPX.set_index('id', inplace=True)
    
    # Reorder columns
    SPX = SPX[['date', 'time', 'open', 'high', 'low', 'close']]
    
    return SPX

def resample_SPX(SPX):
    """Resample SPX data into 30 second OHLC bars"""
    # Create a datetime index for resampling
    SPX_temp = SPX.copy()
    SPX_temp['datetime'] = pd.to_datetime(SPX_temp['date'].astype(str) + ' ' + SPX_temp['time'].astype(str))
    SPX_temp.set_index('datetime', inplace=True)
    
    # Resample to 30s using OHLC aggregation
    resampled = SPX_temp.resample('30s').agg({
        'open': 'first',
        'high': 'max', 
        'low': 'min',
        'close': 'last',
    })
    
    # Reset index and split datetime back into date and time
    resampled.reset_index(inplace=True)
    resampled['date'] = resampled['datetime'].dt.date
    resampled['time'] = resampled['datetime'].dt.time
    resampled.drop('datetime', axis=1, inplace=True)
    
    # Reorder columns to match original format
    resampled = resampled[['date', 'time', 'open', 'high', 'low', 'close']]
    
    return resampled

def calculate_spans(df: pd.DataFrame) -> pd.DataFrame:
    """Calculate spans for a given dataframe"""
    df['span'] = df['high'] - df['low']
    return df


def daily_SPX(SPX):
    """Aggregate SPX data into daily OHLC and combine top/bottom signals"""
    # Group by date and aggregate OHLC
    daily = SPX.groupby('date').agg({
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
    }).reset_index()

    daily.dropna(inplace=True)
    
    return daily


def main():
    SPX_OG = get_SPX()
    SPX_30s = resample_SPX(SPX_OG)
    SPX = daily_SPX(SPX_OG)


    breakpoint()

    return


if __name__ == "__main__":
    test_db = DB.test_connection()

    if test_db:
        print('Database connection successful')
        main()
    else:
        print('Database connection failed')

    DB.close()
